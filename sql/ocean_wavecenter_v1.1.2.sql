-- MySQL dump 10.13  Distrib 5.7.19, for linux-glibc2.12 (x86_64)
--
-- Host: localhost    Database: ocean_wavecenter
-- ------------------------------------------------------
-- Server version	5.7.19-log

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `activity_announcement_deploy_record`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `activity_announcement_deploy_record` (
  `id` bigint(20) NOT NULL,
  `activity_id` bigint(20) NOT NULL COMMENT '活动ID',
  `app_id` int(10) NOT NULL COMMENT '应用ID',
  `nj_id` bigint(10) NOT NULL COMMENT '厅主ID，冗余，部分业务会使用到',
  `original_announcement` varchar(4000) NOT NULL DEFAULT '' COMMENT '原始房间公告',
  `original_announcement_img_url` varchar(100) NOT NULL DEFAULT '' COMMENT '原始房间公告url',
  `announcement` varchar(600) NOT NULL DEFAULT '' COMMENT '要配置的房间公告',
  `announcement_img_url` varchar(100) NOT NULL DEFAULT '' COMMENT '要配置的房间公告图片',
  `try_count` int(10) NOT NULL DEFAULT '0' COMMENT '重试次数',
  `status` tinyint(4) NOT NULL COMMENT '状态，0：待设置，1：设置失败，2：设置成功，3：恢复失败，4：恢复成功',
  `start_time` datetime NOT NULL COMMENT '活动开始时间',
  `end_time` datetime NOT NULL COMMENT '活动结束时间',
  `deploy_env` varchar(20) NOT NULL COMMENT '可选值  TEST/PRE/PRO',
  `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_activityId` (`activity_id`) USING BTREE COMMENT '活动ID',
  KEY `idx_time` (`start_time`) USING BTREE COMMENT '开始时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `activity_apply_flow_resource`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `activity_apply_flow_resource` (
  `id` bigint(20) NOT NULL,
  `activity_id` bigint(20) NOT NULL COMMENT '活动ID',
  `resource_config_id` bigint(20) NOT NULL COMMENT '资源配置ID',
  `image_url` varchar(100) NOT NULL DEFAULT '' COMMENT '图片url',
  `status` tinyint(4) NOT NULL COMMENT '状态，0：待审核，1：不发放，2：可发放',
  `extra` varchar(255) NOT NULL DEFAULT '' COMMENT '扩展配置，如果官频，需要配置展示的开始结束时间',
  PRIMARY KEY (`id`),
  KEY `idx_activity_id` (`activity_id`) USING BTREE COMMENT '活动ID'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `activity_apply_info`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `activity_apply_info` (
  `id` bigint(20) NOT NULL COMMENT '主键ID',
  `app_id` int(10) NOT NULL COMMENT '应用ID',
  `name` varchar(30) NOT NULL COMMENT '业务定义不超过10个字，稍微冗余',
  `nj_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '提报厅厅主ID',
  `family_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '家族ID',
  `class_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '活动分类ID',
  `start_time` datetime NOT NULL COMMENT '活动开始时间',
  `end_time` datetime NOT NULL COMMENT '活动结束时间',
  `audit_status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '活动状态，1：待审批，2：审批通过，3：审批不通过',
  `apply_type` tinyint(4) NOT NULL COMMENT '申请类型，1：自主提报，2: 官方活动',
  `audit_reason` varchar(255) NOT NULL DEFAULT '' COMMENT '审核原因',
  `contact` varchar(40) NOT NULL DEFAULT '0' COMMENT '联系人',
  `applicant_uid` bigint(20) NOT NULL COMMENT '申请者uid',
  `contact_number` varchar(60) NOT NULL DEFAULT '' COMMENT '联系方式',
  `host_id` bigint(20) NOT NULL DEFAULT '0',
  `accompany_nj_ids` varchar(300) NOT NULL DEFAULT '' COMMENT '陪档主播ID列表',
  `goal` varchar(200) NOT NULL DEFAULT '' COMMENT '活动目标，不超过100字',
  `introduction` varchar(200) NOT NULL DEFAULT '' COMMENT '活动介绍，不超过100字',
  `auxiliary_prop_url` varchar(1000) NOT NULL DEFAULT '' COMMENT '活动道具图片，多个逗号分隔，斜杠开头',
  `poster_url` varchar(100) NOT NULL DEFAULT '' COMMENT '活动海报图片地址',
  `activity_tool` varchar(20) NOT NULL DEFAULT '' COMMENT '玩法工具，1:魔法团战，2：跨房PK，3：投票，4：全麦PK，多个逗号分隔',
  `room_announcement` varchar(600) NOT NULL DEFAULT '' COMMENT '房间公告，不超过500字',
  `room_announcement_img_url` varchar(300) NOT NULL DEFAULT '' COMMENT '房间公告图片，不超过3个',
  `room_background_id` bigint(20) NOT NULL COMMENT '房间背景ID',
  `avatar_widget_id` bigint(20) NOT NULL COMMENT '头像框ID',
  `deploy_env` varchar(10) NOT NULL COMMENT '可选值  TEST/PRE/PRO',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除，默认不删除',
  `audit_operator` varchar(40) NOT NULL DEFAULT '' COMMENT '审批操作人',
  `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_name` (`name`) USING BTREE COMMENT '昵称做索引，需要支持模糊查询',
  KEY `idx_njId` (`nj_id`) USING BTREE COMMENT '房间ID',
  KEY `idx_startTime` (`start_time`) USING BTREE COMMENT '活动开始时间，方便查询生效活动'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='活动申请基础信息';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `activity_apply_process`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `activity_apply_process` (
  `id` bigint(20) NOT NULL,
  `activity_id` bigint(20) NOT NULL,
  `name` varchar(255) NOT NULL DEFAULT '',
  `duration` varchar(255) NOT NULL DEFAULT '' COMMENT '时长',
  `explanation` varchar(500) NOT NULL DEFAULT '' COMMENT '说明',
  `index` int(10) NOT NULL DEFAULT '0' COMMENT '步骤',
  PRIMARY KEY (`id`),
  KEY `idx_activityId` (`activity_id`) USING BTREE COMMENT '活动ID'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='活动申请-流程表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `activity_big_class`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `activity_big_class` (
  `id` bigint(20) NOT NULL,
  `name` varchar(60) NOT NULL DEFAULT '' COMMENT '类型名称',
  `app_id` int(10) NOT NULL COMMENT '应用ID',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否逻辑删除',
  `deploy_env` varchar(10) NOT NULL DEFAULT 'TEST' COMMENT '环境隔离 TEST/PRE/PRO',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `operator` varchar(20) NOT NULL DEFAULT '' COMMENT '操作人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uq_deploy_env_app_id_name` (`deploy_env`,`app_id`,`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='活动大类表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `activity_class_config`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `activity_class_config` (
  `id` bigint(20) NOT NULL,
  `app_id` int(10) NOT NULL COMMENT '应用ID',
  `big_class_id` bigint(20) NOT NULL COMMENT '主类型ID',
  `name` varchar(60) NOT NULL DEFAULT '' COMMENT '类型名称',
  `level_id` bigint(20) NOT NULL COMMENT '等级ID',
  `deploy_env` varchar(10) NOT NULL DEFAULT 'TEST' COMMENT '环境隔离 TEST/PRE/PRO',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '逻辑删除',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `operator` varchar(20) NOT NULL DEFAULT '' COMMENT '操作人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uq_big_class_id_name` (`big_class_id`,`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='活动分类配置表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `activity_dress_up_give_record`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `activity_dress_up_give_record` (
  `id` bigint(20) NOT NULL,
  `give_id` bigint(20) NOT NULL COMMENT '发放记录ID',
  `user_id` bigint(20) NOT NULL COMMENT '获得装扮的用户ID',
  `type` tinyint(4) NOT NULL COMMENT '装扮类型，1：背景，2：头像框',
  `dress_up_id` bigint(20) NOT NULL COMMENT '装扮ID',
  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0:待发放，1：发放失败，2：发放成功',
  PRIMARY KEY (`id`),
  KEY `idx_giveId` (`give_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='装扮资源发放记录';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `activity_flow_resource_give_record`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `activity_flow_resource_give_record` (
  `id` bigint(20) NOT NULL,
  `give_id` bigint(20) NOT NULL COMMENT '配置记录ID',
  `image_url` varchar(100) NOT NULL COMMENT '流量资源ID',
  `user_id` bigint(20) NOT NULL COMMENT '获得流量的用户ID',
  `resource_config_id` bigint(20) NOT NULL COMMENT '资源配置ID',
  `resource_code` varchar(20) NOT NULL DEFAULT '' COMMENT '资源code码',
  `deploy_type` tinyint(4) NOT NULL COMMENT '资源配置类型，1：自动，2：手动',
  `resource_name` varchar(20) NOT NULL COMMENT '资源名称',
  `status` tinyint(4) NOT NULL COMMENT '0:待发放，1：发放失败，2：发放成功',
  `extra` varchar(255) DEFAULT NULL COMMENT '扩展字段',
  PRIMARY KEY (`id`),
  KEY `idx_give_id` (`give_id`) USING BTREE COMMENT '资源发放ID'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='流量资源发放记录';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `activity_image_fodder`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `activity_image_fodder` (
  `id` bigint(20) NOT NULL,
  `app_id` int(11) NOT NULL COMMENT '应用ID',
  `name` varchar(20) NOT NULL COMMENT '素材名称',
  `type` tinyint(4) NOT NULL COMMENT '素材分类，参考枚举值',
  `scale` varchar(20) NOT NULL DEFAULT '' COMMENT '宽高比',
  `color` varchar(20) NOT NULL DEFAULT '' COMMENT '素材颜色，包含#',
  `image_url` varchar(100) NOT NULL DEFAULT '' COMMENT '素材图片地址，斜杠开头',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除',
  `deploy_env` varchar(10) NOT NULL DEFAULT 'TEST' COMMENT '环境隔离 TEST/PRE/PRO',
  `operator` varchar(20) NOT NULL DEFAULT '' COMMENT '操作人',
  `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_name` (`name`) USING BTREE COMMENT '会根据名称模糊查询'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='活动图片素材表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `activity_level_config`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `activity_level_config` (
  `id` bigint(20) NOT NULL,
  `app_id` int(10) NOT NULL COMMENT '应用ID',
  `level` varchar(10) NOT NULL DEFAULT '' COMMENT '等级名称',
  `operator` varchar(20) NOT NULL DEFAULT '' COMMENT '操作者',
  `deploy_env` varchar(10) NOT NULL DEFAULT 'TEST' COMMENT '环境隔离 TEST/PRE/PRO',
  `deleted` tinyint(1) NOT NULL COMMENT '是否逻辑删除',
  `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='活动等级配置表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `activity_official_seat_time`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `activity_official_seat_time` (
  `id` bigint(20) NOT NULL,
  `app_id` int(10) NOT NULL,
  `seat` int(10) NOT NULL COMMENT '官频位',
  `show_date` datetime NOT NULL COMMENT '日期',
  `start_time` datetime NOT NULL COMMENT '官频位展示开始时间',
  `end_time` datetime NOT NULL COMMENT '官频位展示结束时间',
  `count` int(11) NOT NULL COMMENT '档期官频位厅数量',
  `deploy_env` varchar(20) NOT NULL COMMENT '可选值  TEST/PRE/PRO',
  PRIMARY KEY (`id`),
  KEY `idx_time` (`show_date`) USING BTREE COMMENT '日期',
  KEY `idx_showTime` (`start_time`) USING BTREE COMMENT '展示时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='管频位厅数量记录';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `activity_recommend_card_config`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `activity_recommend_card_config` (
  `id` bigint(20) NOT NULL,
  `app_id` int(10) NOT NULL COMMENT '应用id',
  `level_id` bigint(20) NOT NULL COMMENT '等级ID',
  `count` int(10) NOT NULL DEFAULT '0' COMMENT '发放张数',
  `valid_day` int(10) NOT NULL DEFAULT '0' COMMENT '有效天数',
  `deploy_env` varchar(10) NOT NULL DEFAULT 'TEST' COMMENT '环境隔离 TEST/PRE/PRO',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除',
  `operator` varchar(20) NOT NULL DEFAULT '' COMMENT '操作者',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_levelId` (`level_id`,`app_id`) USING BTREE COMMENT '一个应用一个等级只能有一张'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='活动推荐卡配置';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `activity_report_data_detail`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `activity_report_data_detail` (
  `id` bigint(20) NOT NULL COMMENT 'ID',
  `activity_id` bigint(20) NOT NULL COMMENT '活动ID',
  `nj_id` bigint(20) NOT NULL COMMENT '提报厅厅主ID',
  `family_id` bigint(20) NOT NULL COMMENT '家族ID',
  `start_time` datetime NOT NULL COMMENT '活动开始时间',
  `end_time` datetime NOT NULL COMMENT '活动结束时间',
  `depart_start_time` datetime NOT NULL COMMENT '5分钟开始时间',
  `depart_end_time` datetime NOT NULL COMMENT '5分钟结束时间',
  `enter_room_user_cnt` bigint(20) NOT NULL DEFAULT '0' COMMENT '进房人数',
  `enter_room_new_user_cnt` bigint(20) NOT NULL DEFAULT '0' COMMENT '新人进房人数',
  `user_full_one_min` bigint(20) NOT NULL DEFAULT '0' COMMENT '房间逗留人数',
  `gift_user_cnt` bigint(20) NOT NULL DEFAULT '0' COMMENT '送礼人数',
  `gift_new_user_cnt` bigint(20) NOT NULL DEFAULT '0' COMMENT '新人送礼人数',
  `new_fans_user_cnt` bigint(20) NOT NULL DEFAULT '0' COMMENT '房间新增粉丝数',
  `all_income` bigint(20) NOT NULL DEFAULT '0' COMMENT '送礼钻石数',
  `all_charm` bigint(20) NOT NULL DEFAULT '0' COMMENT '送礼魅力值',
  `app_id` int(11) NOT NULL COMMENT '应用ID',
  PRIMARY KEY (`id`),
  KEY `idx_activity_id` (`activity_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='活动报表-明细表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `activity_report_data_gift`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `activity_report_data_gift` (
  `id` bigint(20) NOT NULL COMMENT 'ID',
  `activity_id` bigint(20) NOT NULL COMMENT '活动ID',
  `nj_id` bigint(20) NOT NULL COMMENT '提报厅厅主ID',
  `family_id` bigint(20) NOT NULL COMMENT '家族ID',
  `start_time` datetime NOT NULL COMMENT '活动开始时间',
  `end_time` datetime NOT NULL COMMENT '活动结束时间',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `user_name` varchar(64) CHARACTER SET utf8mb4 DEFAULT NULL,
  `all_income` bigint(20) NOT NULL DEFAULT '0' COMMENT '送礼钻石数',
  `all_charm` bigint(20) NOT NULL DEFAULT '0' COMMENT '送礼魅力值',
  `gift_cnt` bigint(20) NOT NULL DEFAULT '0' COMMENT '送礼笔数',
  `app_id` int(11) NOT NULL COMMENT '应用ID',
  PRIMARY KEY (`id`),
  KEY `idx_activity_id` (`activity_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='活动报表-用户送礼表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `activity_report_data_player`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `activity_report_data_player` (
  `id` bigint(20) NOT NULL COMMENT 'ID',
  `activity_id` bigint(20) NOT NULL COMMENT '活动ID',
  `nj_id` bigint(20) NOT NULL COMMENT '提报厅厅主ID',
  `family_id` bigint(20) NOT NULL COMMENT '家族ID',
  `start_time` datetime NOT NULL COMMENT '活动开始时间',
  `end_time` datetime NOT NULL COMMENT '活动结束时间',
  `player_id` bigint(20) NOT NULL COMMENT '主播 ID',
  `player_name` varchar(64) CHARACTER SET utf8mb4 DEFAULT NULL,
  `gift_user_cnt` bigint(20) NOT NULL DEFAULT '0' COMMENT '送礼人数',
  `new_fans_user_cnt` bigint(20) NOT NULL DEFAULT '0' COMMENT '房间新增粉丝数',
  `all_income` bigint(20) NOT NULL DEFAULT '0' COMMENT '送礼钻石数',
  `all_charm` bigint(20) NOT NULL DEFAULT '0' COMMENT '送礼魅力值',
  `app_id` int(11) NOT NULL COMMENT '应用ID',
  PRIMARY KEY (`id`),
  KEY `idx_activity_id` (`activity_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='活动报表-主播表现表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `activity_report_data_summary`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `activity_report_data_summary` (
  `id` bigint(20) NOT NULL COMMENT 'ID',
  `activity_id` bigint(20) NOT NULL COMMENT '活动ID',
  `nj_id` bigint(20) NOT NULL COMMENT '提报厅厅主ID',
  `family_id` bigint(20) NOT NULL COMMENT '家族ID',
  `start_time` datetime NOT NULL COMMENT '活动开始时间',
  `end_time` datetime NOT NULL COMMENT '活动结束时间',
  `enter_room_user_cnt` bigint(20) NOT NULL DEFAULT '0' COMMENT '进房人数',
  `enter_room_new_user_cnt` bigint(20) NOT NULL DEFAULT '0' COMMENT '新人进房人数',
  `user_full_one_min` bigint(20) NOT NULL DEFAULT '0' COMMENT '房间逗留人数',
  `gift_user_cnt` bigint(20) NOT NULL DEFAULT '0' COMMENT '送礼人数',
  `gift_new_user_cnt` bigint(20) NOT NULL DEFAULT '0' COMMENT '新人送礼人数',
  `new_fans_user_cnt` bigint(20) NOT NULL DEFAULT '0' COMMENT '房间新增粉丝数',
  `all_income` bigint(20) NOT NULL DEFAULT '0' COMMENT '送礼钻石数',
  `all_charm` bigint(20) NOT NULL DEFAULT '0' COMMENT '送礼魅力值',
  `app_id` int(11) NOT NULL COMMENT '应用ID',
  `new_user_full_one_min` bigint(20) NOT NULL DEFAULT '0' COMMENT '新人逗留人数',
  PRIMARY KEY (`id`),
  KEY `idx_activity_id_start_time_end_time` (`activity_id`,`start_time`,`end_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='活动报表-汇总表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `activity_resource_config`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `activity_resource_config` (
  `id` bigint(20) NOT NULL,
  `name` varchar(20) NOT NULL COMMENT '资源名称',
  `app_id` int(10) NOT NULL COMMENT '应用ID',
  `resource_code` varchar(20) NOT NULL DEFAULT '' COMMENT '资源code，只有自动配置的资源有',
  `introduction` varchar(500) NOT NULL DEFAULT '' COMMENT '资源介绍',
  `image_url` varchar(100) NOT NULL DEFAULT '' COMMENT '资源图片，斜杠开头',
  `deploy_type` tinyint(4) NOT NULL DEFAULT '1' COMMENT '资源配置类型，1：自动，2：手动',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态，0：禁用，1：启用',
  `required` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否必选',
  `deploy_env` varchar(10) NOT NULL DEFAULT 'TEST' COMMENT '环境隔离 TEST/PRE/PRO',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除',
  `operator` varchar(20) NOT NULL DEFAULT '' COMMENT '操作者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='活动资源配置表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `activity_resource_give_record`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `activity_resource_give_record` (
  `id` bigint(20) NOT NULL COMMENT '主键',
  `resource_id` bigint(20) NOT NULL COMMENT '要发放的资源ID',
  `app_id` int(10) NOT NULL COMMENT '应用ID',
  `activity_name` varchar(20) NOT NULL COMMENT '活动名称，冗余，会大量使用',
  `activity_id` bigint(20) NOT NULL COMMENT '活动ID',
  `start_time` datetime NOT NULL COMMENT '活动开始时间',
  `end_time` datetime NOT NULL COMMENT '活动结束时间',
  `status` tinyint(4) NOT NULL COMMENT '状态，0：待发放，1：发放失败，2：超时失败，3：成功',
  `error_code` int(10) NOT NULL COMMENT '错误码',
  `error_msg` varchar(100) DEFAULT NULL COMMENT '错误描述',
  `try_count` int(10) NOT NULL DEFAULT '0' COMMENT '重试次数',
  `type` tinyint(4) NOT NULL COMMENT '资源类型，1：装扮，2：流量资源',
  `deploy_env` varchar(20) NOT NULL COMMENT '可选值  TEST/PRE/PRO',
  `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_act_id` (`activity_id`) USING BTREE,
  KEY `idx_time` (`start_time`) USING BTREE COMMENT '活动开始时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='活动资源配置记录';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `activity_resource_level_relation`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `activity_resource_level_relation` (
  `id` bigint(20) NOT NULL,
  `resource_id` bigint(20) NOT NULL,
  `level_id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_res_id` (`resource_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='资源-等级关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `activity_resource_transfer`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `activity_resource_transfer` (
  `id` bigint(20) NOT NULL COMMENT 'ID',
  `app_id` int(11) NOT NULL COMMENT '应用ID',
  `source_uri` varchar(100) NOT NULL DEFAULT '' COMMENT '来源 URI',
  `target_uri` varchar(100) NOT NULL DEFAULT '' COMMENT '转存 URI',
  `status` tinyint(4) NOT NULL DEFAULT '-1' COMMENT '转存状态 0: 失败 1: 成功',
  `try_count` int(11) NOT NULL DEFAULT '0' COMMENT '重试次数',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_unique_app_source_id` (`source_uri`,`app_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='活动资源转存记录';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `activity_rule_config`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `activity_rule_config` (
  `id` bigint(20) NOT NULL,
  `app_id` int(10) NOT NULL COMMENT '应用ID',
  `rule_type` tinyint(4) NOT NULL COMMENT '规则类型，1：提报次数，2：官频位轮播厅数',
  `rule_json` text NOT NULL COMMENT '规则json，根据类型映射对象',
  `deploy_env` varchar(10) NOT NULL DEFAULT 'TEST' COMMENT '环境隔离 TEST/PRE/PRO',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  `operator` varchar(20) NOT NULL DEFAULT '' COMMENT '操作者',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='活动-提报规则表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `activity_template_flow_resource`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `activity_template_flow_resource` (
  `id` bigint(20) NOT NULL COMMENT '活动模板流量资源ID',
  `template_id` bigint(20) NOT NULL COMMENT '活动模板ID',
  `resource_config_id` bigint(20) NOT NULL COMMENT '资源配置ID，对应activity_resource_config.id',
  `index` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
  `extra` varchar(1000) NOT NULL DEFAULT '' COMMENT '资源扩展配置，JSON结构',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uq_template_id_resource_config_id` (`template_id`,`resource_config_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='活动模板流量资源表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `activity_template_flow_resource_image`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `activity_template_flow_resource_image` (
  `id` bigint(20) NOT NULL COMMENT '活动模板流量资源图片ID',
  `flow_resource_id` bigint(20) NOT NULL COMMENT '流量资源ID，对应activity_template_flow_resource.id',
  `image_url` varchar(100) NOT NULL DEFAULT '' COMMENT '图片地址，平台域名，相对路径，斜杆开头',
  `extra` varchar(1000) NOT NULL DEFAULT '' COMMENT '图片扩展配置, JSON结构，对应activity_image_fodder表的图片附加字段',
  `index` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uq_flow_resource_id_image_url` (`flow_resource_id`,`image_url`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='活动模板流量资源图片表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `activity_template_highlight`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `activity_template_highlight` (
  `id` bigint(20) NOT NULL COMMENT '活动模板亮点标签ID',
  `template_id` bigint(20) NOT NULL COMMENT '活动模板ID',
  `highlight_key` varchar(100) NOT NULL DEFAULT '' COMMENT '亮点标签key',
  `highlight_value` varchar(100) NOT NULL DEFAULT '' COMMENT '亮点标签value',
  `index` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
  PRIMARY KEY (`id`),
  KEY `idx_template_id` (`template_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='活动模板亮点标签表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `activity_template_info`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `activity_template_info` (
  `id` bigint(20) NOT NULL COMMENT '活动模板ID',
  `app_id` int(11) NOT NULL COMMENT '应用ID',
  `name` varchar(20) NOT NULL DEFAULT '' COMMENT '活动名',
  `class_id` bigint(20) NOT NULL COMMENT '分类ID，对应activity_class_config.id',
  `goal` varchar(100) NOT NULL DEFAULT '' COMMENT '活动目标',
  `introduction` varchar(100) NOT NULL DEFAULT '' COMMENT '活动介绍',
  `auxiliary_prop_url` varchar(1000) NOT NULL DEFAULT '' COMMENT '辅助道具地址列表，逗号分隔，平台域名，相对路径，斜杆开头',
  `poster_url` varchar(100) NOT NULL DEFAULT '' COMMENT '活动海报地址，平台域名，相对路径，斜杆开头',
  `activity_tool` varchar(20) NOT NULL DEFAULT '' COMMENT '玩法工具列表，逗号分隔，数据库仅存储value，apollo存储文本映射',
  `room_background_ids` varchar(1000) NOT NULL DEFAULT '' COMMENT '房间背景ID列表，逗号分隔',
  `avatar_widget_ids` varchar(1000) NOT NULL DEFAULT '' COMMENT '头像框ID列表，逗号分隔',
  `room_announcement` varchar(500) NOT NULL DEFAULT '' COMMENT '房间公告',
  `room_announcement_image` varchar(1000) NOT NULL DEFAULT '' COMMENT '房间公告图片列表，逗号分隔，平台域名，相对路径，斜杆开头',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已删除，0：否，1：是',
  `cover` varchar(100) NOT NULL DEFAULT '' COMMENT '封面地址，平台域名，相对路径，斜杠开头',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '上下架状态，1：下架，2：上架',
  `weight` int(11) NOT NULL DEFAULT '0' COMMENT '权重，权重值高的排在前面',
  `hot_rec` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否上热门推荐，0：否，1：是',
  `hot_weight` int(11) NOT NULL DEFAULT '0' COMMENT '热门权重，权重值高的排在前面，当筛选热门推荐时，按此权重排序',
  `deploy_env` varchar(10) NOT NULL DEFAULT 'TEST' COMMENT '服务部署环境，TEST/PRE/PRO，用于环境隔离',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `operator` varchar(20) NOT NULL DEFAULT '' COMMENT '操作者',
  PRIMARY KEY (`id`),
  KEY `idx_name` (`name`) USING BTREE,
  KEY `idx_create_time` (`create_time`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='活动模板信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `activity_template_process`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `activity_template_process` (
  `id` bigint(20) NOT NULL COMMENT '活动模板环节ID',
  `template_id` bigint(20) NOT NULL COMMENT '活动模板ID',
  `name` varchar(100) NOT NULL DEFAULT '' COMMENT '环节',
  `duration` varchar(100) NOT NULL DEFAULT '' COMMENT '时长',
  `explanation` varchar(500) NOT NULL DEFAULT '' COMMENT '说明',
  `index` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
  PRIMARY KEY (`id`),
  KEY `idx_template_id` (`template_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='活动模板流程表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `activity_template_used_relation`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `activity_template_used_relation` (
  `id` bigint(20) NOT NULL,
  `template_id` bigint(20) NOT NULL COMMENT '模板ID',
  `activity_id` bigint(20) NOT NULL COMMENT '活动ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uq_activity_id_template_id` (`activity_id`,`template_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='活动模板使用关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `wavecenter_audit_record`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `wavecenter_audit_record` (
  `id` bigint(11) NOT NULL,
  `app_id` bigint(11) NOT NULL COMMENT '应用ID',
  `room_id` bigint(11) DEFAULT NULL,
  `family_id` bigint(11) DEFAULT NULL,
  `user_id` bigint(11) NOT NULL COMMENT '主播ID',
  `op` int(6) NOT NULL COMMENT '审核处罚类型',
  `reason` varchar(256) NOT NULL COMMENT '操作理由',
  `insert_time` datetime NOT NULL COMMENT '处罚时间',
  `audit_id` varchar(128) DEFAULT NULL COMMENT '送审id',
  `push_time` varchar(256) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `user_time_index` (`user_id`,`insert_time`) USING BTREE,
  KEY `room_time_index` (`room_id`,`insert_time`) USING BTREE,
  KEY `family_time_index` (`family_id`,`insert_time`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `wavecenter_audit_record_full`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `wavecenter_audit_record_full` (
  `id` bigint(20) NOT NULL,
  `app_id` int(11) NOT NULL COMMENT '应用ID',
  `sign_nj_id` bigint(20) DEFAULT NULL COMMENT '签约厅主ID',
  `sign_family_id` bigint(20) DEFAULT NULL COMMENT '签约家族ID',
  `biz_id` bigint(20) DEFAULT NULL COMMENT '关联的业务场景ID: 直播场景为liveId',
  `biz_nj_id` bigint(20) DEFAULT NULL COMMENT '关联的业务场景对应的厅主ID,比如直播的厅主ID',
  `biz_family_id` bigint(20) DEFAULT NULL COMMENT '关联的业务场景对应的家族ID,比如直播的家族ID',
  `user_id` bigint(20) NOT NULL COMMENT '违规用户ID',
  `to_user_id` bigint(20) DEFAULT NULL COMMENT '受害者用户ID',
  `op` int(11) NOT NULL COMMENT '审核处罚类型',
  `reason` varchar(256) NOT NULL COMMENT '操作理由',
  `audit_end_time` datetime NOT NULL COMMENT '审核时间',
  `audit_start_time` datetime NOT NULL COMMENT '送审时间',
  `audit_id` varchar(128) NOT NULL COMMENT '送审id',
  `record_id` varchar(128) NOT NULL COMMENT '审核记录ID',
  `scene_type` int(11) NOT NULL COMMENT '接审业务场景值',
  `scene_name` varchar(256) NOT NULL COMMENT '接审业务场景名称',
  `push_time` varchar(256) DEFAULT NULL COMMENT '处罚有效时间:1天',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  `source_content_url` varchar(1000) NOT NULL DEFAULT '' COMMENT '原录音文件 URL',
  `public_content_url` varchar(1000) NOT NULL DEFAULT '' COMMENT '公开录音文件 URL',
  PRIMARY KEY (`id`),
  UNIQUE KEY `ri_record` (`record_id`),
  KEY `ri_audit` (`audit_id`),
  KEY `ri_user` (`user_id`),
  KEY `ri_create_time` (`create_time`),
  KEY `ri_family` (`sign_family_id`,`biz_family_id`,`sign_nj_id`,`biz_nj_id`,`user_id`,`audit_start_time`),
  KEY `ri_nj` (`sign_nj_id`,`biz_nj_id`,`user_id`,`audit_start_time`),
  KEY `ri_start_time` (`audit_start_time`),
  KEY `ri_biz_family_nj` (`biz_family_id`,`biz_nj_id`,`audit_start_time`,`user_id`),
  KEY `ri_sign_family_nj` (`sign_family_id`,`sign_nj_id`,`audit_start_time`,`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `wavecenter_check_in_record`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `wavecenter_check_in_record` (
  `id` bigint(11) NOT NULL,
  `app_id` int(11) NOT NULL COMMENT '应用标识',
  `family_id` bigint(11) NOT NULL COMMENT '家族id',
  `host_id` bigint(11) DEFAULT NULL COMMENT '主持ID',
  `room_id` bigint(11) DEFAULT NULL COMMENT '直播房间ID',
  `user_id` bigint(11) NOT NULL COMMENT '主播id',
  `nj_id` bigint(11) NOT NULL COMMENT '厅主ID',
  `income` decimal(12,4) DEFAULT '0.0000' COMMENT '收益值',
  `status` tinyint(4) DEFAULT NULL COMMENT ' 0：未打卡，1：已打卡，2：确认打卡',
  `charm_value` bigint(11) DEFAULT NULL COMMENT '调整之后的魅力值',
  `charm` bigint(11) DEFAULT '0' COMMENT '魅力值',
  `end_time` datetime DEFAULT NULL COMMENT '档期结束时间',
  `start_time` datetime DEFAULT NULL COMMENT '档期开始时间',
  `is_host` tinyint(2) DEFAULT NULL COMMENT '0: 不是主持  1：主持',
  `remark` varchar(512) DEFAULT NULL COMMENT '备注',
  `up_guest_dur` int(8) DEFAULT NULL COMMENT '上麦时长(单位：分钟)',
  `create_time` datetime DEFAULT NULL,
  `modiy_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `user_index` (`user_id`,`start_time`),
  KEY `nj_id_state_date` (`nj_id`,`start_time`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `wavecenter_component`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `wavecenter_component` (
  `id` bigint(20) NOT NULL,
  `component_name` varchar(32) NOT NULL COMMENT '组件名称',
  `component_code` varchar(32) NOT NULL COMMENT '组件编码',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '角色创建时间',
  `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '角色修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_c` (`component_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='组件表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `wavecenter_data_family_day`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `wavecenter_data_family_day` (
  `id` bigint(20) NOT NULL,
  `app_id` int(11) NOT NULL COMMENT '业务',
  `stat_date` date NOT NULL COMMENT '日期 格式 YYYY-MM-DD',
  `stat_date_value` int(11) NOT NULL COMMENT '日期 格式  YYYYMMDD',
  `family_id` bigint(20) NOT NULL COMMENT '公会ID',
  `income` decimal(12,4) NOT NULL COMMENT '公会收入,公会考核期间总收入，单位：钻（结算币)',
  `charm` int(11) NOT NULL DEFAULT '0' COMMENT '公会魅力值        公会考核期间总魅力值，单位：魅力值',
  `sign_room_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '签约厅数        公会签约厅数',
  `open_room_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '开播厅数        公会开播厅数',
  `room_avg_income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '厅均收入 公会收入/开播厅数',
  `room_avg_charm` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '厅均魅力值 公会魅力值/开播厅数',
  `sign_player_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '签约主播数',
  `up_guest_player_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '上麦主播数 直播间上麦人数',
  `income_player_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '有收入主播数 直播间有收入签约主播人数',
  `player_avg_income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '人均收入 公会收入/有收入主播数',
  `player_avg_charm` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '人均魅力值 公会魅力值/有收入主播数',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '角色创建时间',
  `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '角色修改时间',
  `all_income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '总收入',
  `sign_hall_income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '签约厅收礼收入',
  `official_hall_income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '官方厅收礼收入',
  `personal_hall_income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '个播收礼收入',
  `noble_income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '贵族提成收入',
  `personal_noble_income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '个播贵族提成收入',
  `up_player_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '上麦率',
  `open_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '开播率',
  `income_player_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '有收入主播占比',
  `income_room_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '有收入厅数',
  `income_room_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '有收入厅占比',
  PRIMARY KEY (`id`),
  KEY `idx_afs` (`app_id`,`family_id`,`stat_date_value`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='大数据公会日统计表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `wavecenter_data_family_month`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `wavecenter_data_family_month` (
  `id` bigint(20) NOT NULL,
  `app_id` int(11) NOT NULL COMMENT '业务',
  `stat_year` int(11) NOT NULL COMMENT '年 格式:YYYY',
  `stat_month` int(11) NOT NULL COMMENT '月  格式: YYYYMM',
  `family_id` bigint(20) NOT NULL COMMENT '公会ID',
  `income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '公会收入,公会考核期间总收入，单位：钻（结算币)',
  `charm` int(11) NOT NULL DEFAULT '0' COMMENT '公会魅力值        公会考核期间总魅力值，单位：魅力值',
  `sign_room_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '签约厅数        公会签约厅数',
  `open_room_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '开播厅数        公会开播厅数',
  `room_avg_income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '厅均收入 公会收入/开播厅数',
  `room_avg_charm` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '厅均魅力值 公会魅力值/开播厅数',
  `sign_player_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '签约主播数',
  `up_guest_player_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '上麦主播数 直播间上麦人数',
  `income_player_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '有收入主播数 直播间有收入签约主播人数',
  `player_avg_income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '人均收入 公会收入/有收入主播数',
  `player_avg_charm` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '人均魅力值 公会魅力值/有收入主播数',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '角色创建时间',
  `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '角色修改时间',
  `all_income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '总收入',
  `sign_hall_income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '签约厅收礼收入',
  `official_hall_income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '官方厅收礼收入',
  `personal_hall_income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '个播收礼收入',
  `noble_income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '贵族提成收入',
  `personal_noble_income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '个播贵族提成收入',
  `up_player_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '上麦率',
  `open_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '开播率',
  `income_player_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '有收入主播占比',
  `income_room_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '有收入厅数',
  `income_room_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '有收入厅占比',
  PRIMARY KEY (`id`),
  KEY `idx_base` (`app_id`,`family_id`,`stat_month`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='大数据公会月统计表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `wavecenter_data_family_week`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `wavecenter_data_family_week` (
  `id` bigint(20) NOT NULL,
  `app_id` int(11) NOT NULL COMMENT '业务',
  `start_week_date` date NOT NULL COMMENT '日期 格式 YYYY-MM-DD',
  `end_week_date` date NOT NULL COMMENT '日期 格式 YYYY-MM-DD',
  `family_id` bigint(20) NOT NULL COMMENT '公会ID',
  `income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '公会收入,公会考核期间总收入，单位：钻（结算币)',
  `charm` int(11) NOT NULL DEFAULT '0' COMMENT '公会魅力值        公会考核期间总魅力值，单位：魅力值',
  `sign_room_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '签约厅数        公会签约厅数',
  `open_room_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '开播厅数        公会开播厅数',
  `room_avg_income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '厅均收入 公会收入/开播厅数',
  `room_avg_charm` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '厅均魅力值 公会魅力值/开播厅数',
  `sign_player_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '签约主播数',
  `up_guest_player_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '上麦主播数 直播间上麦人数',
  `income_player_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '有收入主播数 直播间有收入签约主播人数',
  `player_avg_income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '人均收入 公会收入/有收入主播数',
  `player_avg_charm` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '人均魅力值 公会魅力值/有收入主播数',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '角色创建时间',
  `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '角色修改时间',
  `all_income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '总收入',
  `sign_hall_income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '签约厅收礼收入',
  `official_hall_income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '官方厅收礼收入',
  `personal_hall_income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '个播收礼收入',
  `noble_income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '贵族提成收入',
  `personal_noble_income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '个播贵族提成收入',
  `up_player_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '上麦率',
  `open_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '开播率',
  `income_player_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '有收入主播占比',
  `income_room_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '有收入厅数',
  `income_room_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '有收入厅占比',
  PRIMARY KEY (`id`),
  KEY `idx_base` (`app_id`,`family_id`,`start_week_date`,`end_week_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='大数据公会周统计表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `wavecenter_data_player_day`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `wavecenter_data_player_day` (
  `id` bigint(20) NOT NULL,
  `app_id` int(11) NOT NULL COMMENT '业务',
  `stat_date` date NOT NULL COMMENT '日期 格式 YYYY-MM-DD',
  `stat_date_value` int(11) NOT NULL COMMENT '日期 格式  YYYYMMDD',
  `player_id` bigint(20) NOT NULL COMMENT '主播ID',
  `income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '收入,考核期间总收入，单位：钻（结算币)',
  `charm` int(11) NOT NULL DEFAULT '0' COMMENT '公会魅力值        公会考核期间总魅力值，单位：魅力值',
  `up_guest_dur` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '上麦时长(分钟)',
  `gift_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '送礼人数',
  `gift_user_price` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '送礼客单价',
  `chat_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '私信人数',
  `reply_chat_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '私信回复人数',
  `chat_enter_room_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '私信进房人数',
  `chat_gift_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '私信付费人数',
  `reply_chat_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '私信回复率',
  `chat_enter_room_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '私信进房率',
  `chat_gift_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '私信付费率',
  `invite_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '邀请人数',
  `invite_enter_room_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '邀请进房人数',
  `invite_gift_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '邀请付费人数',
  `invite_enter_room_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '邀请进房率',
  `invite_gift_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '邀请付费率',
  `fans_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '主播粉丝数',
  `new_fans_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '新增粉丝数',
  `fans_gift_income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '粉丝送礼收入',
  `fans_gift_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '粉丝送礼人数',
  `fans_gift_user_price` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '粉丝送礼客单价',
  `room_sign_player_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '主播签约厅的主播数',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '角色创建时间',
  `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '角色修改时间',
  `sign_hall_income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '签约厅收礼收入',
  `official_hall_income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '官方厅收礼收入',
  `personal_hall_income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '个播收礼收入',
  PRIMARY KEY (`id`),
  KEY `idx_base` (`app_id`,`player_id`,`stat_date_value`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='大数据主播日统计表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `wavecenter_data_player_month`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `wavecenter_data_player_month` (
  `id` bigint(20) NOT NULL,
  `app_id` int(11) NOT NULL COMMENT '业务',
  `stat_year` int(11) NOT NULL COMMENT '年 格式:YYYY',
  `stat_month` int(11) NOT NULL COMMENT '月  格式: YYYYMM',
  `player_id` bigint(20) NOT NULL COMMENT '陪玩ID',
  `income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '公会收入,公会考核期间总收入，单位：钻（结算币)',
  `charm` int(11) NOT NULL DEFAULT '0' COMMENT '公会魅力值        公会考核期间总魅力值，单位：魅力值',
  `up_guest_dur` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '上麦时长(分钟)',
  `gift_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '送礼人数',
  `gift_user_price` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '送礼客单价',
  `chat_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '私信人数',
  `reply_chat_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '私信回复人数',
  `chat_enter_room_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '私信进房人数',
  `chat_gift_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '私信付费人数',
  `reply_chat_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '私信回复率',
  `chat_enter_room_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '私信进房率',
  `chat_gift_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '私信付费率',
  `invite_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '邀请人数',
  `invite_enter_room_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '邀请进房人数',
  `invite_gift_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '邀请付费人数',
  `invite_enter_room_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '邀请进房率',
  `invite_gift_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '邀请付费率',
  `fans_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '主播粉丝数',
  `new_fans_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '新增粉丝数',
  `fans_gift_income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '粉丝送礼收入',
  `fans_gift_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '粉丝送礼人数',
  `fans_gift_user_price` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '粉丝送礼客单价',
  `room_sign_player_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '主播签约厅的主播数',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '角色创建时间',
  `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '角色修改时间',
  `sign_hall_income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '签约厅收礼收入',
  `official_hall_income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '官方厅收礼收入',
  `personal_hall_income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '个播收礼收入',
  PRIMARY KEY (`id`),
  KEY `idx_base` (`app_id`,`player_id`,`stat_month`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='大数据主播月统计表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `wavecenter_data_player_room_day`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `wavecenter_data_player_room_day` (
  `id` bigint(20) NOT NULL,
  `app_id` int(11) NOT NULL COMMENT '业务',
  `stat_date` date NOT NULL COMMENT '日期 格式 YYYY-MM-DD',
  `stat_date_value` int(11) NOT NULL COMMENT '日期 格式  YYYYMMDD',
  `player_id` bigint(20) NOT NULL COMMENT '陪玩ID',
  `room_id` bigint(20) NOT NULL COMMENT '厅主ID',
  `family_id` bigint(20) NOT NULL COMMENT '公会ID',
  `income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '收入,考核期间总收入，单位：钻（结算币)',
  `charm` int(11) NOT NULL DEFAULT '0' COMMENT '公会魅力值        公会考核期间总魅力值，单位：魅力值',
  `up_guest_dur` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '上麦时长(分钟)',
  `gift_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '送礼人数',
  `gift_user_price` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '送礼客单价',
  `chat_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '私信人数',
  `reply_chat_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '私信回复人数',
  `chat_enter_room_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '私信进房人数',
  `chat_gift_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '私信付费人数',
  `reply_chat_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '私信回复率',
  `chat_enter_room_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '私信进房率',
  `chat_gift_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '私信付费率',
  `invite_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '邀请人数',
  `invite_enter_room_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '邀请进房人数',
  `invite_gift_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '邀请付费人数',
  `invite_enter_room_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '邀请进房率',
  `invite_gift_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '邀请付费率',
  `fans_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '主播粉丝数',
  `new_fans_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '新增粉丝数',
  `fans_gift_income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '粉丝送礼收入',
  `fans_gift_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '粉丝送礼人数',
  `fans_gift_user_price` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '粉丝送礼客单价',
  `room_sign_player_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '主播签约厅的主播数',
  `income_rank_room` int(11) NOT NULL DEFAULT '0' COMMENT '主播的收入在厅的排名',
  `income_rank_family` int(11) NOT NULL DEFAULT '0' COMMENT '主播的收入在工会的排名',
  `charm_rank_room` int(11) NOT NULL DEFAULT '0' COMMENT '主播的魅力值在厅的排名',
  `charm_rank_family` int(11) NOT NULL DEFAULT '0' COMMENT '主播的魅力值在工会的排名',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '角色创建时间',
  `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '角色修改时间',
  `sign_hall_income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '签约厅收礼收入',
  `official_hall_income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '官方厅收礼收入',
  `personal_hall_income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '个播收礼收入',
  PRIMARY KEY (`id`),
  KEY `idx_base` (`app_id`,`player_id`,`room_id`,`family_id`,`stat_date_value`),
  KEY `idx_af` (`app_id`,`family_id`,`stat_date_value`),
  KEY `idx_afp` (`app_id`,`family_id`,`player_id`,`stat_date_value`),
  KEY `idx_afr` (`app_id`,`family_id`,`room_id`,`stat_date_value`),
  KEY `idx_arp` (`app_id`,`room_id`,`player_id`,`stat_date_value`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='大数据主播日统计表-主播厅公会维度';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `wavecenter_data_player_room_month`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `wavecenter_data_player_room_month` (
  `id` bigint(20) NOT NULL,
  `app_id` int(11) NOT NULL COMMENT '业务',
  `stat_year` int(11) NOT NULL COMMENT '年 格式:YYYY',
  `stat_month` int(11) NOT NULL COMMENT '月  格式: YYYYMM',
  `player_id` bigint(20) NOT NULL COMMENT '陪玩ID',
  `room_id` bigint(20) NOT NULL COMMENT '厅主ID',
  `family_id` bigint(20) NOT NULL COMMENT '公会ID',
  `income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '公会收入,公会考核期间总收入，单位：钻（结算币)',
  `charm` int(11) NOT NULL DEFAULT '0' COMMENT '公会魅力值        公会考核期间总魅力值，单位：魅力值',
  `up_guest_dur` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '上麦时长(分钟)',
  `gift_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '送礼人数',
  `gift_user_price` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '送礼客单价',
  `chat_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '私信人数',
  `reply_chat_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '私信回复人数',
  `chat_enter_room_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '私信进房人数',
  `chat_gift_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '私信付费人数',
  `reply_chat_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '私信回复率',
  `chat_enter_room_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '私信进房率',
  `chat_gift_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '私信付费率',
  `invite_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '邀请人数',
  `invite_enter_room_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '邀请进房人数',
  `invite_gift_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '邀请付费人数',
  `invite_enter_room_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '邀请进房率',
  `invite_gift_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '邀请付费率',
  `fans_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '主播粉丝数',
  `new_fans_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '新增粉丝数',
  `fans_gift_income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '粉丝送礼收入',
  `fans_gift_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '粉丝送礼人数',
  `fans_gift_user_price` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '粉丝送礼客单价',
  `room_sign_player_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '主播签约厅的主播数',
  `income_rank_room` int(11) NOT NULL DEFAULT '0' COMMENT '主播的收入在厅的排名',
  `income_rank_family` int(11) NOT NULL DEFAULT '0' COMMENT '主播的收入在工会的排名',
  `charm_rank_room` int(11) NOT NULL DEFAULT '0' COMMENT '主播的魅力值在厅的排名',
  `charm_rank_family` int(11) NOT NULL DEFAULT '0' COMMENT '主播的魅力值在工会的排名',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '角色创建时间',
  `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '角色修改时间',
  `sign_hall_income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '签约厅收礼收入',
  `official_hall_income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '官方厅收礼收入',
  `personal_hall_income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '个播收礼收入',
  PRIMARY KEY (`id`),
  KEY `idx_base` (`app_id`,`player_id`,`room_id`,`family_id`,`stat_month`),
  KEY `idx_af` (`app_id`,`family_id`,`stat_month`),
  KEY `idx_afp` (`app_id`,`family_id`,`player_id`,`stat_month`),
  KEY `idx_afr` (`app_id`,`family_id`,`room_id`,`stat_month`),
  KEY `idx_arp` (`app_id`,`room_id`,`player_id`,`stat_month`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='大数据主播月统计表-主播厅公会维度';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `wavecenter_data_player_room_week`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `wavecenter_data_player_room_week` (
  `id` bigint(20) NOT NULL,
  `app_id` int(11) NOT NULL COMMENT '业务',
  `start_week_date` date NOT NULL COMMENT '日期 格式 YYYY-MM-DD',
  `end_week_date` date NOT NULL COMMENT '日期 格式 YYYY-MM-DD',
  `player_id` bigint(20) NOT NULL COMMENT '陪玩ID',
  `room_id` bigint(20) NOT NULL COMMENT '厅主ID',
  `family_id` bigint(20) NOT NULL COMMENT '公会ID',
  `income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '公会收入,公会考核期间总收入，单位：钻（结算币)',
  `charm` int(11) NOT NULL DEFAULT '0' COMMENT '公会魅力值        公会考核期间总魅力值，单位：魅力值',
  `up_guest_dur` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '上麦时长(分钟)',
  `gift_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '送礼人数',
  `gift_user_price` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '送礼客单价',
  `chat_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '私信人数',
  `reply_chat_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '私信回复人数',
  `chat_enter_room_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '私信进房人数',
  `chat_gift_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '私信付费人数',
  `reply_chat_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '私信回复率',
  `chat_enter_room_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '私信进房率',
  `chat_gift_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '私信付费率',
  `invite_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '邀请人数',
  `invite_enter_room_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '邀请进房人数',
  `invite_gift_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '邀请付费人数',
  `invite_enter_room_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '邀请进房率',
  `invite_gift_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '邀请付费率',
  `fans_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '主播粉丝数',
  `new_fans_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '新增粉丝数',
  `fans_gift_income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '粉丝送礼收入',
  `fans_gift_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '粉丝送礼人数',
  `fans_gift_user_price` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '粉丝送礼客单价',
  `room_sign_player_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '主播签约厅的主播数',
  `income_rank_room` int(11) NOT NULL DEFAULT '0' COMMENT '主播的收入在厅的排名',
  `income_rank_family` int(11) NOT NULL DEFAULT '0' COMMENT '主播的收入在工会的排名',
  `charm_rank_room` int(11) NOT NULL DEFAULT '0' COMMENT '主播的魅力值在厅的排名',
  `charm_rank_family` int(11) NOT NULL DEFAULT '0' COMMENT '主播的魅力值在工会的排名',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '角色创建时间',
  `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '角色修改时间',
  `sign_hall_income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '签约厅收礼收入',
  `official_hall_income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '官方厅收礼收入',
  `personal_hall_income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '个播收礼收入',
  PRIMARY KEY (`id`),
  KEY `idx_base` (`app_id`,`player_id`,`room_id`,`family_id`,`start_week_date`,`end_week_date`),
  KEY `idx_af` (`app_id`,`family_id`,`start_week_date`,`end_week_date`),
  KEY `idx_afp` (`app_id`,`family_id`,`player_id`,`start_week_date`,`end_week_date`),
  KEY `idx_afr` (`app_id`,`family_id`,`room_id`,`start_week_date`,`end_week_date`),
  KEY `idx_arp` (`app_id`,`room_id`,`player_id`,`start_week_date`,`end_week_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='大数据主播周统计表-主播厅公会维度';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `wavecenter_data_player_week`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `wavecenter_data_player_week` (
  `id` bigint(20) NOT NULL,
  `app_id` int(11) NOT NULL COMMENT '业务',
  `start_week_date` date NOT NULL COMMENT '日期 格式 YYYY-MM-DD',
  `end_week_date` date NOT NULL COMMENT '日期 格式 YYYY-MM-DD',
  `player_id` bigint(20) NOT NULL COMMENT '陪玩ID',
  `income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '公会收入,公会考核期间总收入，单位：钻（结算币)',
  `charm` int(11) NOT NULL DEFAULT '0' COMMENT '公会魅力值        公会考核期间总魅力值，单位：魅力值',
  `up_guest_dur` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '上麦时长(分钟)',
  `gift_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '送礼人数',
  `gift_user_price` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '送礼客单价',
  `chat_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '私信人数',
  `reply_chat_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '私信回复人数',
  `chat_enter_room_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '私信进房人数',
  `chat_gift_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '私信付费人数',
  `reply_chat_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '私信回复率',
  `chat_enter_room_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '私信进房率',
  `chat_gift_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '私信付费率',
  `invite_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '邀请人数',
  `invite_enter_room_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '邀请进房人数',
  `invite_gift_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '邀请付费人数',
  `invite_enter_room_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '邀请进房率',
  `invite_gift_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '邀请付费率',
  `fans_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '主播粉丝数',
  `new_fans_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '新增粉丝数',
  `fans_gift_income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '粉丝送礼收入',
  `fans_gift_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '粉丝送礼人数',
  `fans_gift_user_price` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '粉丝送礼客单价',
  `room_sign_player_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '主播签约厅的主播数',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '角色创建时间',
  `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '角色修改时间',
  `sign_hall_income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '签约厅收礼收入',
  `official_hall_income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '官方厅收礼收入',
  `personal_hall_income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '个播收礼收入',
  PRIMARY KEY (`id`),
  KEY `idx_base` (`app_id`,`player_id`,`start_week_date`,`end_week_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='大数据主播周统计表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `wavecenter_data_room_day`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `wavecenter_data_room_day` (
  `id` bigint(20) NOT NULL,
  `app_id` int(11) NOT NULL COMMENT '业务',
  `stat_date` date NOT NULL COMMENT '日期 格式 YYYY-MM-DD',
  `stat_date_value` int(11) NOT NULL COMMENT '日期 格式  YYYYMMDD',
  `room_id` bigint(20) NOT NULL COMMENT '厅主ID',
  `income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '厅收入',
  `charm` int(11) NOT NULL DEFAULT '0' COMMENT '厅魅力值',
  `open_duration` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '开播时长(分钟)',
  `sign_player_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '厅签约主播数',
  `income_player_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '有收入主播人数',
  `income_player_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '有收入主播占比',
  `player_avg_income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '人均收入',
  `player_avg_charm` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '人均魅力值',
  `gift_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '送礼人数',
  `gift_user_price` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '送礼客单价',
  `chat_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '私信人数',
  `reply_chat_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '私信回复人数',
  `chat_enter_room_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '私信进房人数',
  `chat_gift_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '私信付费人数',
  `reply_chat_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '私信回复率',
  `chat_enter_room_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '私信进房率',
  `chat_gift_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '私信付费率',
  `invite_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '邀请人数',
  `invite_enter_room_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '邀请进房人数',
  `invite_gift_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '邀请付费人数',
  `invite_enter_room_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '邀请进房率',
  `invite_gift_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '邀请付费率',
  `fans_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '厅粉丝数',
  `new_fans_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '厅新增粉丝',
  `player_fans_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '主播粉丝',
  `player_new_fans_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '主播新增粉丝',
  `player_fans_enter_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '主播粉丝进房数',
  `player_fans_gift_income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '主播粉丝送礼收入',
  `player_fans_gift_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '主播粉丝送礼人数',
  `player_fans_gift_user_price` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '主播粉丝送礼客单价',
  `enter_room_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '进房人数',
  `up_guest_player_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '上麦人数',
  `sign_up_guest_player_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '直播间签约主播上麦人数',
  `comment_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '评论人数',
  `up_guest_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '上麦率',
  `comment_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '评论互动率',
  `avg_user_stay_duration` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '人均逗留时长(分钟)',
  `user_full_one_min` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '用户逗留人数(满1分钟)',
  `user_full_three_min` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '用户逗留人数(满3分钟)',
  `user_full_five_min` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '用户逗留人数(满5分钟)',
  `income_rank` int(11) NOT NULL DEFAULT '0' COMMENT '收入在公会的排名',
  `charm_rank` int(11) NOT NULL DEFAULT '0' COMMENT '魅力值在公会的排名',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '角色创建时间',
  `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '角色修改时间',
  `all_income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '总收入',
  `sign_hall_income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '签约厅收礼收入',
  `official_hall_income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '官方厅收礼收入',
  `personal_hall_income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '个播收礼收入',
  `noble_income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '贵族提成收入',
  `personal_noble_income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '个播贵族提成收入',
  `up_player_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '主播上麦率',
  `comment_one_min_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '厅评论互动率=评论人数/房间逗留人数(满1分钟)',
  `up_guest_one_min_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '厅嘉宾上麦率=上麦嘉宾数/房间逗留人数(满1分钟)',
  `gift_one_min_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '厅付费转化率(1min)',
  `gift_three_min_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '厅付费转化率(3min)',
  `gift_five_min_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '厅付费转化率(5min)',
  `chat_player_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '私信主播数 本厅签约主播中有私信行为的人数',
  PRIMARY KEY (`id`),
  KEY `idx_base` (`app_id`,`room_id`,`stat_date_value`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='大数据厅日统计表-厅维度';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `wavecenter_data_room_family_day`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `wavecenter_data_room_family_day` (
  `id` bigint(20) NOT NULL,
  `app_id` int(11) NOT NULL COMMENT '业务',
  `stat_date` date NOT NULL COMMENT '日期 格式 YYYY-MM-DD',
  `stat_date_value` int(11) NOT NULL COMMENT '日期 格式  YYYYMMDD',
  `room_id` bigint(20) NOT NULL COMMENT '厅主ID',
  `family_id` bigint(20) NOT NULL COMMENT '公会ID',
  `income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '厅收入',
  `charm` int(11) NOT NULL DEFAULT '0' COMMENT '厅魅力值',
  `open_duration` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '开播时长(分钟)',
  `sign_player_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '厅签约主播数',
  `income_player_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '有收入主播人数',
  `income_player_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '有收入主播占比',
  `player_avg_income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '人均收入',
  `player_avg_charm` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '人均魅力值',
  `gift_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '送礼人数',
  `gift_user_price` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '送礼客单价',
  `chat_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '私信人数',
  `reply_chat_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '私信回复人数',
  `chat_enter_room_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '私信进房人数',
  `chat_gift_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '私信付费人数',
  `reply_chat_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '私信回复率',
  `chat_enter_room_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '私信进房率',
  `chat_gift_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '私信付费率',
  `invite_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '邀请人数',
  `invite_enter_room_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '邀请进房人数',
  `invite_gift_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '邀请付费人数',
  `invite_enter_room_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '邀请进房率',
  `invite_gift_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '邀请付费率',
  `fans_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '厅粉丝数',
  `new_fans_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '厅新增粉丝',
  `player_fans_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '主播粉丝',
  `player_new_fans_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '主播新增粉丝',
  `player_fans_enter_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '主播粉丝进房数',
  `player_fans_gift_income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '主播粉丝送礼收入',
  `player_fans_gift_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '主播粉丝送礼人数',
  `player_fans_gift_user_price` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '主播粉丝送礼客单价',
  `enter_room_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '进房人数',
  `up_guest_player_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '上麦人数',
  `sign_up_guest_player_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '直播间签约主播上麦人数',
  `comment_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '评论人数',
  `up_guest_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '上麦率',
  `comment_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '评论互动率',
  `avg_user_stay_duration` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '人均逗留时长(分钟)',
  `user_full_one_min` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '用户逗留人数(满1分钟)',
  `user_full_three_min` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '用户逗留人数(满3分钟)',
  `user_full_five_min` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '用户逗留人数(满5分钟)',
  `income_rank` int(11) NOT NULL DEFAULT '0' COMMENT '收入在公会的排名',
  `charm_rank` int(11) NOT NULL DEFAULT '0' COMMENT '魅力值在公会的排名',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '角色创建时间',
  `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '角色修改时间',
  `all_income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '总收入',
  `sign_hall_income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '签约厅收礼收入',
  `official_hall_income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '官方厅收礼收入',
  `personal_hall_income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '个播收礼收入',
  `noble_income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '贵族提成收入',
  `personal_noble_income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '个播贵族提成收入',
  `up_player_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '主播上麦率',
  `comment_one_min_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '厅评论互动率=评论人数/房间逗留人数(满1分钟)',
  `up_guest_one_min_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '厅嘉宾上麦率=上麦嘉宾数/房间逗留人数(满1分钟)',
  `gift_one_min_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '厅付费转化率(1min)',
  `gift_three_min_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '厅付费转化率(3min)',
  `gift_five_min_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '厅付费转化率(5min)',
  `chat_player_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '私信主播数 本厅签约主播中有私信行为的人数',
  PRIMARY KEY (`id`),
  KEY `idx_base` (`app_id`,`room_id`,`family_id`,`stat_date_value`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='大数据厅日统计表-厅-公会维度';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `wavecenter_data_room_family_month`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `wavecenter_data_room_family_month` (
  `id` bigint(20) NOT NULL,
  `app_id` int(11) NOT NULL COMMENT '业务',
  `stat_year` int(11) NOT NULL COMMENT '年 格式:YYYY',
  `stat_month` int(11) NOT NULL COMMENT '月  格式: YYYYMM',
  `room_id` bigint(20) NOT NULL COMMENT '厅主ID',
  `family_id` bigint(20) NOT NULL COMMENT '公会ID',
  `income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '厅收入',
  `charm` int(11) NOT NULL DEFAULT '0' COMMENT '厅魅力值',
  `open_duration` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '开播时长(分钟)',
  `sign_player_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '厅签约主播数',
  `income_player_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '有收入主播人数',
  `income_player_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '有收入主播占比',
  `player_avg_income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '人均收入',
  `player_avg_charm` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '人均魅力值',
  `gift_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '送礼人数',
  `gift_user_price` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '送礼客单价',
  `chat_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '私信人数',
  `reply_chat_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '私信回复人数',
  `chat_enter_room_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '私信进房人数',
  `chat_gift_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '私信付费人数',
  `reply_chat_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '私信回复率',
  `chat_enter_room_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '私信进房率',
  `chat_gift_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '私信付费率',
  `invite_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '邀请人数',
  `invite_enter_room_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '邀请进房人数',
  `invite_gift_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '邀请付费人数',
  `invite_enter_room_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '邀请进房率',
  `invite_gift_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '邀请付费率',
  `fans_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '厅粉丝数',
  `new_fans_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '厅新增粉丝',
  `player_fans_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '主播粉丝',
  `player_new_fans_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '主播新增粉丝',
  `player_fans_enter_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '主播粉丝进房数',
  `player_fans_gift_income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '主播粉丝送礼收入',
  `player_fans_gift_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '主播粉丝送礼人数',
  `player_fans_gift_user_price` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '主播粉丝送礼客单价',
  `enter_room_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '进房人数',
  `up_guest_player_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '上麦人数',
  `sign_up_guest_player_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '直播间签约主播上麦人数',
  `comment_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '评论人数',
  `up_guest_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '上麦率',
  `comment_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '评论互动率',
  `avg_user_stay_duration` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '人均逗留时长(分钟)',
  `user_full_one_min` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '用户逗留人数(满1分钟)',
  `user_full_three_min` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '用户逗留人数(满3分钟)',
  `user_full_five_min` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '用户逗留人数(满5分钟)',
  `income_rank` int(11) NOT NULL DEFAULT '0' COMMENT '收入在公会的排名',
  `charm_rank` int(11) NOT NULL DEFAULT '0' COMMENT '魅力值在公会的排名',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '角色创建时间',
  `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '角色修改时间',
  `all_income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '总收入',
  `sign_hall_income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '签约厅收礼收入',
  `official_hall_income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '官方厅收礼收入',
  `personal_hall_income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '个播收礼收入',
  `noble_income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '贵族提成收入',
  `personal_noble_income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '个播贵族提成收入',
  `up_player_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '主播上麦率',
  `comment_one_min_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '厅评论互动率=评论人数/房间逗留人数(满1分钟)',
  `up_guest_one_min_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '厅嘉宾上麦率=上麦嘉宾数/房间逗留人数(满1分钟)',
  `gift_one_min_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '厅付费转化率(1min)',
  `gift_three_min_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '厅付费转化率(3min)',
  `gift_five_min_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '厅付费转化率(5min)',
  `chat_player_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '私信主播数 本厅签约主播中有私信行为的人数',
  PRIMARY KEY (`id`),
  KEY `idx_base` (`app_id`,`room_id`,`family_id`,`stat_month`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='大数据厅月统计表-厅-公会维度';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `wavecenter_data_room_family_week`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `wavecenter_data_room_family_week` (
  `id` bigint(20) NOT NULL,
  `app_id` int(11) NOT NULL COMMENT '业务',
  `start_week_date` date NOT NULL COMMENT '日期 格式 YYYY-MM-DD',
  `end_week_date` date NOT NULL COMMENT '日期 格式 YYYY-MM-DD',
  `room_id` bigint(20) NOT NULL COMMENT '厅主ID',
  `family_id` bigint(20) NOT NULL COMMENT '公会ID',
  `income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '厅收入',
  `charm` int(11) NOT NULL DEFAULT '0' COMMENT '厅魅力值',
  `open_duration` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '开播时长(分钟)',
  `sign_player_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '厅签约主播数',
  `income_player_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '有收入主播人数',
  `income_player_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '有收入主播占比',
  `player_avg_income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '人均收入',
  `player_avg_charm` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '人均魅力值',
  `gift_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '送礼人数',
  `gift_user_price` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '送礼客单价',
  `chat_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '私信人数',
  `reply_chat_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '私信回复人数',
  `chat_enter_room_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '私信进房人数',
  `chat_gift_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '私信付费人数',
  `reply_chat_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '私信回复率',
  `chat_enter_room_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '私信进房率',
  `chat_gift_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '私信付费率',
  `invite_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '邀请人数',
  `invite_enter_room_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '邀请进房人数',
  `invite_gift_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '邀请付费人数',
  `invite_enter_room_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '邀请进房率',
  `invite_gift_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '邀请付费率',
  `fans_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '厅粉丝数',
  `new_fans_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '厅新增粉丝',
  `player_fans_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '主播粉丝',
  `player_new_fans_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '主播新增粉丝',
  `player_fans_enter_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '主播粉丝进房数',
  `player_fans_gift_income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '主播粉丝送礼收入',
  `player_fans_gift_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '主播粉丝送礼人数',
  `player_fans_gift_user_price` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '主播粉丝送礼客单价',
  `enter_room_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '进房人数',
  `up_guest_player_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '上麦人数',
  `sign_up_guest_player_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '直播间签约主播上麦人数',
  `comment_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '评论人数',
  `up_guest_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '上麦率',
  `comment_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '评论互动率',
  `avg_user_stay_duration` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '人均逗留时长(分钟)',
  `user_full_one_min` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '用户逗留人数(满1分钟)',
  `user_full_three_min` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '用户逗留人数(满3分钟)',
  `user_full_five_min` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '用户逗留人数(满5分钟)',
  `income_rank` int(11) NOT NULL DEFAULT '0' COMMENT '收入在公会的排名',
  `charm_rank` int(11) NOT NULL DEFAULT '0' COMMENT '魅力值在公会的排名',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '角色创建时间',
  `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '角色修改时间',
  `all_income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '总收入',
  `sign_hall_income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '签约厅收礼收入',
  `official_hall_income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '官方厅收礼收入',
  `personal_hall_income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '个播收礼收入',
  `noble_income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '贵族提成收入',
  `personal_noble_income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '个播贵族提成收入',
  `up_player_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '主播上麦率',
  `comment_one_min_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '厅评论互动率=评论人数/房间逗留人数(满1分钟)',
  `up_guest_one_min_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '厅嘉宾上麦率=上麦嘉宾数/房间逗留人数(满1分钟)',
  `gift_one_min_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '厅付费转化率(1min)',
  `gift_three_min_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '厅付费转化率(3min)',
  `gift_five_min_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '厅付费转化率(5min)',
  `chat_player_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '私信主播数 本厅签约主播中有私信行为的人数',
  PRIMARY KEY (`id`),
  KEY `idx_base` (`app_id`,`room_id`,`family_id`,`start_week_date`,`end_week_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='大数据厅周统计表-厅-公会维度';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `wavecenter_data_room_month`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `wavecenter_data_room_month` (
  `id` bigint(20) NOT NULL,
  `app_id` int(11) NOT NULL COMMENT '业务',
  `stat_year` int(11) NOT NULL COMMENT '年 格式:YYYY',
  `stat_month` int(11) NOT NULL COMMENT '月  格式: YYYYMM',
  `room_id` bigint(20) NOT NULL COMMENT '厅主ID',
  `income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '厅收入',
  `charm` int(11) NOT NULL DEFAULT '0' COMMENT '厅魅力值',
  `open_duration` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '开播时长(分钟)',
  `sign_player_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '厅签约主播数',
  `income_player_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '有收入主播人数',
  `income_player_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '有收入主播占比',
  `player_avg_income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '人均收入',
  `player_avg_charm` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '人均魅力值',
  `gift_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '送礼人数',
  `gift_user_price` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '送礼客单价',
  `chat_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '私信人数',
  `reply_chat_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '私信回复人数',
  `chat_enter_room_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '私信进房人数',
  `chat_gift_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '私信付费人数',
  `reply_chat_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '私信回复率',
  `chat_enter_room_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '私信进房率',
  `chat_gift_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '私信付费率',
  `invite_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '邀请人数',
  `invite_enter_room_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '邀请进房人数',
  `invite_gift_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '邀请付费人数',
  `invite_enter_room_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '邀请进房率',
  `invite_gift_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '邀请付费率',
  `fans_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '厅粉丝数',
  `new_fans_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '厅新增粉丝',
  `player_fans_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '主播粉丝',
  `player_new_fans_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '主播新增粉丝',
  `player_fans_enter_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '主播粉丝进房数',
  `player_fans_gift_income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '主播粉丝送礼收入',
  `player_fans_gift_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '主播粉丝送礼人数',
  `player_fans_gift_user_price` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '主播粉丝送礼客单价',
  `enter_room_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '进房人数',
  `up_guest_player_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '上麦人数',
  `sign_up_guest_player_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '直播间签约主播上麦人数',
  `comment_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '评论人数',
  `up_guest_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '上麦率',
  `comment_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '评论互动率',
  `avg_user_stay_duration` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '人均逗留时长(分钟)',
  `user_full_one_min` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '用户逗留人数(满1分钟)',
  `user_full_three_min` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '用户逗留人数(满3分钟)',
  `user_full_five_min` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '用户逗留人数(满5分钟)',
  `income_rank` int(11) NOT NULL DEFAULT '0' COMMENT '收入在公会的排名',
  `charm_rank` int(11) NOT NULL DEFAULT '0' COMMENT '魅力值在公会的排名',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '角色创建时间',
  `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '角色修改时间',
  `all_income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '总收入',
  `sign_hall_income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '签约厅收礼收入',
  `official_hall_income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '官方厅收礼收入',
  `personal_hall_income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '个播收礼收入',
  `noble_income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '贵族提成收入',
  `personal_noble_income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '个播贵族提成收入',
  `up_player_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '主播上麦率',
  `comment_one_min_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '厅评论互动率=评论人数/房间逗留人数(满1分钟)',
  `up_guest_one_min_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '厅嘉宾上麦率=上麦嘉宾数/房间逗留人数(满1分钟)',
  `gift_one_min_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '厅付费转化率(1min)',
  `gift_three_min_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '厅付费转化率(3min)',
  `gift_five_min_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '厅付费转化率(5min)',
  `chat_player_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '私信主播数 本厅签约主播中有私信行为的人数',
  PRIMARY KEY (`id`),
  KEY `idx_base` (`app_id`,`room_id`,`stat_month`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='大数据厅月统计表-厅维度';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `wavecenter_data_room_week`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `wavecenter_data_room_week` (
  `id` bigint(20) NOT NULL,
  `app_id` int(11) NOT NULL COMMENT '业务',
  `start_week_date` date NOT NULL COMMENT '日期 格式 YYYY-MM-DD',
  `end_week_date` date NOT NULL COMMENT '日期 格式 YYYY-MM-DD',
  `room_id` bigint(20) NOT NULL COMMENT '厅主ID',
  `income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '厅收入',
  `charm` int(11) NOT NULL DEFAULT '0' COMMENT '厅魅力值',
  `open_duration` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '开播时长(分钟)',
  `sign_player_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '厅签约主播数',
  `income_player_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '有收入主播人数',
  `income_player_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '有收入主播占比',
  `player_avg_income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '人均收入',
  `player_avg_charm` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '人均魅力值',
  `gift_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '送礼人数',
  `gift_user_price` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '送礼客单价',
  `chat_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '私信人数',
  `reply_chat_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '私信回复人数',
  `chat_enter_room_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '私信进房人数',
  `chat_gift_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '私信付费人数',
  `reply_chat_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '私信回复率',
  `chat_enter_room_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '私信进房率',
  `chat_gift_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '私信付费率',
  `invite_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '邀请人数',
  `invite_enter_room_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '邀请进房人数',
  `invite_gift_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '邀请付费人数',
  `invite_enter_room_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '邀请进房率',
  `invite_gift_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '邀请付费率',
  `fans_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '厅粉丝数',
  `new_fans_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '厅新增粉丝',
  `player_fans_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '主播粉丝',
  `player_new_fans_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '主播新增粉丝',
  `player_fans_enter_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '主播粉丝进房数',
  `player_fans_gift_income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '主播粉丝送礼收入',
  `player_fans_gift_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '主播粉丝送礼人数',
  `player_fans_gift_user_price` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '主播粉丝送礼客单价',
  `enter_room_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '进房人数',
  `up_guest_player_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '上麦人数',
  `sign_up_guest_player_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '直播间签约主播上麦人数',
  `comment_user_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '评论人数',
  `up_guest_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '上麦率',
  `comment_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '评论互动率',
  `avg_user_stay_duration` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '人均逗留时长(分钟)',
  `user_full_one_min` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '用户逗留人数(满1分钟)',
  `user_full_three_min` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '用户逗留人数(满3分钟)',
  `user_full_five_min` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '用户逗留人数(满5分钟)',
  `income_rank` int(11) NOT NULL DEFAULT '0' COMMENT '收入在公会的排名',
  `charm_rank` int(11) NOT NULL DEFAULT '0' COMMENT '魅力值在公会的排名',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '角色创建时间',
  `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '角色修改时间',
  `all_income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '总收入',
  `sign_hall_income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '签约厅收礼收入',
  `official_hall_income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '官方厅收礼收入',
  `personal_hall_income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '个播收礼收入',
  `noble_income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '贵族提成收入',
  `personal_noble_income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '个播贵族提成收入',
  `up_player_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '主播上麦率',
  `comment_one_min_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '厅评论互动率=评论人数/房间逗留人数(满1分钟)',
  `up_guest_one_min_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '厅嘉宾上麦率=上麦嘉宾数/房间逗留人数(满1分钟)',
  `gift_one_min_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '厅付费转化率(1min)',
  `gift_three_min_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '厅付费转化率(3min)',
  `gift_five_min_rate` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '厅付费转化率(5min)',
  `chat_player_cnt` int(11) NOT NULL DEFAULT '0' COMMENT '私信主播数 本厅签约主播中有私信行为的人数',
  PRIMARY KEY (`id`),
  KEY `idx_base` (`app_id`,`room_id`,`start_week_date`,`end_week_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='大数据厅周统计表-厅维度';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `wavecenter_evaluate_record`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `wavecenter_evaluate_record` (
  `id` bigint(20) NOT NULL,
  `app_id` int(11) NOT NULL COMMENT '业务',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `evaluate_tag` varchar(24) NOT NULL COMMENT '评分唯一标识',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_c` (`app_id`,`user_id`,`evaluate_tag`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='推荐评分记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `wavecenter_file_export_record`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `wavecenter_file_export_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `app_id` int(11) NOT NULL COMMENT '业务',
  `file_status` int(11) NOT NULL DEFAULT '1' COMMENT '文件状态 1: 导出中, 2: 完成导出, 3=导出失败',
  `file_path` varchar(255) DEFAULT NULL COMMENT '文件路径',
  `file_name` varchar(100) DEFAULT NULL COMMENT '文件名',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `modify_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  `user_id` bigint(20) NOT NULL COMMENT '文件所属用户ID',
  `deleted` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0=未删除, 1=已删除',
  PRIMARY KEY (`id`),
  KEY `idx_q` (`app_id`,`user_id`,`create_time`)
) ENGINE=InnoDB AUTO_INCREMENT=5415769330151507584 DEFAULT CHARSET=utf8mb4 COMMENT='文件导出记录';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `wavecenter_flow_family_nj`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `wavecenter_flow_family_nj` (
  `id` bigint(20) NOT NULL,
  `app_id` int(11) NOT NULL COMMENT '业务',
  `flow_date` datetime NOT NULL COMMENT '流水时间',
  `family_id` bigint(20) NOT NULL COMMENT '公会ID',
  `nj_id` bigint(20) NOT NULL COMMENT '厅主ID',
  `nj_band` varchar(64) NOT NULL COMMENT '厅主波段号',
  `nj_name` varchar(255) NOT NULL COMMENT '厅主昵称',
  `biz_id` int(11) NOT NULL COMMENT '收入类型ID',
  `biz_name` varchar(64) NOT NULL COMMENT '收入类型名称',
  `income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '收入',
  `remark` varchar(255) NOT NULL DEFAULT '' COMMENT '流水内容',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`),
  KEY `idx_affb` (`app_id`,`flow_date`,`family_id`,`biz_id`),
  KEY `idx_affnb` (`app_id`,`flow_date`,`family_id`,`nj_id`,`biz_id`),
  KEY `idx_afnd` (`app_id`,`family_id`,`nj_id`,`flow_date`),
  KEY `idx_alf` (`app_id`,`family_id`,`flow_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='支付流水表-厅&公会维度';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `wavecenter_flow_user`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `wavecenter_flow_user` (
  `id` bigint(20) NOT NULL,
  `app_id` int(11) NOT NULL COMMENT '业务',
  `flow_date` datetime NOT NULL COMMENT '流水时间',
  `user_id` bigint(20) NOT NULL COMMENT '主播ID',
  `biz_id` int(11) NOT NULL COMMENT '收入类型ID',
  `biz_name` varchar(64) CHARACTER SET utf8mb4 NOT NULL COMMENT '收入类型名称',
  `income` decimal(12,4) NOT NULL DEFAULT '0.0000' COMMENT '收入',
  `remark` varchar(255) CHARACTER SET utf8mb4 NOT NULL DEFAULT '' COMMENT '流水内容',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  `income_amount` decimal(12,4) DEFAULT NULL COMMENT '收益额度（西米+小陪伴独有）',
  PRIMARY KEY (`id`),
  KEY `idx_afu` (`app_id`,`flow_date`,`user_id`),
  KEY `idx_afub` (`app_id`,`flow_date`,`user_id`,`biz_id`),
  KEY `idx_lf` (`app_id`,`user_id`,`flow_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='支付流水表-主播维度';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `wavecenter_menu`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `wavecenter_menu` (
  `id` bigint(20) NOT NULL,
  `menu_name` varchar(32) NOT NULL COMMENT '菜单名称',
  `menu_code` varchar(32) NOT NULL COMMENT '菜单编码',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '角色创建时间',
  `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '角色修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_c` (`menu_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='菜单表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `wavecenter_player_check_in_day_stats`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `wavecenter_player_check_in_day_stats` (
  `id` bigint(11) NOT NULL,
  `app_id` int(11) DEFAULT NULL,
  `user_id` bigint(11) NOT NULL COMMENT '主播ID',
  `income` decimal(14,2) NOT NULL DEFAULT '0.00',
  `nj_id` bigint(11) DEFAULT NULL COMMENT '打卡所在的厅主ID',
  `charm` bigint(11) NOT NULL DEFAULT '0',
  `seat_order` int(4) DEFAULT NULL COMMENT '麦序',
  `host_num` int(4) DEFAULT NULL COMMENT '主持次数',
  `up_guest_dur` int(5) DEFAULT NULL COMMENT '上麦时长（单位分钟）',
  `stat_date` date DEFAULT NULL COMMENT '统计天',
  `create_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `user_id_stat_date` (`user_id`,`stat_date`) USING BTREE,
  KEY `nj_id_stat_date` (`nj_id`,`stat_date`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `wavecenter_player_sign_charm_stat`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `wavecenter_player_sign_charm_stat` (
  `id` bigint(20) NOT NULL COMMENT '主键ID',
  `app_id` bigint(20) NOT NULL COMMENT '业务',
  `family_id` bigint(20) NOT NULL COMMENT '公会ID',
  `room_id` bigint(20) NOT NULL COMMENT '房主id',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `value` bigint(20) NOT NULL DEFAULT '0' COMMENT '魅力值',
  `stat_day` date NOT NULL COMMENT '统计日期',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `u_idx_1` (`app_id`,`family_id`,`room_id`,`user_id`,`stat_day`),
  KEY `idx_s` (`stat_day`),
  KEY `idx_nj` (`app_id`,`room_id`,`user_id`,`stat_day`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='签约陪玩魅力值统计';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `wavecenter_role`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `wavecenter_role` (
  `id` bigint(20) NOT NULL,
  `role_name` varchar(32) NOT NULL COMMENT '角色名称',
  `role_code` varchar(32) NOT NULL COMMENT '角色编码',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '角色创建时间',
  `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '角色修改时间',
  `level` tinyint(4) NOT NULL DEFAULT '1' COMMENT '优先级',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_c` (`role_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `wavecenter_role_auth_ref`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `wavecenter_role_auth_ref` (
  `id` bigint(20) NOT NULL,
  `app_id` int(11) NOT NULL COMMENT '业务线',
  `user_id` bigint(20) NOT NULL COMMENT '被授权用户ID',
  `role_code` varchar(32) NOT NULL COMMENT '角色编码',
  `subject_id` bigint(20) NOT NULL COMMENT '授权范围主体ID, role_code=家族长, 该字段为family_id; role_code=厅长, 该字段为厅主ID',
  `subject_user_id` bigint(20) NOT NULL COMMENT '授权范围主体用户ID, role_code=家族长, 该字段为家族长用户ID; role_code=厅长, 该字段为厅主ID',
  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0=禁用, 1=启用',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  `deleted` tinyint(4) DEFAULT NULL COMMENT '0=未删除，null表示已删除',
  `create_user_id` bigint(20) DEFAULT NULL COMMENT '创建用户ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_only` (`app_id`,`user_id`,`role_code`,`subject_id`,`create_user_id`,`deleted`),
  KEY `idx_au` (`app_id`,`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色授权配置表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `wavecenter_role_component`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `wavecenter_role_component` (
  `id` bigint(20) NOT NULL,
  `component_code` varchar(180) NOT NULL COMMENT '组件编码',
  `role_code` varchar(32) NOT NULL COMMENT '角色编码',
  `app_id` int(11) NOT NULL DEFAULT '0' COMMENT '业务线',
  `permission_type` tinyint(4) NOT NULL DEFAULT '1' COMMENT '权限类型：1=写，2=读',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '角色创建时间',
  `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '角色修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_arm` (`app_id`,`role_code`,`component_code`,`permission_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色组件关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `wavecenter_role_menu`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `wavecenter_role_menu` (
  `id` bigint(20) NOT NULL,
  `menu_code` varchar(180) NOT NULL COMMENT '菜单编码',
  `role_code` varchar(32) NOT NULL COMMENT '角色编码',
  `app_id` int(11) NOT NULL DEFAULT '0' COMMENT '业务线',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '角色创建时间',
  `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '角色修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_arm` (`app_id`,`role_code`,`menu_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色菜单关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `wavecenter_test`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `wavecenter_test` (
  `id` bigint(20) NOT NULL,
  `test_name` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='测试';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `wavecenter_user_page_config`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `wavecenter_user_page_config` (
  `id` bigint(20) NOT NULL,
  `app_id` int(11) NOT NULL COMMENT '业务',
  `user_id` bigint(20) NOT NULL COMMENT '主播ID',
  `page_code` varchar(128) NOT NULL COMMENT '页面code',
  `config` text NOT NULL COMMENT '配置json',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_un` (`app_id`,`user_id`,`page_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户页面配置';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `waveconter_room_check_in_day_stats`
--

/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `waveconter_room_check_in_day_stats` (
  `id` bigint(11) NOT NULL,
  `app_id` int(11) NOT NULL,
  `family_id` bigint(11) NOT NULL COMMENT '家族ID',
  `nj_id` bigint(11) NOT NULL COMMENT '厅主ID',
  `income` decimal(14,2) NOT NULL COMMENT '收入',
  `charm` bigint(11) NOT NULL COMMENT '魅力值',
  `income_player_num` int(8) NOT NULL DEFAULT '0' COMMENT '有收入主播数',
  `check_in_player` bigint(11) NOT NULL DEFAULT '0' COMMENT '打卡主播数',
  `seat_order` bigint(20) DEFAULT '0' COMMENT '打卡麦序',
  `stat_date` date DEFAULT NULL,
  `create_time` datetime DEFAULT NULL,
  `host_num` int(4) DEFAULT '0' COMMENT '主持档',
  PRIMARY KEY (`id`),
  KEY `family_stat_date_index` (`family_id`,`stat_date`) USING BTREE,
  KEY `nj_start_date_index` (`nj_id`,`stat_date`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2024-11-14 10:19:11
