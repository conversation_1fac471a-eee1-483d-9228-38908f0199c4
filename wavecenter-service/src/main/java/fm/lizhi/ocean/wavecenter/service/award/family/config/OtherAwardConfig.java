package fm.lizhi.ocean.wavecenter.service.award.family.config;

import lombok.Data;

import java.util.Collections;
import java.util.List;

/**
 * 其他奖励规则配置
 */
@Data
public class OtherAwardConfig {

    /**
     * 公会等级推荐卡配置, 配置时必须保证顺序, 按照公会等级从高到低的顺序配置
     */
    private List<LevelRecommendCardConfig> levelRecommendCardConfigs = Collections.emptyList();

    /**
     * 流水环比推荐卡配置, 配置时必须保证顺序, 按照公会等级从高到低的顺序配置
     */
    private List<FlowRecommendCardConfig> flowRecommendCardConfigs = Collections.emptyList();

    /**
     * 新厅留存推荐卡配置, 配置时必须保证顺序, 按照留存新厅数从高到低的顺序配置
     */
    private List<NewRoomRecommendCardConfig> newRoomRecommendCardConfigs = Collections.emptyList();

    /**
     * 零流失厅推荐卡配置
     */
    private List<ZeroLostRoomRecommendCardConfig> zeroLostRoomRecommendCardConfigs = Collections.emptyList();

    /**
     * 公会等级新厅名额配置
     */
    private List<LevelNewRoomConfig> levelNewRoomConfigs = Collections.emptyList();

    /**
     * 流水涨幅新厅名额配置, 配置时必须保证顺序, 按照周涨幅从高到低的顺序配置
     */
    private List<FlowGrowthNewRoomConfig> flowGrowthNewRoomConfigs = Collections.emptyList();

    /**
     * 流失厅新厅名额配置, 配置时必须保证顺序, 按照流失厅数量从高到低的顺序配置
     */
    private List<LostRoomNewRoomConfig> lostRoomNewRoomConfigs = Collections.emptyList();

    /**
     * 新厅留存新厅名额配置, 配置时必须保证顺序, 按照留存新厅数从高到低的顺序配置
     */
    private List<NewRoomRetainNewRoomConfig> newRoomRetainNewRoomConfigs = Collections.emptyList();
}
