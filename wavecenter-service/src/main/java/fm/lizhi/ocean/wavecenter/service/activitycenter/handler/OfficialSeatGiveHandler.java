package fm.lizhi.ocean.wavecenter.service.activitycenter.handler;

import com.alibaba.fastjson.JSONObject;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.OfficialSeatExtraBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.AutoConfigResourceEnum;
import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ResourceExtraMapping;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.common.constants.TimeConstant;
import fm.lizhi.ocean.wavecenter.service.activitycenter.constatns.ResourceGiveErrorTipConstant;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.*;
import fm.lizhi.ocean.wavecenter.service.activitycenter.manager.ActivityMaterielManager;
import fm.lizhi.ocean.wavecenter.service.activitycenter.processor.IOfficialSeatGiveProcess;
import fm.lizhi.ocean.wavecenter.service.common.processor.ProcessorFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Date;

/**
 * 官频位资源发放处理器
 */
@Slf4j
@Component
public class OfficialSeatGiveHandler implements FlowResourceGiveHandler {

    @Autowired
    private ActivityMaterielManager activityMaterielManager;

    @Autowired
    private ProcessorFactory factory;

    @Override
    public Result<GiveFlowResourceResDTO> giveFlowResource(FlowResourceContext context) {
        try {
            ActivityFlowResourceGiveDTO flowResourceGiveDTO = context.getFlowResourceGiveDTO();
            ActivityResourceGiveDTO resourceGiveDTO = context.getResourceGiveDTO();

            String extra = flowResourceGiveDTO.getExtra();
            if (extra == null) {
                log.warn("saveOfficialSeat extra is null. activityId={}, resourceId={}", resourceGiveDTO.getActivityId(), resourceGiveDTO.getResourceId());
                return RpcResult.fail(GIVE_FLOW_RESOURCE_FAIL, ResourceGiveErrorTipConstant.OFFICIAL_SEAT_GIVE_CONFIG_EXCEPTION);
            }
            OfficialSeatExtraBean extraBean = ResourceExtraMapping.convertJsonToExtra(extra, AutoConfigResourceEnum.OFFICIAL_SEAT.getResourceCode());
            if (extraBean == null || extraBean.getStartTime() == null || extraBean.getEndTime() == null || extraBean.getSeat() == null) {
                log.warn("saveOfficialSeat extra info is null. activityId={}, resourceId={}, extra={}", resourceGiveDTO.getActivityId(), resourceGiveDTO.getResourceId(), extra);
                return RpcResult.fail(GIVE_FLOW_RESOURCE_FAIL, ResourceGiveErrorTipConstant.OFFICIAL_SEAT_GIVE_CONFIG_EXCEPTION);
            }

            //获取差异化处理器
            IOfficialSeatGiveProcess processor = factory.getProcessor(IOfficialSeatGiveProcess.class);
            SaveOfficialSeatParamDTO paramDTO = buildSeatParamDTO(context, extraBean);
            //补全所需参数
            processor.fillOfficialSeatParam(context, paramDTO, extraBean);

            log.info("saveOfficialSeat param. activityId={}, requestParam={}", resourceGiveDTO.getActivityId(), JSONObject.toJSONString(paramDTO));
            Result<SaveOfficialSeatResDTO> result = activityMaterielManager.saveOfficialSeat(paramDTO);
            if (RpcResult.isFail(result)) {
                log.warn("saveOfficialSeat fail. rCode={}, activityId={}, resourceId={}", result.rCode(), resourceGiveDTO.getActivityId(), resourceGiveDTO.getResourceId());
                return RpcResult.fail(GIVE_FLOW_RESOURCE_FAIL, ResourceGiveErrorTipConstant.OFFICIAL_SEAT_GIVE_FAIL_EXCEPTION);
            }

            log.info("saveOfficialSeat success. activityId={}, resultMsg={}", resourceGiveDTO.getActivityId(), result.target().getMsg());
            //构建结果信息
            return result.target().isSuccess() ? RpcResult.success(new GiveFlowResourceResDTO().setBizRecordId(result.target().getBizRecordId())) : RpcResult.fail(GIVE_FLOW_RESOURCE_FAIL, result.target().getMsg());
        } catch (Exception e) {
            log.error("OfficialSeatGiveHandler.giveFlowResource happen error: context={}", JSONObject.toJSONString(context), e);
            return RpcResult.fail(GIVE_FLOW_RESOURCE_FAIL, ResourceGiveErrorTipConstant.OFFICIAL_SEAT_GIVE_FAIL_EXCEPTION);
        }
    }

    @Override
    public String getResourceCode() {
        return AutoConfigResourceEnum.OFFICIAL_SEAT.getResourceCode();
    }

    private SaveOfficialSeatParamDTO buildSeatParamDTO(FlowResourceContext context, OfficialSeatExtraBean extraBean) {
        ActivityResourceGiveDTO resourceGiveDTO = context.getResourceGiveDTO();
        ActivityFlowResourceGiveDTO flowResourceGiveDTO = context.getFlowResourceGiveDTO();

        flowResourceGiveDTO.setImageUrl(flowResourceGiveDTO.getImageUrl() == null ? "" : StringUtils.removeStart(flowResourceGiveDTO.getImageUrl(), "/"));
        if (extraBean.getEndTime() != null) {
            //结束时间减少一秒
            long endTimeMill = extraBean.getEndTime().getTime() - TimeConstant.ONE_SECOND_MILLISECOND;
            extraBean.setEndTime(new Date(endTimeMill));
        }

        SaveOfficialSeatParamDTO paramDTO = new SaveOfficialSeatParamDTO();
        paramDTO.setName(resourceGiveDTO.getActivityName())
                .setStartTime(extraBean.getStartTime())
                .setEndTime(extraBean.getEndTime())
                .setBackgroundUrl(flowResourceGiveDTO.getImageUrl())
                .setPosition(extraBean.getSeat())
                .setUserIds(Collections.singletonList(flowResourceGiveDTO.getUserId()));
        return paramDTO;
    }

    @Override
    public Result<Void> cancelGiveFlowResource(DeleteOfficialSeatParamDTO param) {
        return activityMaterielManager.deleteOfficialSeat(param);
    }
}
