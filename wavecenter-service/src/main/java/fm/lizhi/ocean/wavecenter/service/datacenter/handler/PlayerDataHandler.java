package fm.lizhi.ocean.wavecenter.service.datacenter.handler;

import fm.lizhi.ocean.wavecenter.api.datacenter.bean.PlayerGetKeyIndicatorsParamBean;
import fm.lizhi.ocean.wavecenter.service.datacenter.dto.DataPlayerDayDTO;
import fm.lizhi.ocean.wavecenter.service.datacenter.dto.DataPlayerRoomDayDTO;
import fm.lizhi.ocean.wavecenter.service.datacenter.dto.GetPlayerRoomDayParam;
import fm.lizhi.ocean.wavecenter.service.datacenter.manager.PlayerDataManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/4/21 20:54
 */
@Component
public class PlayerDataHandler extends AbsDataHandler<PlayerGetKeyIndicatorsParamBean, Object>{

    @Autowired
    private PlayerDataManager playerDataManager;

    @Override
    protected Map<String, String> getDayKeyIndicators(PlayerGetKeyIndicatorsParamBean paramBean, Date day, List<String> queryValueMetrics) {
        Integer appId = paramBean.getAppId();
        Long playerId = paramBean.getPlayerId();
        Long familyId = paramBean.getFamilyId();
        Long roomId = paramBean.getRoomId();
        return playerDataManager.getDayKeyIndicators(appId, familyId, roomId, playerId, day, queryValueMetrics);
    }

    @Override
    protected Map<String, String> getWeekKeyIndicators(PlayerGetKeyIndicatorsParamBean paramBean, Date startDay, Date endDay, List<String> queryValueMetrics) {
        Integer appId = paramBean.getAppId();
        Long playerId = paramBean.getPlayerId();
        Long familyId = paramBean.getFamilyId();
        Long roomId = paramBean.getRoomId();
        return playerDataManager.getWeekKeyIndicators(appId, familyId, roomId, playerId, startDay, endDay, queryValueMetrics);
    }

    @Override
    protected Map<String, String> getMonthKeyIndicators(PlayerGetKeyIndicatorsParamBean paramBean, Date month, List<String> queryValueMetrics) {
        Integer appId = paramBean.getAppId();
        Long playerId = paramBean.getPlayerId();
        Long familyId = paramBean.getFamilyId();
        Long roomId = paramBean.getRoomId();
        return playerDataManager.getMonthKeyIndicators(appId, familyId, roomId, playerId, month, queryValueMetrics);
    }

    @Override
    protected Map<Integer, Object> getDaysData(IndicatorTrendParam paramBean, List<String> metrics, List<Integer> dayValues) {
        Long familyId = paramBean.getFamilyId();
        Long roomId = paramBean.getRoomId();
        Long playerId = paramBean.getPlayerId();

        Map<Integer, Object> valueMap;

        GetPlayerRoomDayParam param = new GetPlayerRoomDayParam();
        param.setDayValues(dayValues).setPlayerId(playerId);

        if (familyId == null && roomId == null) {
            List<DataPlayerDayDTO> dtoList = playerDataManager.getPlayerDayList(param);
            valueMap = dtoList.stream().collect(Collectors.toMap(DataPlayerDayDTO::getStatDateValue, v -> v, (k1, k2) -> k2));
        } else {
            param.setFamilyId(familyId);
            param.setRoomId(roomId);
            List<DataPlayerRoomDayDTO> dtoList = playerDataManager.getPlayerRoomDayList(param);
            valueMap = dtoList.stream().collect(Collectors.toMap(DataPlayerRoomDayDTO::getStatDateValue, v -> v, (k1, k2) -> k2));
        }

        return valueMap;
    }

}
