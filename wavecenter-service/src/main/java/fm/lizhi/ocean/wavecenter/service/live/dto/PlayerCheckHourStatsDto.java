package fm.lizhi.ocean.wavecenter.service.live.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 陪玩小时统计指标
 * <AUTHOR>
 * @date 2024/6/11 17:58
 */
@Data
@Accessors(chain = true)
public class PlayerCheckHourStatsDto {

    /**
     * 档期开始时间
     */
    private Date startTime;

    private Date endTime;

    private BigDecimal income;

    private Long charm;

    private Long charmValue;

    /**
     * 是否是主持,0: 不是主持  1：主持
     */
    private Integer isHost;

    /**
     * 0：未打卡，1：已打卡，2：确认打卡
     */
    private Integer checkInStatus;

    /**
     * 打卡厅ID
     */
    private Long roomId;

    /**
     * 打卡备注
     */
    private String remark;

}
