package fm.lizhi.ocean.wavecenter.service.home.impl;

import cn.hutool.core.text.StrPool;
import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.wavecenter.api.home.bean.QuickEntryBean;
import fm.lizhi.ocean.wavecenter.api.home.service.QuickEntryService;
import fm.lizhi.ocean.wavecenter.api.message.constant.MessageTargetLinkEnum;
import fm.lizhi.ocean.wavecenter.service.user.config.UserConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 快捷入口
 * <AUTHOR>
 */
@ServiceProvider
@Slf4j
public class QuickEntryServiceImpl implements QuickEntryService {

    @Autowired
    private UserConfig userConfig;

    @Override
    public Result<List<QuickEntryBean>> getQuickEntryList(String roleCode) {
        Map<String, String> quickEntry = userConfig.getQuickEntry();

        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, Arrays.stream(quickEntry.get(roleCode).split(StrPool.COMMA))
                .map(e -> {
                    // 判断e 在不在枚举中
                    if (MessageTargetLinkEnum.contains(e)){
                        return new QuickEntryBean().setLinkType(e);
                    }
                    log.warn("quick entry is fail. e is not in MessageTargetLinkEnum. e: {}", e);
                    return null;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList())
        );
    }
}
