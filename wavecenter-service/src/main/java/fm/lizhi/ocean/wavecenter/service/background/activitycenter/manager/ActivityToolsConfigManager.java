package fm.lizhi.ocean.wavecenter.service.background.activitycenter.manager;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityToolsInfoBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityToolStatusEnum;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestSaveActivityTools;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestUpdateActivityTools;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface ActivityToolsConfigManager {

    /**
     * 保存活动工具
     */
    Result<Boolean> saveTools(RequestSaveActivityTools param);


    /**
     * 更新活动工具
     */
    Result<Boolean> updateTools(RequestUpdateActivityTools param);

    /**
     * 查询列表
     */
    Result<List<ActivityToolsInfoBean>> listByAppId(Integer appId, ActivityToolStatusEnum status);

    boolean isActivityToolsIllegal(int appId, List<Integer> activityTools);
}
