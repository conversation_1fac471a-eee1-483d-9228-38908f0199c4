package fm.lizhi.ocean.wavecenter.service.grow.handler;

import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wavecenter.domain.grow.service.GrowFamilyLevelSettleService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * 公会等级结算处理器
 * <AUTHOR>
 * @date 2025/3/19 14:12
 */
@Slf4j
@Component
public class FamilyLevelSettleHandler {

    @Autowired
    private GrowFamilyLevelSettleService growFamilyLevelSettleService;

    /**
     * 通过列表结算
     * @param familyIds
     */
    public void settleFamilyByList(List<Long> familyIds, Date startTime, Date endTime){
        log.info("familyIds={}", JsonUtil.dumps(familyIds));
        if (CollectionUtils.isEmpty(familyIds)) {
            log.info("familyIds is empty");
            return;
        }

        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        growFamilyLevelSettleService.settleWeekLevel(appId, familyIds, startTime, endTime);
    }

    /**
     * 满足测试场景
     * @param familyId
     * @param startTime
     * @param endTime
     * @param levelId
     */
    public void settleFamily(Long familyId, Date startTime, Date endTime, Long levelId) {
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        growFamilyLevelSettleService.settleInLevel(appId, familyId, startTime, endTime, levelId);
    }

}
