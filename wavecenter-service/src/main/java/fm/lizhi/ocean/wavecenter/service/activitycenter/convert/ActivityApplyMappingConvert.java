package fm.lizhi.ocean.wavecenter.service.activitycenter.convert;

import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.*;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.ActivityApplyParamDTO;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.ActivityParamDTO;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.ActivitySimpleInfoDT0;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.ActivitySimpleQueryParamDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ActivityApplyMappingConvert {

    ActivityApplyMappingConvert I = Mappers.getMapper(ActivityApplyMappingConvert.class);

    // 自定义映射方法
    @Named("convertAuditStatus")
    default List<Integer> convertAuditStatusToList(Integer value) {
        if (value == null) {
            return Collections.emptyList();
        }
        return Collections.singletonList(value);
    }

    @Mappings({
            @Mapping(target = "startTime", expression = "java(new java.util.Date(activityParamDTO.getStartTime()))"),
            @Mapping(target = "endTime", expression = "java(new java.util.Date(activityParamDTO.getEndTime()))"),
            @Mapping(target = "applyType", expression = "java(activityParamDTO.getApplyType().getApplyType())"),
            @Mapping(target = "auditStatus", expression = "java(fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityAuditStatusEnum.WAITING_AUDIT.getStatus())"),
            @Mapping(target = "auxiliaryPropUrl", ignore = true),
            @Mapping(target = "activityTool", ignore = true),
            @Mapping(target = "deployEnv", expression = "java(com.lizhi.commons.config.core.util.ConfigUtils.getEnvRequired().name())"),
            @Mapping(target = "accompanyNjIds", ignore = true),
            @Mapping(target = "flowResources", ignore = true),
            @Mapping(target = "processList", ignore = true),
            @Mapping(target = "roomAnnouncementImgUrl", ignore = true),

    })
    ActivityApplyParamDTO applyInfoBean2DTO(ActivityParamDTO activityParamDTO);

    /**
     * 运营后台活动审批列表查询参数转换为活动列表查询参数. 特别注意, 运营后台审批列表要能看到未删除和已删除的活动, 因为用户提报不通过后可能会删除活动.
     *
     * @param param 运营后台活动审批列表查询参数
     * @return 活动列表查询参数
     */
    @BeanMapping(unmappedTargetPolicy = ReportingPolicy.ERROR, unmappedSourcePolicy = ReportingPolicy.ERROR,
            ignoreUnmappedSourceProperties = {"classId", "levelId"})
    @Mappings({
            @Mapping(target = "pageNo", source = "param.pageParam.pageNo"),
            @Mapping(target = "pageSize", source = "param.pageParam.pageSize"),
            @Mapping(target = "applyUserId", ignore = true),
            @Mapping(target = "familyId", ignore = true),
            @Mapping(target = "deleted", constant = "0"),
            @Mapping(target = "classIds", ignore = true)
    })
    ActivitySimpleQueryParamDTO queryActivityListReq2DTO(RequestQueryActivityListBean param);

    default Date longToDate(Long time) {
        return time != null && time > 0 ? new Date(time) : null;
    }

    default Long dateToLong(Date date) {
        return date != null ? date.getTime() : null;
    }


    List<ActivitySimpleInfoBean> activitySimpleDTOs2Beans(List<ActivitySimpleInfoDT0> infoDTOs);

    @Mappings({
        @Mapping(target = "applyUserInfo.id", source = "applicantUid"),
        @Mapping(target = "hostInfo.id", source = "hostId"),
        @Mapping(target = "applyTime", expression = "java(infoDTO.getCreateTime().getTime())"),
    })
    ActivitySimpleInfoBean activitySimpleDTO2Bean(ActivitySimpleInfoDT0 infoDTO);

    /**
     * 创作服务中心查询用户活动日历列表请求参数转换为活动列表查询参数
     *
     * @param requestQueryUserActivities 创作服务中心查询用户活动日历列表请求参数
     * @return 活动列表查询参数
     */
    @BeanMapping(unmappedTargetPolicy = ReportingPolicy.ERROR, unmappedSourcePolicy = ReportingPolicy.ERROR,
            ignoreUnmappedSourceProperties = {"njBrand", "applyBrand", "activityStatus", "classId"})
    @Mappings({
            @Mapping(target = "pageNo", source = "requestQueryUserActivities.pageParam.pageNo"),
            @Mapping(target = "pageSize", source = "requestQueryUserActivities.pageParam.pageSize"),
            @Mapping(target = "auditStatus", source = "auditStatus", qualifiedByName = "convertAuditStatus"),
            @Mapping(target = "applyStartTime", source = "applyStartTime"),
            @Mapping(target = "applyEndTime", source = "applyEndTime"),
            @Mapping(target = "applyUserId", ignore = true),
            @Mapping(target = "deleted", constant = "0"),
            @Mapping(target = "njId", ignore = true),
            @Mapping(target = "templateName", ignore = true),
            @Mapping(target = "classIds", source = "requestQueryUserActivities", qualifiedByName = "convertClassId"),
            @Mapping(target = "activityId", ignore = true)

        })
    ActivitySimpleQueryParamDTO queryUserActivitiesReq2DTO(RequestQueryUserActivitiesBean requestQueryUserActivities);

    @Named("convertClassId")
    default List<Long> convertClassId(RequestQueryUserActivitiesBean requestQueryUserActivities) {
        List<Long> classIds = new ArrayList<>();
        if (requestQueryUserActivities.getClassId() != null) {
            classIds.add(requestQueryUserActivities.getClassId());
        }
        if (CollectionUtils.isNotEmpty(requestQueryUserActivities.getClassIds())) {
            classIds.addAll(requestQueryUserActivities.getClassIds());
        }
        return classIds;
    }

    @Mappings({
            @Mapping(target = "endTime", expression = "java(infoDTO.getEndTime().getTime())"),
            @Mapping(target = "startTime", expression = "java(infoDTO.getStartTime().getTime())"),
            @Mapping(target = "createTime", expression = "java(infoDTO.getCreateTime().getTime())"),
    })
    UserActivitySimpleInfoBean userActivitySimpleInfoDTO2Bean(ActivitySimpleInfoDT0 infoDTO);

    List<UserActivitySimpleInfoBean> userActivitySimpleInfoDTOs2Beans(List<ActivitySimpleInfoDT0> infoDTOs);
}
