package fm.lizhi.ocean.wavecenter.service.live.impl;

import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.live.bean.LiveSmsPlayerParamBean;
import fm.lizhi.ocean.wavecenter.api.live.bean.LiveSmsRoomParamBean;
import fm.lizhi.ocean.wavecenter.api.live.bean.PlayerSmsStatBean;
import fm.lizhi.ocean.wavecenter.api.live.bean.RoomSmsStatBean;
import fm.lizhi.ocean.wavecenter.api.live.service.LiveSmsService;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserBean;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.common.utils.ResultHandler;
import fm.lizhi.ocean.wavecenter.service.live.manager.LiveSmsManager;
import fm.lizhi.ocean.wavecenter.service.user.dto.SimpleUserDto;
import fm.lizhi.ocean.wavecenter.service.sign.manager.FamilyManager;
import fm.lizhi.ocean.wavecenter.service.user.manager.UserManager;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 私信分析
 * <AUTHOR>
 * @date 2024/4/20 17:06
 */
@ServiceProvider
public class LiveSmsServiceImpl implements LiveSmsService {

    @Autowired
    private UserManager userManager;
    @Autowired
    private FamilyManager familyManager;
    @Autowired
    private LiveSmsManager liveSmsManager;

    /**
     * 厅列表
     * @param paramBean
     * @return
     */
    @Override
    public Result<PageBean<RoomSmsStatBean>> roomList(LiveSmsRoomParamBean paramBean) {
        LogContext.addReqLog("paramBean={}", JsonUtil.dumps(paramBean));
        LogContext.addResLog("paramBean={}", JsonUtil.dumps(paramBean));
        return ResultHandler.handle(paramBean.getAppId(), ()->{
            Long roomId = paramBean.getRoomId();
            Long familyId = paramBean.getFamilyId();

            PageBean<RoomSmsStatBean> pageBean = liveSmsManager.roomList(familyId, roomId, paramBean);
            List<RoomSmsStatBean> list = pageBean.getList();

            //厅主信息
            List<Long> roomIds = list.stream().map(v -> v.getRoomInfo().getId()).collect(Collectors.toList());
            Map<Long, SimpleUserDto> userInfoMap = userManager.getSimpleUserMapByIds(roomIds);

            for (RoomSmsStatBean roomSmsStatBean : list) {
                UserBean roomInfo = roomSmsStatBean.getRoomInfo();
                SimpleUserDto userDto = userInfoMap.get(roomInfo.getId());
                if (userDto == null) {
                    continue;
                }
                roomInfo.setName(userDto.getName());
                roomInfo.setBand(userDto.getBand());
            }

            return RpcResult.success(pageBean);
        });
    }

    @Override
    public Result<PageBean<PlayerSmsStatBean>> playerList(LiveSmsPlayerParamBean paramBean) {
        LogContext.addReqLog("paramBean={}", JsonUtil.dumps(paramBean));
        LogContext.addResLog("paramBean={}", JsonUtil.dumps(paramBean));
        return ResultHandler.handle(paramBean.getAppId(), ()->{
            Long roomId = paramBean.getRoomId();
            Long playerId = paramBean.getPlayerId();
            Long familyId = paramBean.getFamilyId();

            PageBean<PlayerSmsStatBean> pageList = liveSmsManager.playerList(familyId, roomId, playerId, paramBean);

            List<PlayerSmsStatBean> list = pageList.getList();
            List<Long> userIds = new ArrayList<>();
            for (PlayerSmsStatBean smsStatBean : list) {
                UserBean roomInfo = smsStatBean.getRoomInfo();
                if (roomInfo != null) {
                    userIds.add(roomInfo.getId());
                }
                UserBean playerInfo = smsStatBean.getPlayerInfo();
                if (playerInfo != null) {
                    userIds.add(playerInfo.getId());
                }
            }
            Map<Long, SimpleUserDto> userInfoMap = userManager.getSimpleUserMapByIds(userIds);

            for (PlayerSmsStatBean SmsStatBean : list) {
                UserBean roomInfo = SmsStatBean.getRoomInfo();
                if (roomInfo != null) {
                    SimpleUserDto userDto = userInfoMap.get(roomInfo.getId());
                    if (userDto != null) {
                        roomInfo.setName(userDto.getName());
                        roomInfo.setBand(userDto.getBand());
                    }
                }

                UserBean playerInfo = SmsStatBean.getPlayerInfo();
                if (playerInfo != null) {
                    SimpleUserDto userDto = userInfoMap.get(playerInfo.getId());
                    if (userDto != null) {
                        playerInfo.setName(userDto.getName());
                        playerInfo.setBand(userDto.getBand());
                    }
                }
            }

            return RpcResult.success(pageList);
        });
    }
}
