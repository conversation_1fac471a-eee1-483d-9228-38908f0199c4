package fm.lizhi.ocean.wavecenter.service.background.activitycenter.impl;

import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityRuleConfigBean;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestSaveActivityRule;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestUpdateActivityRule;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.service.ActivityRuleConfigService;
import fm.lizhi.ocean.wavecenter.common.utils.ResultHandler;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.manager.ActivityRuleManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 */
@ServiceProvider
@Slf4j
public class ActivityRuleConfigServiceImpl implements ActivityRuleConfigService {

    @Autowired
    private ActivityRuleManager activityRuleManager;


    @Override
    public Result<Void> saveActivityRule(RequestSaveActivityRule param) {
        LogContext.addReqLog("paramBean={}", JsonUtil.dumps(param));
        LogContext.addResLog("paramBean={}", JsonUtil.dumps(param));

        return ResultHandler.handle(param.getAppId(), () -> activityRuleManager.saveActivityRule(param));
    }

    @Override
    public Result<Void> updateActivityRule(RequestUpdateActivityRule param) {
        LogContext.addReqLog("paramBean={}", JsonUtil.dumps(param));
        LogContext.addResLog("paramBean={}", JsonUtil.dumps(param));
        return ResultHandler.handle(param.getAppId(), () -> activityRuleManager.updateActivityRule(param));
    }

    @Override
    public Result<Void> deleteActivityRule(Long id, int appId, String operator) {
        LogContext.addReqLog("paramBean={}", JsonUtil.dumps(id));
        LogContext.addResLog("paramBean={}", JsonUtil.dumps(id));

        return ResultHandler.handle(appId, () -> activityRuleManager.deleteActivityRule(id, operator));
    }

    @Override
    public Result<List<ActivityRuleConfigBean>> listActivityRule(int appId) {
        LogContext.addReqLog("appId={}", JsonUtil.dumps(appId));
        LogContext.addResLog("appId={}", JsonUtil.dumps(appId));
        return ResultHandler.handle(appId, () -> activityRuleManager.listActivityRule(appId));
    }
}
