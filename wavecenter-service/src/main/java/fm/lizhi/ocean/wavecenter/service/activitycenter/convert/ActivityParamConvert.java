package fm.lizhi.ocean.wavecenter.service.activitycenter.convert;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.RequestActivityApplyBean;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestActivityModifyBean;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.ActivityParamDTO;

@Mapper(unmappedTargetPolicy = ReportingPolicy.ERROR)
public interface ActivityParamConvert {

    ActivityParamConvert INSTANCE = Mappers.getMapper(ActivityParamConvert.class);

    /**
     * 转换为活动参数DTO
     * @param requestActivityApplyBean 活动参数请求Bean
     * @return 活动参数DTO
     */
    @Mappings({
            @Mapping(target = "fromBackend", ignore = true),
    })
    ActivityParamDTO convertApplyBeanToDTO(RequestActivityApplyBean requestActivityApplyBean);


    /**
     * 转换为活动参数DTO
     * @param requestActivityModifyBean 活动参数请求Bean
     * @return 活动参数DTO
     */
    @Mappings({
            @Mapping(target = "njId", ignore = true),
    })
    ActivityParamDTO convertModifyBeanToDTO(RequestActivityModifyBean requestActivityModifyBean, boolean fromBackend);

}
