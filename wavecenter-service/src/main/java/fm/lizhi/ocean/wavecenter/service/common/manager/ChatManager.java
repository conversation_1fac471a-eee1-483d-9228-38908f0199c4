package fm.lizhi.ocean.wavecenter.service.common.manager;

import java.util.List;

public interface ChatManager {

    /**
     * 发送私信
     * @param userId 用户ID
     * @param content 文案
     */
    void sendChatAsync(long userId, String content);

    /**
     * 批量发送私信
     * @param receiverUserIdList 接收者用户ID列表
     * @param content 文案
     */
    void batchSendChatAsync(List<Long> receiverUserIdList, String content);

    /**
     * 发送带跳转链接的私信
     * @param userId 用户ID
     * @param content 文案
     * @param url 跳转链接
     */
    void sendChatAsyncWithSkipWeb(long userId, String content, String url);
}
