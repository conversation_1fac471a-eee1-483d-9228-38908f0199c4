package fm.lizhi.ocean.wavecenter.service.home.dto;


import fm.lizhi.ocean.wavecenter.api.home.bean.RoomTrendChartBean;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * 公会大盘规模监控-趋势图
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class GuildMarketMonitorTrendChartDTO {


    /**
     * 日期
     */
    private Date date;


    /**
     * 厅列表
     */
    private List<RoomTrendChartBean> values;
}
