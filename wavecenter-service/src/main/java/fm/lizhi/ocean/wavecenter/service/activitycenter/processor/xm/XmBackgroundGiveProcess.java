package fm.lizhi.ocean.wavecenter.service.activitycenter.processor.xm;

import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.ActivityResourceGiveDTO;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.DressUpGiveContext;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.SendDecorateParamDTO;
import fm.lizhi.ocean.wavecenter.service.activitycenter.processor.IBackgroundGiveProcess;
import org.springframework.stereotype.Component;

@Component
public class XmBackgroundGiveProcess implements IBackgroundGiveProcess {

    @Override
    public void fillSendParam(DressUpGiveContext context, SendDecorateParamDTO param) {
        ActivityResourceGiveDTO resourceGiveDTO = context.getResourceGiveDTO();
        param.setCount(1);
        param.setValidMin((int) (resourceGiveDTO.getEndTime().getTime() - resourceGiveDTO.getStartTime().getTime()) / 1000 / 60);
    }

    @Override
    public BusinessEvnEnum getBusinessEnv() {
        return BusinessEvnEnum.XIMI;
    }
}
