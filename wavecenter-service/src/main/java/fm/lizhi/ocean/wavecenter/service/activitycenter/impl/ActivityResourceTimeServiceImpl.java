package fm.lizhi.ocean.wavecenter.service.activitycenter.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.*;
import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityApplyRuleEnum;
import fm.lizhi.ocean.wavecenter.api.activitycenter.service.ActivityOfficialSeatTimeService;
import fm.lizhi.ocean.wavecenter.api.activitycenter.service.ActivityResourceTimeService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.common.dto.DateDTO;
import fm.lizhi.ocean.wavecenter.common.utils.DateTimeUtils;
import fm.lizhi.ocean.wavecenter.service.activitycenter.config.ActivityConfig;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.ActivityApplyInfoSimpleDTO;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.ActivityOfficialSeatTimeDTO;
import fm.lizhi.ocean.wavecenter.service.activitycenter.manager.ActivityApplyManager;
import fm.lizhi.ocean.wavecenter.service.activitycenter.manager.ActivityOfficialSeatManager;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.manager.ActivityRuleManager;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.manager.ActivityTemplateManager;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.model.dto.ActivityTemplateFlowResourceDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@ServiceProvider
public class ActivityResourceTimeServiceImpl implements ActivityResourceTimeService {


    @Autowired
    private ActivityConfig activityConfig;

    @Autowired
    private ActivityApplyManager activityApplyManager;

    @Autowired
    private ActivityTemplateManager activityTemplateManager;

    @Autowired
    private ActivityRuleManager activityRuleManager;

    @Autowired
    private ActivityOfficialSeatManager activityOfficialSeatManager;



    @Override
    public Result<ResponseGetResourceTimeBean> getResourceTimeList(RequestGetResourceTimeBean request) {
        LogContext.addReqLog("getResourceTimeList.paramBean={}", JsonUtil.dumps(request));
        LogContext.addResLog("getResourceTimeList.paramBean={}", JsonUtil.dumps(request));
        if (request.getStartDate() == null || request.getEndDate() == null) {
            request.setStartDate(fm.lizhi.commons.util.DateUtil.getDayStart(new Date()));
            request.setEndDate(fm.lizhi.commons.util.DateUtil.getDayAfter(request.getStartDate(), activityConfig.getMaxQueryOfficialSeatTimeDayRange()));
        }

        Integer maxPreactApplyDay = activityConfig.getBizConfig(request.getAppId()).getMaxPreactApplyDay();

        if (request.getEndDate().getTime() < request.getStartDate().getTime()){
            log.warn("getResourceTimeList fail, endDate < startDate. endDate:{}, startDate:{}", request.getEndDate(), request.getStartDate());
            return RpcResult.fail(ActivityResourceTimeService.GET_RESOURCE_TIME_LIST, "结束时间不能小于开始时间");
        }

        // 开始时间大于最大提报时间
        if (DateUtil.between(new Date(), request.getStartDate(), DateUnit.DAY) > maxPreactApplyDay) {
            log.info("getResourceTimeList fail, startDate > maxPreactApplyDay. startDate:{}, maxPreactApplyDay:{}", request.getEndDate(),
                    maxPreactApplyDay);
            return RpcResult.success(new ResponseGetResourceTimeBean()
                    .setMaxOfficialSeatHallCount(getMaxOfficialSeatHallCount(request.getAppId()))
                    .setTimeInfoList(Collections.emptyList())
            );
        }

        // 结束时间大于最大提报时间，需进行日期截断
        if (DateUtil.between(new Date(), request.getEndDate(), DateUnit.DAY) > maxPreactApplyDay){
            request.setEndDate(fm.lizhi.commons.util.DateUtil.getDayAfter(new Date(), maxPreactApplyDay));
            log.info("end time is greater than max preact apply day, truncate end time. endDate:{}", request.getEndDate());
        }

        // 获取官频位数据
        List<ActivityOfficialSeatTimeDTO> seatList = new ArrayList<>();
        if (request.getSeat() != null){
            seatList = activityOfficialSeatManager.getOfficialSeatList(request.getAppId(), request.getStartDate(), request.getEndDate(), request.getSeat());
        }

        List<ActivityApplyInfoSimpleDTO> applyInfoListByProgramme = new ArrayList<>();
        // 是否支持节目单
        boolean isEnableProgramResource = false;

        boolean supportProgramResourceTime = activityConfig.getBizConfig(request.getAppId()).isSupportProgramResourceTime();
        if (supportProgramResourceTime){
            // 查询节目单提报活动
            ActivityTemplateFlowResourceDTO programme = activityTemplateManager.getTemplateProgramme(request.getTemplateId());
            if (programme != null){
                isEnableProgramResource = true;
                applyInfoListByProgramme = activityApplyManager.getInTimeRangeActivityApplyByProgramme(request.getStartDate(), request.getEndDate(),
                        request.getAppId(), programme.getResourceConfigId()
                );
            }
        }


        // 组装结果
        ResponseGetResourceTimeBean response = buildResponseGetResourceTimeBean(request, seatList, applyInfoListByProgramme, isEnableProgramResource);
        return RpcResult.success(response);
    }


    /**
     * 构建时间资源表
     */
    private ResponseGetResourceTimeBean buildResponseGetResourceTimeBean(RequestGetResourceTimeBean request,
                                                                         List<ActivityOfficialSeatTimeDTO> seatList,
                                                                         List<ActivityApplyInfoSimpleDTO> applyInfoListByProgramme, boolean isEnableProgramResource) {
        ResponseGetResourceTimeBean response = new ResponseGetResourceTimeBean();
        List<DateDTO> dateList = DateTimeUtils.divideTimeSlots(request.getStartDate(), request.getEndDate());


        List<ResourceTimeItemBean> list = dateList.stream().map(date -> {
            ResourceTimeItemBean itemBean = new ResourceTimeItemBean();
            itemBean.setStartTime(date.getStartTime().getTime());
            itemBean.setEndTime(date.getEndTime().getTime());
            itemBean.setSeat(request.getSeat() == null ? -1 : request.getSeat());
            itemBean.setSeatCount(getSeatCount(date, seatList));
            itemBean.setProgrammeCount(getProgrammeCount(date, applyInfoListByProgramme, isEnableProgramResource));
            return itemBean;
        }).collect(Collectors.toList());

        if (request.getSeat() != null){
            response.setMaxOfficialSeatHallCount(getMaxOfficialSeatHallCount(request.getAppId()));
        }
        response.setTimeInfoList(buildTimeInfoList(list));
        return response;
    }


    private List<ResourceTimeBean> buildTimeInfoList( List<ResourceTimeItemBean> list) {
        if (CollectionUtils.isEmpty(list)){
            return new ArrayList<>();
        }

        // 按时间分组
        Map<Date, List<ResourceTimeItemBean>> map = list.stream()
                .collect(Collectors.groupingBy(item -> DateUtil.beginOfDay(new Date(item.getStartTime()))));

        List<ResourceTimeBean> result = new ArrayList<>();
        for (Map.Entry<Date, List<ResourceTimeItemBean>> entry : map.entrySet()) {
            ResourceTimeBean timeBean = new ResourceTimeBean();
            timeBean.setDate(entry.getKey().getTime());
            timeBean.setList(entry.getValue().stream().sorted(Comparator.comparing(ResourceTimeItemBean::getStartTime)).collect(Collectors.toList()));
            result.add(timeBean);
        }
        return result.stream().sorted(Comparator.comparing(ResourceTimeBean::getDate)).collect(Collectors.toList());
    }

    /**
     * 获取节目单提报数量
     */
    private Integer getProgrammeCount(DateDTO date, List<ActivityApplyInfoSimpleDTO> applyInfoListByProgramme, boolean isEnableProgramResource) {
        if (date == null || !isEnableProgramResource) {
            // 时间为空 或者不支持节目单
            return -1;
        }
        if (CollUtil.isEmpty(applyInfoListByProgramme)){
            return 0;
        }
        Date startDate = date.getStartTime();
        Date endDate = date.getEndTime();
        return Math.toIntExact(applyInfoListByProgramme.stream()
                .filter(applyInfo ->
                        startDate.getTime() >= applyInfo.getStartTime().getTime()
                                && endDate.getTime() <= applyInfo.getEndTime().getTime())
                .count());
    }

    /**
     * 获取官频位数量
     */
    private Integer getSeatCount(DateDTO date, List<ActivityOfficialSeatTimeDTO> seatList) {

        if (date == null || CollUtil.isEmpty(seatList)){
            return -1;
        }
        Date startDate = date.getStartTime();
        Date endDate = date.getEndTime();
        return seatList.stream()
                .filter(seat -> seat.getStartTime().equals(startDate) && seat.getEndTime().equals(endDate))
                .findFirst()
                .map(ActivityOfficialSeatTimeDTO::getCount)
                .orElse(-1);
    }

    /**
     * 获取最大官频位提报数量
     * @param appId
     * @return
     */
    private int getMaxOfficialSeatHallCount(Integer appId){
        ActivityRuleConfigBean rule = activityRuleManager.getActivityRuleByRuleTypeAndAppId(appId, ActivityApplyRuleEnum.OFFICIAL_COUNT);
        int count = 5;
        if (!Objects.isNull(rule)) {
            OfficialCountRuleBean ruleBean = activityRuleManager.getRuleBean(ActivityApplyRuleEnum.OFFICIAL_COUNT, rule.getRuleJson());
            if (ruleBean != null) {
                count = ruleBean.getCount();
            }
        }
        return count;
    }
}
