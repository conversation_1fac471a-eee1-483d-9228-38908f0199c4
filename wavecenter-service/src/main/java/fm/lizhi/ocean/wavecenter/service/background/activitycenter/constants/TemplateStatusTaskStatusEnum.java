package fm.lizhi.ocean.wavecenter.service.background.activitycenter.constants;

/**
 * 模板状态任务的执行状态
 * <AUTHOR>
 * @date 2025/5/8 17:26
 */
public enum TemplateStatusTaskStatusEnum {

    /**
     * 等待执行
     */
    WAITING(0)
    /**
     * 执行完成
     */
    , FINISHED(1)
    /**
     * 执行失败
     */
    , FAILED(2)
    ;

    TemplateStatusTaskStatusEnum(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    private int value;

}
