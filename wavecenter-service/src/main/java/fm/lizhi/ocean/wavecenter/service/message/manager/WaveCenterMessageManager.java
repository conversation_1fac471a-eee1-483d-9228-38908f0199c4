package fm.lizhi.ocean.wavecenter.service.message.manager;


import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.message.request.*;
import fm.lizhi.ocean.wavecenter.api.message.response.ResponseGetMessageList;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface WaveCenterMessageManager {


    /**
     * 向指定用户发送消息
     * @return messageId
     */
    Long sendMessage(RequestSendMessage param);

    /**
     * 批量发送消息
     */
    Result<List<Long>> sendMessageBatch(RequestSendMessageBatch param);


    /**
     * 向指定角色发送消息
     */
    Result<List<Long>> sendMessage2Role(RequestSendMessageToRole param);

    /**
     * 获取消息列表
     */
    ResponseGetMessageList getMessageList(RequestGetMessageList param);

    /**
     * 批量已读
     */
    void batchRead(RequestBatchReadMessage param);
}
