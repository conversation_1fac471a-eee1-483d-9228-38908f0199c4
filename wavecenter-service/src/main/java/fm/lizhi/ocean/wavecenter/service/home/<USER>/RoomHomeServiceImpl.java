package fm.lizhi.ocean.wavecenter.service.home.impl;

import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.wavecenter.api.common.constants.DateType;
import fm.lizhi.ocean.wavecenter.api.home.request.RequestGetRoomKeyDataSummary;
import fm.lizhi.ocean.wavecenter.api.home.request.RequestGetRoomKeyDataTrendChart;
import fm.lizhi.ocean.wavecenter.api.home.request.RequestGetRoomMsgAnalysisPerformance;
import fm.lizhi.ocean.wavecenter.api.home.response.ResponseRoomKeyDataSummary;
import fm.lizhi.ocean.wavecenter.api.home.response.ResponseRoomKeyDataTrendChart;
import fm.lizhi.ocean.wavecenter.api.home.response.ResponseRoomMsgAnalysisPerformance;
import fm.lizhi.ocean.wavecenter.api.home.service.RoomHomeService;
import fm.lizhi.ocean.wavecenter.common.utils.ResultHandler;
import fm.lizhi.ocean.wavecenter.service.home.convert.RoomHomeConvert;
import fm.lizhi.ocean.wavecenter.service.home.dto.RoomKeyDataSummaryDTO;
import fm.lizhi.ocean.wavecenter.service.home.dto.RoomKeyDataTrendChartDTO;
import fm.lizhi.ocean.wavecenter.service.home.dto.RoomMsgAnalysisPerformanceDTO;
import fm.lizhi.ocean.wavecenter.service.home.manager.RoomHomeManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * 厅管理首页
 * <AUTHOR>
 */
@ServiceProvider
@Slf4j
public class RoomHomeServiceImpl implements RoomHomeService {

    @Autowired
    private RoomHomeManager roomHomeManager;



    @Override
    public Result<ResponseRoomKeyDataSummary> getRoomKeyDataSummary(RequestGetRoomKeyDataSummary request) {

        LogContext.addReqLog("getRoomKeyDataSummary.request={}", JsonUtil.dumps(request));
        LogContext.addResLog("getRoomKeyDataSummary.request={}", JsonUtil.dumps(request));

        return ResultHandler.handle(request.getAppId(), () -> {
            RoomKeyDataSummaryDTO dto = roomHomeManager.getRoomKeyDataSummary(request);
            return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, RoomHomeConvert.I.toRoomKeyDataSummaryDto(dto));
        });
    }

    @Override
    public Result<List<ResponseRoomKeyDataTrendChart>> getRoomKeyDataTrendChart(RequestGetRoomKeyDataTrendChart request) {

        LogContext.addReqLog("getRoomKeyDataTrendChart.request={}", JsonUtil.dumps(request));
        LogContext.addResLog("getRoomKeyDataTrendChart.request={}", JsonUtil.dumps(request));

        return ResultHandler.handle(request.getAppId(), () -> {
            List<RoomKeyDataTrendChartDTO> dtos = roomHomeManager.getRoomKeyDataTrendChart(request);
            return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, RoomHomeConvert.I.toResponseRoomKeyDataTrendChart(dtos));
        });
    }

    @Override
    public Result<ResponseRoomMsgAnalysisPerformance> getRoomMsgAnalysisPerformance(RequestGetRoomMsgAnalysisPerformance request) {

        LogContext.addReqLog("getRoomMsgAnalysisPerformance.request={}", JsonUtil.dumps(request));
        LogContext.addResLog("getRoomMsgAnalysisPerformance.request={}", JsonUtil.dumps(request));
        return ResultHandler.handle(request.getAppId(), () -> {

            RoomMsgAnalysisPerformanceDTO dto = roomHomeManager.getRoomMsgAnalysisPerformance(request);
            return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, RoomHomeConvert.I.toResponseRoomMsgAnalysisPerformance(dto));
        });
    }
}
