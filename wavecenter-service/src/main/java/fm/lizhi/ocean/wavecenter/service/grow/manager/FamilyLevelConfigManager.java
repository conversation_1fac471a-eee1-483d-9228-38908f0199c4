package fm.lizhi.ocean.wavecenter.service.grow.manager;

import fm.lizhi.ocean.wavecenter.api.grow.level.bean.FamilyLevelConfigAwardBean;
import fm.lizhi.ocean.wavecenter.api.grow.level.bean.FamilyLevelConfigBean;
import fm.lizhi.ocean.wavecenter.api.grow.level.request.RequestDeleteFamilyLevelConfig;
import fm.lizhi.ocean.wavecenter.api.grow.level.request.RequestGetFamilyLevelConfigList;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025/3/18 17:59
 */
public interface FamilyLevelConfigManager {

    /**
     * 查询公会等级配置列表
     * @param request
     * @return
     */
    List<FamilyLevelConfigBean> getList(RequestGetFamilyLevelConfigList request);

    /**
     * 通过名称查询已被删除的公会等级配置
     * @param levelName
     * @return
     */
    Optional<FamilyLevelConfigBean> getDeletedByName(String levelName);

    /**
     * 删除规则
     * @param request
     */
    void delete(RequestDeleteFamilyLevelConfig request);

    /**
     * 查询等级配置列表 包括宣传图
     * @param request
     * @return
     */
    List<FamilyLevelConfigAwardBean> getAwardlist(RequestGetFamilyLevelConfigList request);

    /**
     * 获取等级配置, 不管是否删除
     *
     * @param levelId 等级id
     * @return 等级配置
     */
    FamilyLevelConfigBean getLevelConfig(Long levelId);

    /**
     * 根据给定的等级id列表获取等级配置map, 包含删除状态. key为等级id, value为等级配置
     *
     * @param levelIds 等级id列表
     * @return 等级配置map
     */
    Map<Long, FamilyLevelConfigBean> getLevelConfigMap(List<Long> levelIds);
}
