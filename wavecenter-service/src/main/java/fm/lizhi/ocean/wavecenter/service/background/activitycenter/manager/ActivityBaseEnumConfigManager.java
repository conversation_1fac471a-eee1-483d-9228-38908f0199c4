package fm.lizhi.ocean.wavecenter.service.background.activitycenter.manager;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.response.ResponseActivityConfigBean;

/**
 * 基础业务配置
 * <AUTHOR>
 */
public interface ActivityBaseEnumConfigManager {


    /**
     * 获取一些基础的枚举配置
     */
    Result<ResponseActivityConfigBean> getBaseEnumConfig(int appId);


}
