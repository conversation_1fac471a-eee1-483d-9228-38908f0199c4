package fm.lizhi.ocean.wavecenter.service.home.impl;

import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Lists;
import com.google.common.collect.Table;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.commons.util.DateUtil;
import fm.lizhi.ocean.wavecenter.api.common.constants.DateType;
import fm.lizhi.ocean.wavecenter.api.common.constants.MetricsEnum;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.CountDataBean;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.GuildGetKeyIndicatorsParamBean;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.IndicatorBean;
import fm.lizhi.ocean.wavecenter.api.home.bean.MetricsDataBean;
import fm.lizhi.ocean.wavecenter.api.home.bean.TrendChartBean;
import fm.lizhi.ocean.wavecenter.api.home.constants.MarketMonitorTrendChartType;
import fm.lizhi.ocean.wavecenter.api.home.request.RequestGetGuildKeyDataSummary;
import fm.lizhi.ocean.wavecenter.api.home.request.RequestGetGuildKeyDataTrendChart;
import fm.lizhi.ocean.wavecenter.api.home.request.RequestGetGuildMarketMonitorSummary;
import fm.lizhi.ocean.wavecenter.api.home.request.RequestGetGuildMarketMonitorTrendChart;
import fm.lizhi.ocean.wavecenter.api.home.response.ResponseGuildKeyDataSummary;
import fm.lizhi.ocean.wavecenter.api.home.response.ResponseGuildKeyDataTrendChart;
import fm.lizhi.ocean.wavecenter.api.home.response.ResponseGuildMarketMonitorSummary;
import fm.lizhi.ocean.wavecenter.api.home.response.ResponseGuildMarketMonitorTrendChart;
import fm.lizhi.ocean.wavecenter.api.home.service.GuildHomeService;
import fm.lizhi.ocean.wavecenter.base.util.CalculateUtil;
import fm.lizhi.ocean.wavecenter.common.utils.MyDateUtil;
import fm.lizhi.ocean.wavecenter.common.utils.ResultHandler;
import fm.lizhi.ocean.wavecenter.service.datacenter.config.DataCenterConfig;
import fm.lizhi.ocean.wavecenter.service.datacenter.handler.GuildDataHandler;
import fm.lizhi.ocean.wavecenter.service.datacenter.handler.IndicatorTrendParam;
import fm.lizhi.ocean.wavecenter.service.datacenter.handler.RoomScopeDataHandler;
import fm.lizhi.ocean.wavecenter.service.home.convert.GuildHomeConvert;
import fm.lizhi.ocean.wavecenter.service.home.dto.GuildMarketMonitorTrendChartDTO;
import fm.lizhi.ocean.wavecenter.service.home.manager.GuildHomeManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.*;

/**
 * 公会首页
 * <AUTHOR>
 */
@ServiceProvider
@Slf4j
public class GuildHomeServiceImpl implements GuildHomeService {

    @Autowired
    private GuildHomeManager guildHomeManager;
    @Autowired
    private GuildDataHandler guildDataHandler;
    @Autowired
    private RoomScopeDataHandler roomScopeDataHandler;
    @Autowired
    private DataCenterConfig dataCenterConfig;


    @Override
    public Result<ResponseGuildKeyDataSummary> getKeyDataSummary(RequestGetGuildKeyDataSummary request) {
        LogContext.addReqLog("getKeyDataSummary.request={}", JsonUtil.dumps(request));
        LogContext.addResLog("getKeyDataSummary.request={}", JsonUtil.dumps(request));

        return ResultHandler.handle(request.getAppId(), () -> {
            ResponseGuildKeyDataSummary result = new ResponseGuildKeyDataSummary();

            MetricsDataBean sumIncome = guildHomeManager.getKeyDataSummary(request);
            result.setSumIncome(sumIncome);

            List<IndicatorBean> keyIndicators = null;
            GuildGetKeyIndicatorsParamBean.GuildGetKeyIndicatorsParamBeanBuilder builder = GuildGetKeyIndicatorsParamBean.builder()
                    .appId(request.getAppId())
                    .familyId(request.getFamilyId())
                    .dateType(DateType.WEEK)
                    .startDate(request.getStartDate())
                    .endDate(request.getEndDate())
                    .valueMetric(MetricsEnum.SIGN_UP_GUEST_PLAYER_CNT.getValue())
                    .ratioMetric(MetricsEnum.SIGN_UP_GUEST_PLAYER_CNT.getValue());
            if (CollectionUtils.isEmpty(request.getRoomIds())) {
                keyIndicators = guildDataHandler.getKeyIndicators(builder.build());
            } else {
                builder.roomIds(request.getRoomIds());
                keyIndicators = roomScopeDataHandler.getKeyIndicators(builder.build());
            }

            if (CollectionUtils.isNotEmpty(keyIndicators)) {
                Optional<IndicatorBean> first = keyIndicators.stream().filter(v -> MetricsEnum.SIGN_UP_GUEST_PLAYER_CNT.getValue().equals(v.getMetric())).findFirst();
                if (first.isPresent()) {
                    IndicatorBean indicatorBean = first.get();
                    MetricsDataBean signUpGuestPlayerCnt = new MetricsDataBean();
                    signUpGuestPlayerCnt.setCurrent(Double.parseDouble(indicatorBean.getIndicator()));
                    if (indicatorBean.getExtension() != null) {
                        signUpGuestPlayerCnt.setPre(Double.parseDouble(indicatorBean.getExtension().getPreIndicator()));
                        signUpGuestPlayerCnt.setRatio(indicatorBean.getExtension().getRatio());
                    }
                    Date startDate = DateUtil.formatStrToDate(request.getStartDate(), DateUtil.date_2);
                    Date endDate = DateUtil.formatStrToDate(request.getEndDate(), DateUtil.date_2);
                    signUpGuestPlayerCnt.setStartTime(startDate.getTime());
                    signUpGuestPlayerCnt.setEndTime(endDate.getTime());
                    result.setSignUpGuestPlayerCnt(signUpGuestPlayerCnt);
                }
            }

            return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, result);
        });

    }

    @Override
    public Result<List<ResponseGuildKeyDataTrendChart>> getKeyDataTrendChart(RequestGetGuildKeyDataTrendChart request) {

        LogContext.addReqLog("getKeyDataTrendChart.request={}", JsonUtil.dumps(request));
        LogContext.addResLog("getKeyDataTrendChart.request={}", JsonUtil.dumps(request));

        return ResultHandler.handle(request.getAppId(), () -> {

            Date startDate = DateUtil.formatStrToDate(request.getStartDate(), DateUtil.date_2);
            Date endDate = DateUtil.formatStrToDate(request.getEndDate(), DateUtil.date_2);
            List<Integer> curDays = MyDateUtil.getRangeDayValues(startDate, endDate);

            Date startBefore = DateUtil.getDayBefore(startDate, 1);
            Date endBefore = DateUtil.getDayBefore(endDate, 1);
            List<Integer> beforeDays = MyDateUtil.getRangeDayValues(startBefore, endBefore);

            List<String> metrics = Lists.newArrayList(MetricsEnum.ALL_INCOME.getValue()
                    , MetricsEnum.ROOM_AVG_INCOME.getValue()
                    , MetricsEnum.SIGN_UP_GUEST_PLAYER_CNT.getValue()
                    , MetricsEnum.PLAYER_AVG_INCOME.getValue()
            );

            IndicatorTrendParam param = new IndicatorTrendParam();
            param.setFamilyId(request.getFamilyId()).setRoomIds(request.getRoomIds());
            Map<String, List<CountDataBean>> curDataMap;
            Map<String, List<CountDataBean>> beforeDataMap;
            if (CollectionUtils.isEmpty(request.getRoomIds())) {
                curDataMap = guildDataHandler.getIndicatorTrend(param, metrics, curDays);
                beforeDataMap = guildDataHandler.getIndicatorTrend(param, metrics, beforeDays);
            } else {
                curDataMap = roomScopeDataHandler.getIndicatorTrend(param, metrics, curDays);
                beforeDataMap = roomScopeDataHandler.getIndicatorTrend(param, metrics, beforeDays);
            }

            //转为天为key的数据结构 <day, metrics, value>
            Table<Integer, String, String> curDataTable = transTable(curDataMap);
            Table<Integer, String, String> beforeDataTable = transTable(beforeDataMap);

            List<ResponseGuildKeyDataTrendChart> result = new ArrayList<>();
            //数据结构组织以及计算环比
            for (Integer dayValue : curDays) {

                Date curDay = MyDateUtil.getDayValueDate(dayValue);
                ResponseGuildKeyDataTrendChart chart = new ResponseGuildKeyDataTrendChart();
                chart.setDate(curDay);
                chart.setSumIncome(buildChart(curDataTable, beforeDataTable, MetricsEnum.ALL_INCOME.getValue(), dayValue, curDay));
                chart.setPlayerAvgIncome(buildChart(curDataTable, beforeDataTable, MetricsEnum.PLAYER_AVG_INCOME.getValue(), dayValue, curDay));
                chart.setRoomAvgIncome(buildChart(curDataTable, beforeDataTable, MetricsEnum.ROOM_AVG_INCOME.getValue(), dayValue, curDay));
                chart.setSignUpGuestPlayerCnt(buildChart(curDataTable, beforeDataTable, MetricsEnum.SIGN_UP_GUEST_PLAYER_CNT.getValue(), dayValue, curDay));

                result.add(chart);
            }

            return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, result);
        });

    }

    private TrendChartBean buildChart(Table<Integer, String, String> curDataTable
            , Table<Integer, String, String> beforeDataTable
            , String metrics
            , Integer dayValue, Date curDate){
        Double ratioWarning = dataCenterConfig.getRatioWarning();
        Date dayBefore = DateUtil.getDayBefore(curDate, 1);
        String curValue = curDataTable.get(dayValue, metrics);
        String beforeValue = beforeDataTable.get(MyDateUtil.getDateDayValue(dayBefore), metrics);

        TrendChartBean valueBean = new TrendChartBean();
        if ( curValue != null ) {
            valueBean.setCurrent( Double.parseDouble( curValue ) );
        }
        if ( beforeValue != null ) {
            valueBean.setPre( Double.parseDouble( beforeValue ) );
        }
        valueBean.setRatio(CalculateUtil.relativeRatio(beforeValue, curValue));
        valueBean.setWarn(BigDecimal.valueOf(ratioWarning).compareTo(CalculateUtil.relativeRatioNotFormat(beforeValue, curValue)) > 0 );

        return valueBean;
    }

    private Table<Integer, String, String> transTable(Map<String, List<CountDataBean>> dataMap){
        Table<Integer, String, String> table = HashBasedTable.create();
        for (Map.Entry<String, List<CountDataBean>> entry : dataMap.entrySet()) {
            String metrics = entry.getKey();
            for (CountDataBean dataBean : entry.getValue()) {
                if (dataBean.getValue() == null) {
                    continue;
                }
                table.put(MyDateUtil.getDateDayValue(dataBean.getDate()), metrics, dataBean.getValue());
            }
        }
        return table;
    }

    @Override
    public Result<ResponseGuildMarketMonitorSummary> getMarketMonitorSummary(RequestGetGuildMarketMonitorSummary request) {

        LogContext.addReqLog("getMarketMonitorSummary.request={}", JsonUtil.dumps(request));
        LogContext.addResLog("getMarketMonitorSummary.request={}", JsonUtil.dumps(request));

        return ResultHandler.handle(request.getAppId(), () -> {

            ResponseGuildMarketMonitorSummary result = new ResponseGuildMarketMonitorSummary();

            List<IndicatorBean> keyIndicators = null;
            GuildGetKeyIndicatorsParamBean.GuildGetKeyIndicatorsParamBeanBuilder builder = GuildGetKeyIndicatorsParamBean.builder()
                    .appId(request.getAppId())
                    .familyId(request.getFamilyId())
                    .dateType(DateType.WEEK)
                    .startDate(request.getStartDate())
                    .endDate(request.getEndDate())
                    .valueMetric(MetricsEnum.INCOME_ROOM_CNT.getValue())
                    .ratioMetric(MetricsEnum.INCOME_ROOM_CNT.getValue());
            if (CollectionUtils.isEmpty(request.getRoomIds())) {
                keyIndicators = guildDataHandler.getKeyIndicators(builder.build());
            } else {
                builder.roomIds(request.getRoomIds());
                keyIndicators = roomScopeDataHandler.getKeyIndicators(builder.build());
            }

            if (CollectionUtils.isNotEmpty(keyIndicators)) {
                IndicatorBean indicatorBean = keyIndicators.get(0);
                MetricsDataBean incomeRoomCnt = new MetricsDataBean();
                incomeRoomCnt.setCurrent(Double.parseDouble(indicatorBean.getIndicator()));
                if (indicatorBean.getExtension() != null) {
                    incomeRoomCnt.setPre(Double.parseDouble(indicatorBean.getExtension().getPreIndicator()));
                    incomeRoomCnt.setRatio(indicatorBean.getExtension().getRatio());
                }
                incomeRoomCnt.setStartTime(DateUtil.formatStrToDate(request.getStartDate(), DateUtil.date_2).getTime());
                incomeRoomCnt.setEndTime(DateUtil.formatStrToDate(request.getEndDate(), DateUtil.date_2).getTime());
                result.setIncomeRoomCnt(incomeRoomCnt);
            }

            return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, result);
        });

    }

    @Override
    public Result<List<ResponseGuildMarketMonitorTrendChart>> getMarketMonitorTrendChart(RequestGetGuildMarketMonitorTrendChart request) {

        LogContext.addReqLog("getMarketMonitorTrendChart.request={}", JsonUtil.dumps(request));
        LogContext.addResLog("getMarketMonitorTrendChart.request={}", JsonUtil.dumps(request));

        return ResultHandler.handle(request.getAppId(), () -> {

            List<GuildMarketMonitorTrendChartDTO> dtos = new ArrayList<>();
            if (MarketMonitorTrendChartType.ROOM_INCOME.getType() == request.getType()){
                dtos = guildHomeManager.getMarketMonitorTrendChartByRoomIncome(request);
            }else if (MarketMonitorTrendChartType.ROOM_SUM_INCOME_WEEK.getType() == request.getType()){
                dtos = guildHomeManager.getMarketMonitorTrendChartByRoomSumIncome(request);
            }

            return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, GuildHomeConvert.I.toResponseGuildMarketMonitorTrendChart(dtos));
        });

    }
}
