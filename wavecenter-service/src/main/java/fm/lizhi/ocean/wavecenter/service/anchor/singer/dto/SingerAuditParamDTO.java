package fm.lizhi.ocean.wavecenter.service.anchor.singer.dto;

import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerHallApplyStatusEnum;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class SingerAuditParamDTO {

  /**
   * 操作人
   */
  private String operator;

  /**
   * 拒绝原因
   */
  private String rejectReason;

  /**
   * 点唱厅状态
   * 通过操作才需要传
   */
  private SingerHallApplyStatusEnum singerHallStatus;

  /**
   * 目标审核状态
   */
  private Integer targetAuditStatus;

  /**
   * 歌手类型
   */
  private Integer singerType;
}
