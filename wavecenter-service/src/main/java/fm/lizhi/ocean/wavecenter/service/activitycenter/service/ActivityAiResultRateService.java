package fm.lizhi.ocean.wavecenter.service.activitycenter.service;

import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.ActivityAiResultRateDTO;

/**
 * AI结果评分 Service 接口
 */
public interface ActivityAiResultRateService {

    /**
     * 保存AI结果评分
     *
     * @param dto ActivityAiResultRateDTO 实体
     * @return 是否保存成功
     */
    boolean saveActivityAiResultRate(ActivityAiResultRateDTO dto);
} 