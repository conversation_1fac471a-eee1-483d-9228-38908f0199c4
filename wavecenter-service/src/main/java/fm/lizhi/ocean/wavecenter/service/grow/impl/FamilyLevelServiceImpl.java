package fm.lizhi.ocean.wavecenter.service.grow.impl;

import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.grow.level.bean.FamilyLastLevelBean;
import fm.lizhi.ocean.wavecenter.api.grow.level.request.RequestGetFamilyLastLevel;
import fm.lizhi.ocean.wavecenter.api.grow.level.service.FamilyLevelService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.common.utils.MyDateUtil;
import fm.lizhi.ocean.wavecenter.service.grow.dto.FamilyLevelDTO;
import fm.lizhi.ocean.wavecenter.service.grow.manager.FamilyLevelManager;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025/3/20 15:51
 */
@ServiceProvider
public class FamilyLevelServiceImpl implements FamilyLevelService {

    @Autowired
    private FamilyLevelManager familyLevelManager;

    @Override
    public Result<FamilyLastLevelBean> getFamilyLastLevel(RequestGetFamilyLastLevel request) {
        LogContext.addReqLog("request={}", request);
        LogContext.addResLog("request={}", request);

        Date lastWeekStartDay = MyDateUtil.getLastWeekStartDay();
        LogContext.addResLog("lastWeekStartDay={}", lastWeekStartDay);
        Optional<FamilyLevelDTO> familyLevel = familyLevelManager.getFamilyLevel(request.getFamilyId(), lastWeekStartDay);

        if (!familyLevel.isPresent()) {
            return RpcResult.fail(FamilyLevelService.GET_FAMILY_LAST_LEVEL_NOT_EXIST);
        }

        LogContext.addResLog("familyLevel={}", familyLevel.get());
        return RpcResult.success(new FamilyLastLevelBean()
                .setLevelId(familyLevel.get().getLevelId())
                .setExp(familyLevel.get().getExp()));
    }

}
