package fm.lizhi.ocean.wavecenter.service.datacenter.handler;

import fm.lizhi.ocean.wavecenter.api.datacenter.bean.GuildGetKeyIndicatorsParamBean;
import fm.lizhi.ocean.wavecenter.service.datacenter.dto.DataFamilyDayDTO;
import fm.lizhi.ocean.wavecenter.service.datacenter.dto.GetFamilyDayListParam;
import fm.lizhi.ocean.wavecenter.service.datacenter.manager.GuildDataManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/4/20 12:18
 */
@Component
public class GuildDataHandler extends AbsDataHandler<GuildGetKeyIndicatorsParamBean, DataFamilyDayDTO>{

    @Autowired
    private GuildDataManager guildDataManager;

    @Override
    protected Map<String, String> getDayKeyIndicators(GuildGetKeyIndicatorsParamBean paramBean, Date day, List<String> queryValueMetrics) {
        Integer appId = paramBean.getAppId();
        Long familyId = paramBean.getFamilyId();
        return guildDataManager.getDayKeyIndicators(appId, familyId, day, queryValueMetrics);
    }

    @Override
    protected Map<String, String> getWeekKeyIndicators(GuildGetKeyIndicatorsParamBean paramBean, Date startDay, Date endDay, List<String> queryValueMetrics) {
        Integer appId = paramBean.getAppId();
        Long familyId = paramBean.getFamilyId();
        return guildDataManager.getWeekKeyIndicators(appId, familyId, startDay, endDay, queryValueMetrics);
    }

    @Override
    protected Map<String, String> getMonthKeyIndicators(GuildGetKeyIndicatorsParamBean paramBean, Date month, List<String> queryValueMetrics) {
        Integer appId = paramBean.getAppId();
        Long familyId = paramBean.getFamilyId();
        return guildDataManager.getMonthKeyIndicators(appId, familyId, month, queryValueMetrics);
    }

    @Override
    protected Map<Integer, DataFamilyDayDTO> getDaysData(IndicatorTrendParam paramBean, List<String> metrics, List<Integer> dayValues) {
        List<DataFamilyDayDTO> dtoList = guildDataManager.getFamilyDayList(new GetFamilyDayListParam()
                .setFamilyId(paramBean.getFamilyId())
                .setDayValues(dayValues));
        return dtoList.stream().collect(Collectors.toMap(DataFamilyDayDTO::getStatDateValue, v -> v, (k1, k2) -> k2));
    }

}
