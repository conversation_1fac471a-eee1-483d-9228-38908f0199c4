package fm.lizhi.ocean.wavecenter.service.activitycenter.manager;

import fm.lizhi.ocean.wavecenter.common.utils.RedisLock;

import java.util.Date;

/**
 * 活动申请redis操作
 */
public interface ActivityRedisManager {

    /**
     * 活动申请锁
     *
     * @param appId  应用ID
     * @param userId 活动申请人
     * @return RedisLock对象
     */
    RedisLock getApplyLock(Integer appId, Long userId);

    /**
     * 活动修改锁
     *
     * @param appId      应用ID
     * @param activityId 活动ID
     * @return RedisLock对象
     */
    RedisLock getModifyLock(Integer appId, Long activityId);

    /**
     * 活动申请数量自增
     * 每周
     *
     * @param appId     应用ID
     * @param njId      厅主ID
     * @param startTime 开始时间
     * @return 是否成功
     */
    boolean applyCountIncrementByWeekly(Integer appId, Long njId, Date startTime);

    /**
     * 活动申请数量自减
     * 每周
     *
     * @param appId     应用ID
     * @param njId      厅主ID
     * @param startTime 开始时间
     * @return 是否成功
     */
    boolean applyCountDecrementByWeekly(Integer appId, Long njId, Date startTime);

    /**
     * 查询活动申请数量
     *
     * @param appId 应用ID
     * @param njId  厅主ID
     * @return 数量
     */
    Integer getApplyCountByWeekly(Integer appId, Long njId, Date startTime);


    /**
     * 获取资源发放锁
     *
     * @param appId      应用ID
     * @param resourceId 资源ID
     * @return 结果
     */
    RedisLock getResourceGiveLock(Integer appId, Long resourceId);

    /**
     * 获取活动操作锁
     *
     * @param appId      应用ID
     * @param activityId 活动ID
     * @return 结果
     */
    RedisLock getActivityOperateLock(Integer appId, Long activityId);

}
