package fm.lizhi.ocean.wavecenter.service.activitycenter.processor;

import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.DressUpGiveContext;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.SendDecorateParamDTO;
import fm.lizhi.ocean.wavecenter.service.common.processor.BusinessEnvAwareProcessor;

public interface IBackgroundGiveProcess extends BusinessEnvAwareProcessor {

    /**
     * 补充发放参数
     *
     * @param param 参数
     */
    void fillSendParam(DressUpGiveContext context, SendDecorateParamDTO param);


    default Class<? extends BusinessEnvAwareProcessor> getBaseBusinessProcessor() {
        return IBackgroundGiveProcess.class;
    }
}
