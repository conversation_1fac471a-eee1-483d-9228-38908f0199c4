package fm.lizhi.ocean.wavecenter.service.activitycenter.processor;

import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.DressUpGiveContext;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.SendDecorateParamDTO;
import fm.lizhi.ocean.wavecenter.service.common.processor.BusinessEnvAwareProcessor;

public interface IAvatarGiveProcess extends BusinessEnvAwareProcessor {

    /**
     * 是否可以自动发放
     *
     * @return true: 可以自动发放
     */
    boolean isAutoGive();

    /**
     * 补充发放参数
     *
     * @param param 参数
     */
    void fillSendParam(DressUpGiveContext context, SendDecorateParamDTO param);


    default Class<? extends BusinessEnvAwareProcessor> getBaseBusinessProcessor() {
        return IAvatarGiveProcess.class;
    }
}
