package fm.lizhi.ocean.wavecenter.service.permissions.impl;

import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.wavecenter.api.permissions.bean.DemoBean;
import fm.lizhi.ocean.wavecenter.api.permissions.service.DemoService;
import fm.lizhi.ocean.wavecenter.service.permissions.dto.DemoDto;
import fm.lizhi.ocean.wavecenter.service.permissions.manager.DemoManager;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @date 2024/3/25 18:30
 */
@ServiceProvider
public class DemoServiceImpl implements DemoService {

    @Autowired
    private DemoManager demoManager;

    @Override
    public Result<DemoBean> test(String test) {
        DemoDto test1 = demoManager.getTest();
        DemoBean demoBean = new DemoBean();
        demoBean.setId(test1.getId());
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, demoBean);
    }
}
