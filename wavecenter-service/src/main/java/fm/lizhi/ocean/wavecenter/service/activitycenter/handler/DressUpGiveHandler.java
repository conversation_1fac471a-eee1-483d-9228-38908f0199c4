package fm.lizhi.ocean.wavecenter.service.activitycenter.handler;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.DressUpGiveContext;

public interface DressUpGiveHandler {

    /**
     * 发放装扮
     *
     * @param context 上下文
     * @return 结果
     */
    Result<Void> giveDressUp(DressUpGiveContext context);

    Integer getDressUpType();

    int GIVE_DRESS_UP = 10;

}
