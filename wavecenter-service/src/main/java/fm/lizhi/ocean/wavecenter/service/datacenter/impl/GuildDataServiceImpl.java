package fm.lizhi.ocean.wavecenter.service.datacenter.impl;

import com.google.common.collect.Lists;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.*;
import fm.lizhi.ocean.wavecenter.api.datacenter.request.RequestGuildGetAssessmentInfo;
import fm.lizhi.ocean.wavecenter.api.datacenter.request.RequestGuildGetIndicatorTrend;
import fm.lizhi.ocean.wavecenter.api.datacenter.request.RequestGuildRoomPerformance;
import fm.lizhi.ocean.wavecenter.api.datacenter.service.GuildDataService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.common.utils.ResultHandler;
import fm.lizhi.ocean.wavecenter.service.datacenter.config.DataCenterConfig;
import fm.lizhi.ocean.wavecenter.service.datacenter.dto.AssessTimeDto;
import fm.lizhi.ocean.wavecenter.service.datacenter.handler.GuildDataHandler;
import fm.lizhi.ocean.wavecenter.service.datacenter.handler.IndicatorTrendParam;
import fm.lizhi.ocean.wavecenter.service.datacenter.handler.RoomScopeDataHandler;
import fm.lizhi.ocean.wavecenter.service.datacenter.manager.AssessmentManager;
import fm.lizhi.ocean.wavecenter.service.datacenter.manager.GuildDataManager;
import fm.lizhi.ocean.wavecenter.service.income.manager.IncomeManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/4/17 17:04
 */
@Slf4j
@ServiceProvider
public class GuildDataServiceImpl implements GuildDataService{

    @Autowired
    private GuildDataManager guildDataManager;
    @Autowired
    private DataCenterConfig dataCenterConfig;
    @Autowired
    private GuildDataHandler guildDataHandler;
    @Autowired
    private AssessmentManager assessmentManager;
    @Autowired
    private IncomeManager incomeManager;
    @Autowired
    private RoomScopeDataHandler roomScopeDataHandler;

    @Override
    public Result<GuildAssessmentInfoBean> getAssessmentInfo(int appId, long familyId) {
        LogContext.addReqLog("appId={}, familyId={}", appId, familyId);
        LogContext.addResLog("appId={}, familyId={}", appId, familyId);
        return ResultHandler.handle(appId, ()->{
            RequestGuildGetAssessmentInfo request = new RequestGuildGetAssessmentInfo();
            request.setAppId(appId);
            request.setFamilyId(familyId);
            return getAssessmentInfoV2(request);
        });
    }

    @Override
    public Result<GuildAssessmentInfoBean> getAssessmentInfoV2(RequestGuildGetAssessmentInfo request) {
        LogContext.addReqLog("request={}", JsonUtil.dumps(request));
        LogContext.addResLog("request={}", JsonUtil.dumps(request));
        Optional<GuildAssessmentInfoBean> assessmentInfoOp = guildDataManager.getAssessmentInfo(request.getFamilyId(), request.getRoomIds());
        if (!assessmentInfoOp.isPresent()) {
            LogContext.addResLog("assessmentInfo not present");
            return RpcResult.fail(ASSESSMENT_INFO_NOT_FOUND);
        }
        GuildAssessmentInfoBean guildAssessmentInfoBean = assessmentInfoOp.get();

        //查询当前考核周期
        AssessTimeDto assessTime = assessmentManager.getCurrentTime(request.getAppId(), request.getFamilyId());
        guildAssessmentInfoBean.setStartDate(assessTime.getStartDate().getTime());
        guildAssessmentInfoBean.setEndDate(assessTime.getEndDate().getTime());

        //有收入主播数
        int playerPayCount = incomeManager.getPlayerPayCount(request.getFamilyId(), request.getRoomIds(), assessTime.getStartDate(), assessTime.getEndDate());
        LogContext.addResLog("playerPayCount={}", playerPayCount);
        guildAssessmentInfoBean.setPlayerPayCount(playerPayCount);

        return RpcResult.success(guildAssessmentInfoBean);
    }

    @Override
    public Result<List<IndicatorBean>> getKeyIndicators(GuildGetKeyIndicatorsParamBean paramBean) {
        LogContext.addReqLog("paramBean{}", JsonUtil.dumps(paramBean));
        LogContext.addResLog("paramBean{}", JsonUtil.dumps(paramBean));
        return ResultHandler.handle(paramBean.getAppId(), ()->{
            if (CollectionUtils.isEmpty(paramBean.getRoomIds())) {
                return RpcResult.success(guildDataHandler.getKeyIndicators(paramBean));
            }

            return RpcResult.success(roomScopeDataHandler.getKeyIndicators(paramBean));
        });
    }

    @Override
    public Result<GuildRoomPerformanceResBean> roomPerformance(int appId, long familyId) {
        LogContext.addReqLog("appId={}, familyId={}", appId, familyId);
        LogContext.addResLog("appId={}, familyId={}", appId, familyId);
        return ResultHandler.handle(appId, ()->{
            return RpcResult.success(guildDataManager.roomPerformance(familyId, null));
        });
    }

    @Override
    public Result<GuildRoomPerformanceResBean> roomPerformanceV2(RequestGuildRoomPerformance request) {
        LogContext.addReqLog("request={}", JsonUtil.dumps(request));
        LogContext.addResLog("request={}", JsonUtil.dumps(request));
        return RpcResult.success(guildDataManager.roomPerformance(request.getFamilyId()
                , request.getRoomIds()));
    }

    @Override
    public Result<IndicatorTrendResBean> getIndicatorTrend(int appId, long familyId, String metric) {
        LogContext.addReqLog("appId={},familyId={},metric={}", appId, familyId, metric);
        LogContext.addResLog("appId={},familyId={},metric={}", appId, familyId, metric);
        return ResultHandler.handle(appId, ()->{
            int days = dataCenterConfig.getIndicatorTrendDays();
            List<CountDataBean> indicatorTrend = guildDataManager.getIndicatorTrend(familyId, metric, days);
            return RpcResult.success(new IndicatorTrendResBean()
                    .setCountDays(days)
                    .setCountData(indicatorTrend));
        });
    }

    @Override
    public Result<IndicatorTrendResBean> getIndicatorTrendV2(RequestGuildGetIndicatorTrend request) {
        LogContext.addReqLog("request={}", JsonUtil.dumps(request));
        LogContext.addResLog("request={}", JsonUtil.dumps(request));
        int days = dataCenterConfig.getIndicatorTrendDays();

        Map<String, List<CountDataBean>> indicatorTrend;

        IndicatorTrendParam param = new IndicatorTrendParam();
        param.setFamilyId(request.getFamilyId())
                .setRoomIds(request.getRoomIds());
        if (CollectionUtils.isEmpty(request.getRoomIds())) {
            indicatorTrend = guildDataHandler.getIndicatorTrend(param, Lists.newArrayList(request.getMetric()), days);
        } else {
            indicatorTrend = roomScopeDataHandler.getIndicatorTrend(param, Lists.newArrayList(request.getMetric()), days);
        }

        List<CountDataBean> countDataBeans = indicatorTrend.get(request.getMetric());
        return RpcResult.success(new IndicatorTrendResBean()
                .setCountDays(days)
                .setCountData(countDataBeans));
    }
}
