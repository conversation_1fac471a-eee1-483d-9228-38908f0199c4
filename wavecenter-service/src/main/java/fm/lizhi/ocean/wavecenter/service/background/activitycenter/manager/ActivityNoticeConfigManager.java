package fm.lizhi.ocean.wavecenter.service.background.activitycenter.manager;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestDeleteActivityNoticeConfig;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.model.dto.ActivityNoticeConfigDTO;

import java.util.List;
import java.util.Optional;

public interface ActivityNoticeConfigManager {

    /**
     * 获取公告配置
     *
     * @param appId
     * @return 公告配置
     */
    List<ActivityNoticeConfigDTO> getAllNoticeConfig(Integer appId);

    /**
     * 获取公告配置
     *
     * @param appId    应用id
     * @param category 分类
     * @return 公告配置
     */
    Optional<ActivityNoticeConfigDTO> getNoticeConfig(Integer appId, Integer category);

    /**
     * 更新公告配置
     *
     * @param noticeConfigDTO 公告配置
     * @return 公告配置
     */
    Result<Long> updateNoticeConfig(ActivityNoticeConfigDTO noticeConfigDTO);

    /**
     * 检查分类是否存在
     * @param appId
     * @param categoryList
     * @return 已存在的分类
     */
    List<Integer> hasCategoryByAppId(Integer appId, List<Integer> categoryList);

    /**
     * 获取公告配置品类
     *
     * @param appId    应用id
     * @param noticeId 公告id
     * @return 公告配置品类
     */
    List<Integer> getCategoryListByNoticeId(Integer appId, Long noticeId);

    /**
     * 删除公告配置
     *
     * @param id 公告配置
     * @return 公告配置
     */
    Result<Void> deleteNoticeConfig(RequestDeleteActivityNoticeConfig id);

    int UPDATE_NOTICE_CONFIG_FAIL = 1;

    int DELETE_NOTICE_CONFIG_FAIL = 2;
}
