package fm.lizhi.ocean.wavecenter.service.award.family.impl;

import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.award.family.bean.ListFamilyLevelAwardRuleBean;
import fm.lizhi.ocean.wavecenter.api.award.family.request.*;
import fm.lizhi.ocean.wavecenter.api.award.family.response.ResponseGetFamilyLevelAwardRule;
import fm.lizhi.ocean.wavecenter.api.award.family.service.FamilyLevelAwardRuleService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.base.util.JsonUtils;
import fm.lizhi.ocean.wavecenter.service.award.family.manager.FamilyLevelAwardRuleManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

@Slf4j
@ServiceProvider
public class FamilyLevelAwardRuleServiceImpl implements FamilyLevelAwardRuleService {

    @Autowired
    private FamilyLevelAwardRuleManager familyLevelAwardRuleManager;

    @Override
    public Result<Void> createFamilyLevelAwardRule(RequestCreateFamilyLevelAwardRule request) {
        LogContext.addReqLog("request={}", JsonUtils.toJsonString(request));
        LogContext.addResLog("request={}", JsonUtils.toJsonString(request));
        return familyLevelAwardRuleManager.createRule(request);
    }

    @Override
    public Result<Void> updateFamilyLevelAwardRule(RequestUpdateFamilyLevelAwardRule request) {
        LogContext.addReqLog("request={}", JsonUtils.toJsonString(request));
        LogContext.addResLog("request={}", JsonUtils.toJsonString(request));
        return familyLevelAwardRuleManager.updateRule(request);
    }

    @Override
    public Result<Void> deleteFamilyLevelAwardRule(RequestDeleteFamilyLevelAwardRule request) {
        LogContext.addReqLog("request={}", JsonUtils.toJsonString(request));
        LogContext.addResLog("request={}", JsonUtils.toJsonString(request));
        return familyLevelAwardRuleManager.deleteRule(request);
    }

    @Override
    public Result<List<ListFamilyLevelAwardRuleBean>> listFamilyLevelAwardRule(RequestListFamilyLevelAwardRule request) {
        LogContext.addReqLog("request={}", JsonUtils.toJsonString(request));
        LogContext.addResLog("request={}", JsonUtils.toJsonString(request));
        Result<List<ListFamilyLevelAwardRuleBean>> result = familyLevelAwardRuleManager.listRule(request);
        if (RpcResult.isSuccess(result) && log.isDebugEnabled()) {
            log.debug("listFamilyLevelAwardRule response={}", JsonUtils.toJsonString(result.target()));
        }
        return result;
    }

    @Override
    public Result<ResponseGetFamilyLevelAwardRule> getFamilyLevelAwardRule(RequestGetFamilyLevelAwardRule request) {
        LogContext.addReqLog("request={}", JsonUtils.toJsonString(request));
        LogContext.addResLog("request={}", JsonUtils.toJsonString(request));
        Result<ResponseGetFamilyLevelAwardRule> result = familyLevelAwardRuleManager.getFamilyLevelAwardRule(request);
        if (RpcResult.isSuccess(result) && log.isDebugEnabled()) {
            log.debug("getFamilyLevelAwardRule response={}", JsonUtils.toJsonString(result.target()));
        }
        return result;
    }
}
