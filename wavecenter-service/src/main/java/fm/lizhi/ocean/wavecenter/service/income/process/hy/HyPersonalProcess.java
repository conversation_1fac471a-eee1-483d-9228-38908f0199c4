package fm.lizhi.ocean.wavecenter.service.income.process.hy;


import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.service.income.process.PersonalProcess;
import org.springframework.stereotype.Component;


@Component
public class HyPersonalProcess implements PersonalProcess {


    @Override
    public BusinessEvnEnum getBusinessEnv() {
        return BusinessEvnEnum.HEI_YE;
    }

    @Override
    public boolean enablePlayerIncome() {
        return false;
    }
}
