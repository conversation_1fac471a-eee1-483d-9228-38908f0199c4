package fm.lizhi.ocean.wavecenter.service.resource.recommendcard.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/3/25 19:19
 */
@Data
@Accessors(chain = true)
public class RecommendCardUseRecordDTO {

    private Long id;

    /**
     * 被推荐厅ID
     */
    private Long njId;

    /**
     * 使用推荐卡数量
     */
    private Integer nums;

    /**
     * 置顶列表类型
     */
    private String category;

    /**
     * 推荐位置
     */
    private Integer recommendIndex;

    /**
     * 推荐位置
     */
    private String position;

    /**
     * 推荐时间段
     */
    private String recommendTime;

    /**
     * 使用时间
     */
    private Date useTime;

}
