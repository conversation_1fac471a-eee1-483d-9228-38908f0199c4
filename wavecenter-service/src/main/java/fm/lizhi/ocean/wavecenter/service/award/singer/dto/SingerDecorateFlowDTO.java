package fm.lizhi.ocean.wavecenter.service.award.singer.dto;

import lombok.*;

import java.util.Date;

/**
 *
 * 歌手装扮流水
 *
 * @date 2025-04-01 10:20:50
 */
@Data
public class SingerDecorateFlowDTO {
    /**
     * ID
     */
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 应用ID
     */
    private Integer appId;

    /**
     * 批次ID
     */
    private Long transactionId;

    /**
     * 装扮类型
     */
    private Integer decorateType;

    /**
     * 装扮ID
     */
    private Long decorateId;

    /**
     * 环境：TEST/PRE/PRO
     */
    private String deployEnv;

    /**
     * 发放时长，单位秒
     */
    private Long duration;

    /**
     * 歌手认证等级
     */
    private Integer singerType;

    /**
     * 过期时间
     */
    private Date expireTime;

    /**
     * 重试次数
     */
    private Integer retryCount;

    /**
     * 发放的规则 ID
     */
    private Long ruleId;

    /**
     * 状态 1: 未处理 2: 成功 3: 失败
     */
    private Integer status;

    /**
     * 操作类型 1: 发放 2: 回收
     */
    private Integer operateType;

    /**
     * 是否被回收，只有operate_type=发放时生效, 0 未回收,1回收
     */
    private Boolean recycled;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 操作时间
     */
    private Date operateTime;

    /**
     * 操作原因
     */
    private String reason;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date modifyTime;
}