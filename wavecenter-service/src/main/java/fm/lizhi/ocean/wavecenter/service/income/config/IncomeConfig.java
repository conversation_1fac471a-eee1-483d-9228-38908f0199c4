package fm.lizhi.ocean.wavecenter.service.income.config;

import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.common.config.AbsBizConfig;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * <AUTHOR>
 * @date 2024/4/24 18:19
 */
@Data
@ConfigurationProperties(prefix = "wavecenter-income")
public class IncomeConfig extends AbsBizConfig<BizIncomeConfig> {

    /**
     * 支付批量查询最大量
     */
    private Integer queryPayPageSize = 30;

    /**
     * 查询打卡记录最大分页条数
     */
    private Integer queryCheckInRecordMaxSize = 200;

    private PpIncomeConfig pp;

    private HyIncomeConfig hy;

    private XmIncomeConfig xm;

    public IncomeConfig() {
        PpIncomeConfig ppConfig = new PpIncomeConfig();
        this.pp = ppConfig;
        bizConfigMap.put(BusinessEvnEnum.PP.appId(), ppConfig);
        HyIncomeConfig hyConfig = new HyIncomeConfig();
        this.hy = hyConfig;
        bizConfigMap.put(BusinessEvnEnum.HEI_YE.appId(), hyConfig);
        XmIncomeConfig xmConfig = new XmIncomeConfig();
        this.xm = xmConfig;
        bizConfigMap.put(BusinessEvnEnum.XIMI.appId(), xmConfig);
    }

}
