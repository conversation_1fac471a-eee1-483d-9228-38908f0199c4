package fm.lizhi.ocean.wavecenter.service.anchor.singer.manager;

import java.util.List;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerTypeEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.request.RequestSaveApplyMenuConfig;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.request.RequestUpdateSingerAuditConfig;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.SingerAuditConfigDTO;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.SingerMenuConfigDTO;

/**
 * 主播审核配置管理
 */
public interface SingerAuditConfigManager {

    /**
     * 获取主播审核配置
     *
     * @param appId 应用ID
     * @return 主播审核配置
     */
    List<SingerAuditConfigDTO> getSingerAuditConfig(Integer appId);

    /**
     * 保存主播审核配置
     *
     * @param request 请求参数
     * @return 结果
     */
    Result<Void> updateSingerAuditConfig(RequestUpdateSingerAuditConfig request);

    /**
     * 获取启用状态的主播审核配置
     *
     * @param appId 应用ID
     * @return 启用状态的主播审核配置
     */
    Result<List<SingerAuditConfigDTO>> getEnableSingerAuditConfig(Integer appId);

    /**
     * 按使用场景查询预审核配置
     *
     * @param appId 应用ID
     * @param scene 使用场景
     * @return 预审核配置列表
     */
    List<SingerAuditConfigDTO> getSingerAuditConfigByScene(Integer appId, Integer scene);

    /**
     * 保存申请菜单配置
     *
     * @param request 请求参数
     */
    void saveApplyMenuConfig(RequestSaveApplyMenuConfig request);

    /**
     * 获取申请菜单配置
     *
     * @param appId      应用ID
     */
    SingerMenuConfigDTO getApplyMenuConfig(int appId, Integer singerType);

    /**
     * 检查菜单配置是否有效
     *
     * @param config 菜单配置
     * @param type   歌手类型
     * @return 配置是否有效
     */
    boolean checkMenuConfig(SingerMenuConfigDTO config, SingerTypeEnum type);

    int SINGER_AUDIT_CONFIG_FAILED = 1;

    int UPDATE_SINGER_AUDIT_CONFIG_FAIL = 1;


}