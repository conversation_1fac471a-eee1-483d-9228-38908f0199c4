package fm.lizhi.ocean.wavecenter.service.anchor.singer.manager;

import java.util.List;
import java.util.Map;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.SingerBlackListDTO;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.SingerInBlackResultDTO;

public interface SingerBlackListManager {

    /**
     * 查询当前用户是否在黑名单中
     *
     * @param userId 用户ID
     * @param appId  应用ID
     * @return 结果，不管在不在黑名单中，都会返回身份证号码
     */
    Result<SingerInBlackResultDTO> isInBlackList(Integer appId, Long userId, String idCardNumber);
    Boolean isInBlackList(Integer appId, Long userId);

    /**
     * 拉黑操作
     *
     * @param appId  应用ID
     * @param userId 用户ID
     * @return 拉黑是否成功
     */
    boolean addBlackList(Integer appId, Long userId, String idCardNumber);

    /**
     * 取消拉黑操作
     *
     * @param appId        应用ID
     * @param idCardNumber 证件号
     * @return 取消拉黑是否成功
     */
    boolean cancelBlackList(Integer appId, String idCardNumber);

    /**
     * 根据应用ID和证件号批量查询用户黑名单列表
     * 结果类型是map, key: 证件号, value: 结果对象
     */
    Map<String, SingerBlackListDTO> searchBlackListByCertNo(Integer appId, List<String> idCardNumberList);

    /**
     * 批量拉黑用户
     * @param appId 应用ID
     * @param userIdToIdCardMap 用户ID和证件号的映射
     * @return 结果
     */
    boolean batchAddBlackList(Integer appId, Map<Long, String> userIdToIdCardMap);

    /**
     * 批量取消拉黑用户
     * @param appId 应用ID
     * @param idCardNumberMap 证件号列表
     * @return 结果, 返回取消拉黑失败的用户ID列表
     */
    List<Long> batchCancelBlackList(Integer appId, Map<Long, String> idCardNumberMap);

}
