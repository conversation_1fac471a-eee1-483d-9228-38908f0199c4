package fm.lizhi.ocean.wavecenter.service.common.manager;

import fm.lizhi.ocean.wavecenter.api.common.bean.PageConfigBean;
import fm.lizhi.ocean.wavecenter.api.common.bean.SaveConfigReqBean;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/20 14:40
 */
public interface CommonConfigManager {

    void savePageConfig(SaveConfigReqBean reqBean);

    List<PageConfigBean> getPageCode(int appId, long userId, String pageCode);

}
