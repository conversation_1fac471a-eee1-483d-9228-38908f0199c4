package fm.lizhi.ocean.wavecenter.service.background.activitycenter.impl;

import cn.hutool.core.collection.CollUtil;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestDeleteActivityNoticeConfig;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestUpdateActivityNoticeConfig;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.response.ResponseGetActivityNoticeConfig;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.service.ActivityNoticeConfigService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.convert.ActivityNoticeConvert;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.manager.ActivityNoticeConfigManager;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.model.dto.ActivityNoticeConfigDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.stream.Collectors;

@ServiceProvider
@Slf4j
public class ActivityNoticeConfigServiceImpl implements ActivityNoticeConfigService {

    @Autowired
    private ActivityNoticeConfigManager activityNoticeConfigManager;

    @Override
    public Result<List<ResponseGetActivityNoticeConfig>> getNoticeConfig(Integer appId) {
        if (appId == null) {
            return RpcResult.fail(ActivityNoticeConfigService.GET_NOTICE_CONFIG_PARAM_INVALID, "参数异常，查询失败");
        }
        List<ActivityNoticeConfigDTO> result = activityNoticeConfigManager.getAllNoticeConfig(appId);
        List<ResponseGetActivityNoticeConfig> list = ActivityNoticeConvert.I.convertResponseGetActivityNoticeConfigList(result);
        return RpcResult.success(list);
    }

    @Override
    public Result<Long> updateNoticeConfig(RequestUpdateActivityNoticeConfig request) {
        LogContext.addReqLog("updateNoticeConfig request:{}", JsonUtil.dumps(request));
        LogContext.addResLog("updateNoticeConfig request:{}", JsonUtil.dumps(request));

        if (request.getId() == null || request.getId() <= 0) {
            // 新增，直接判断品类是否存在
            List<Integer> categoryList = activityNoticeConfigManager.hasCategoryByAppId(request.getAppId(), request.getCategoryList());
            if (CollUtil.isNotEmpty(categoryList)){
                log.info("updateNoticeConfig add fail, category is exist. appId:{}, categoryList:{}",request.getAppId(), categoryList);
                return RpcResult.fail(ActivityNoticeConfigService.UPDATE_NOTICE_CONFIG_EXIST_CATEGORY, "品类公告已存在，保存失败");
            }
        }else {
            // 更新，检查新添加的品类是否存在
            List<Integer> categoryListByNoticeId = activityNoticeConfigManager.getCategoryListByNoticeId(request.getAppId(), request.getId());
            List<Integer> addCategoryList = request.getCategoryList().stream().filter(category -> !categoryListByNoticeId.contains(category)).collect(Collectors.toList());
            List<Integer> existCategoryList = activityNoticeConfigManager.hasCategoryByAppId(request.getAppId(), addCategoryList);
            if (CollUtil.isNotEmpty(existCategoryList)){
                log.info("updateNoticeConfig update fail, category is exist. appId:{}, categoryList:{}",request.getAppId(), addCategoryList);
                return RpcResult.fail(ActivityNoticeConfigService.UPDATE_NOTICE_CONFIG_EXIST_CATEGORY, "品类公告已存在，更新失败");
            }
        }


        if (request.getContent() == null) {
            request.setContent("");
        }
        ActivityNoticeConfigDTO noticeConfig = new ActivityNoticeConfigDTO();
        noticeConfig.setId(request.getId());
        noticeConfig.setOperator(request.getOperator());
        noticeConfig.setContent(request.getContent());
        noticeConfig.setAppId(request.getAppId());
        noticeConfig.setCategoryList(request.getCategoryList());

        Result<Long> result = activityNoticeConfigManager.updateNoticeConfig(noticeConfig);
        if (RpcResult.isFail(result)) {
            return RpcResult.fail(ActivityNoticeConfigService.UPDATE_NOTICE_CONFIG_FAIL, result.getMessage() == null ? "保存失败，请稍候重试" : result.getMessage());
        }
        return RpcResult.success(result.target());
    }


    @Override
    public Result<Void> deleteNoticeConfig(RequestDeleteActivityNoticeConfig request) {
        LogContext.addReqLog("deleteNoticeConfig request:{}", JsonUtil.dumps(request));
        LogContext.addResLog("deleteNoticeConfig request:{}", JsonUtil.dumps(request));

        Result<Void> result = activityNoticeConfigManager.deleteNoticeConfig(request);
        if (RpcResult.isFail(result)) {
            return RpcResult.fail(ActivityNoticeConfigService.DELETE_NOTICE_CONFIG_FAIL, result.getMessage() == null? "删除失败，请稍候重试" : result.getMessage());
        }

        return RpcResult.success();
    }

}
