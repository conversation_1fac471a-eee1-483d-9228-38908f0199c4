package fm.lizhi.ocean.wavecenter.service.anchor.singer.constant;

/**
 * 歌手淘汰原因 常量
 * <AUTHOR>
 */
public interface SingerEliminationReasonConstant {

    /**
     * 解约
     */
    String SINGER_UNSIGNED = "【自动】解约";

    /**
     * 厅未通过审核
     */
    String HALL_QUALIFICATION_CANCEL = "【自动】厅未通过审核";

    /**
     * 厅被取消名额
     */
    String HALL_CANCEL_QUOTA = "【自动】厅被取消名额";

    /**
     * 运营淘汰
     */
    String OPERATOR_ELIMINATION = "【手动】运营淘汰";

    /**
     * 长期无营收
     */
    String NO_REVENUE = "【自动】长期无营收";





}
