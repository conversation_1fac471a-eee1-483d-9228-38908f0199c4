package fm.lizhi.ocean.wavecenter.service.home.convert;

import cn.hutool.core.convert.NumberChineseFormatter;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.IncomeBean;
import fm.lizhi.ocean.wavecenter.api.home.bean.MetricsDataBean;
import fm.lizhi.ocean.wavecenter.api.home.bean.MetricsPerformanceBean;
import fm.lizhi.ocean.wavecenter.api.home.response.ResponseRoomKeyDataSummary;
import fm.lizhi.ocean.wavecenter.api.home.response.ResponseRoomKeyDataTrendChart;
import fm.lizhi.ocean.wavecenter.api.home.response.ResponseRoomMsgAnalysisPerformance;
import fm.lizhi.ocean.wavecenter.base.util.CalculateUtil;
import fm.lizhi.ocean.wavecenter.service.home.dto.RoomKeyDataSummaryDTO;
import fm.lizhi.ocean.wavecenter.service.home.dto.RoomKeyDataTrendChartDTO;
import fm.lizhi.ocean.wavecenter.service.home.dto.RoomMsgAnalysisPerformanceDTO;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.List;

@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface RoomHomeConvert {

    RoomHomeConvert I = Mappers.getMapper(RoomHomeConvert.class);

    default Long dateToLong(Date date) {
        return date.getTime();
    }

    @Named("digitToChinese")
    default String digitToChinese(String digit){
        if (StrUtil.isBlank(digit) || !NumberUtil.isNumber(digit)) {
            return "";
        }
        return "第" + NumberChineseFormatter.format(Long.parseLong(digit), false, false) + "梯队";
    }

    @Named("transformRate")
    default String transformRate(String bigDecimalStr, String maxPerformance){
        if (bigDecimalStr == null) {
            return CalculateUtil.formatPercent(new BigDecimal(0), 0, RoundingMode.HALF_UP);
        }
        bigDecimalStr = CalculateUtil.recoverPercent(bigDecimalStr);
        BigDecimal bigDecimal = new BigDecimal(bigDecimalStr);


        if (StrUtil.isNotBlank(maxPerformance) && bigDecimal.compareTo(new BigDecimal(maxPerformance)) >= 0) {
            // 如果超于指定的值，则使用最大值
            bigDecimal = new BigDecimal(maxPerformance);
        }


        if (bigDecimal.abs().doubleValue() < 0.01){
            return CalculateUtil.formatPercent(bigDecimal, 0, RoundingMode.UP);
        }

        return CalculateUtil.formatPercent(bigDecimal, 0, RoundingMode.HALF_UP);
    }

    @Mappings({
            @Mapping(source = "examinationFlow.currentEchelonName", target = "examinationFlow.currentEchelonName", qualifiedByName = "digitToChinese"),
            @Mapping(source = "nextEchelon.currentEchelonName", target = "nextEchelon.currentEchelonName", qualifiedByName = "digitToChinese")
    })
    ResponseRoomKeyDataSummary toRoomKeyDataSummaryDto(RoomKeyDataSummaryDTO dto);

    @Mappings({
            @Mapping(target = "pre", source = "bean.per"),
    })
    MetricsDataBean buildMetricsDataBean(IncomeBean bean, Date startTime, Date endTime);

    List<ResponseRoomKeyDataTrendChart> toResponseRoomKeyDataTrendChart(List<RoomKeyDataTrendChartDTO> dtos);


    @Mappings({
            @Mapping(target = "ratio", expression = "java(fm.lizhi.ocean.wavecenter.base.util.CalculateUtil.relativeRatio(pre, current))"),
            @Mapping(target = "performance", expression = "java(transformRate(performance, maxPerformance))"),
            @Mapping(target = "metricsRate", expression = "java(transformRate(metricsRate, null))"),
    })
    MetricsPerformanceBean buildMetricsPerformanceBean(String pre, String current, String metricsRate, String performance, String maxPerformance);

    ResponseRoomMsgAnalysisPerformance toResponseRoomMsgAnalysisPerformance(RoomMsgAnalysisPerformanceDTO dto);
}
