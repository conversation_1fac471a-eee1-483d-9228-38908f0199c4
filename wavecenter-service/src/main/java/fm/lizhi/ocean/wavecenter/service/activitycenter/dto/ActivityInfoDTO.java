package fm.lizhi.ocean.wavecenter.service.activitycenter.dto;

import lombok.Data;

import java.util.Date;

@Data
public class ActivityInfoDTO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 应用ID
     */
    private Integer appId;

    /**
     * 业务定义不超过10个字，稍微冗余
     */
    private String name;

    /**
     * 提报厅厅主ID
     */
    private Long njId;

    /**
     * 家族ID
     */
    private Long familyId;

    /**
     * 活动分类ID
     */
    private Long classId;

    /**
     * 活动开始时间
     */
    private Date startTime;

    /**
     * 活动结束时间
     */
    private Date endTime;

    /**
     * 活动状态，1：待审批，2：审批通过，3：审批不通过
     */
    private Integer auditStatus;

    /**
     * 申请类型，1：自主提报，2: 官方活动
     */
    private Integer applyType;

    /**
     * 审核原因
     */
    private String auditReason;

    /**
     * 联系人
     */
    private String contact;

    /**
     * 申请者uid
     */
    private Long applicantUid;

    /**
     * 联系方式
     */
    private String contactNumber;

    /**
     * 主持人ID
     */
    private Long hostId;

    /**
     * 陪档主播ID列表
     */
    private String accompanyNjIds;

    /**
     * 活动目标，不超过100字
     */
    private String goal;

    /**
     * 活动介绍，不超过100字
     */
    private String introduction;

    /**
     * 活动道具图片，多个逗号分隔，斜杠开头
     */
    private String auxiliaryPropUrl;

    /**
     * 活动海报图片地址
     */
    private String posterUrl;

    /**
     * 玩法工具，多个逗号分隔
     */
    private String activityTool;

    /**
     * 房间公告，不超过500字
     */
    private String roomAnnouncement;

    /**
     * 房间公告图片，不超过3个
     */
    private String roomAnnouncementImgUrl;

    /**
     * 房间背景ID
     */
    private Long roomBackgroundId;

    /**
     * 头像框ID
     */
    private Long avatarWidgetId;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 可选值  TEST/PRE/PRO
     */
    private String deployEnv;

    /**
     * 是否删除，默认不删除
     */
    private Integer deleted;

    /**
     * 修改时间
     */
    private Date modifyTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 提报模式
     */
    private Integer model;

}
