package fm.lizhi.ocean.wavecenter.service.activitycenter.handler;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.DecorateEnum;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.base.util.JsonUtils;
import fm.lizhi.ocean.wavecenter.service.activitycenter.constatns.ResourceGiveErrorTipConstant;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.ActivityDressUpGiveDTO;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.ActivityResourceGiveDTO;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.DressUpGiveContext;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.SendDecorateParamDTO;
import fm.lizhi.ocean.wavecenter.service.activitycenter.manager.ActivityMaterielManager;
import fm.lizhi.ocean.wavecenter.service.activitycenter.processor.IBackgroundGiveProcess;
import fm.lizhi.ocean.wavecenter.service.common.processor.ProcessorFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 房间背景发放处理器
 */
@Slf4j
@Component
public class BackgroundGiveHandler implements DressUpGiveHandler {

    @Autowired
    private ActivityMaterielManager activityMaterielManager;

    @Autowired
    private ProcessorFactory processorFactory;

    @Override
    public Result<Void> giveDressUp(DressUpGiveContext context) {
        try {
            ActivityResourceGiveDTO resourceGiveDTO = context.getResourceGiveDTO();
            ActivityDressUpGiveDTO dressUpGiveDTO = context.getDressUpGiveDTO();
            SendDecorateParamDTO paramDTO = new SendDecorateParamDTO();
            paramDTO.setDecorateId(resourceGiveDTO.getResourceId());
            paramDTO.setDecorateRecordId(dressUpGiveDTO.getId());
            paramDTO.setOwnerId(dressUpGiveDTO.getUserId());

            IBackgroundGiveProcess processor = processorFactory.getProcessor(IBackgroundGiveProcess.class);
            processor.fillSendParam(context, paramDTO);
            return activityMaterielManager.sendDecorate(paramDTO);
        } catch (Exception e) {
            log.error("BackgroundGiveHandler.giveDressUp happen error: dressUpGiveDTO={}", JsonUtils.toJsonStringLegacy(context.getDressUpGiveDTO()), e);
            return RpcResult.fail(GIVE_DRESS_UP, ResourceGiveErrorTipConstant.BACKGROUND_GIVE_FAIL);
        }
    }

    @Override
    public Integer getDressUpType() {
        return DecorateEnum.BACKGROUND.getType();
    }
}
