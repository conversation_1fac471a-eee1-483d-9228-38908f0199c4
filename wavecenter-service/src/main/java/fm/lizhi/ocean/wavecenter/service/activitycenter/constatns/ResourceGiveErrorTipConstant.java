package fm.lizhi.ocean.wavecenter.service.activitycenter.constatns;

public class ResourceGiveErrorTipConstant {

    public static final String REC_CARD_GIVE_FAIL = "缺少推荐卡配置，推荐卡发放失败";
    public static final String REC_CARD_GIVE_CONFIG_NOT_FOUND = "推荐卡配置不存在，发放失败";
    public static final String REC_CARD_GIVE_EXCEPTION = "服务未知异常，推荐卡发放失败";

    public static final String PROGRAMME_GIVE_ACTIVITY_NOT_EXIST = "活动不存在，配置失败，请检查下发结果并手动处理";
    public static final String PROGRAMME_GIVE_LEVEL_EXCEPTION = "活动等级不存在，配置失败，请检查下发结果并手动处理";
    public static final String PROGRAMME_GIVE_LEVEL_CONFIG_EXCEPTION = "平台分类-西米分类映射不存在，配置失败，请检查下发结果并手动处理";
    public static final String PROGRAMME_GIVE_FAIL_EXCEPTION = "配置失败，请检查下发结果并手动处理";

    public static final String OFFICIAL_SEAT_GIVE_CONFIG_EXCEPTION = "缺少配置官频位核心信息, 请检查下发结果并手动处理";
    public static final String OFFICIAL_SEAT_GIVE_FAIL_EXCEPTION = "官频位配置失败, 请检查下发结果并手动处理";
    public static final String OFFICIAL_SEAT_TIME_SAME = "官频位配置失败, 已存在时间冲突的官频位配置，请检查下发结果并手动处理";

    public static final String BANNER_GIVE_NJ_NOT_LIVE = "主播还未开播，无法设置宣传横幅，请检查下发结果并手动处理";
    public static final String BANNER_GIVE_FAIL = "宣传横幅设置失败，请检查下发结果并手动处理";
    public static final String BANNER_GIVE_CONFIG_ERROR = "缺少关键配置-宽高比，宣传横幅设置失败，请检查下发结果并手动处理";

    public static final String AVATAR_GIVE_FAIL = "服务未知异常，头像框发放失败";
    public static final String BACKGROUND_GIVE_FAIL = "服务未知异常，房间背景发放失败";


    public static final String DELETE_OFFICIAL_SEAT_FAIL = "删除官频位配置失败，请到运营后台手动处理";


}
