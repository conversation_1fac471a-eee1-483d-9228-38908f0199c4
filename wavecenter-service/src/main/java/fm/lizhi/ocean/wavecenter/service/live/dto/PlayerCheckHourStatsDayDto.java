package fm.lizhi.ocean.wavecenter.service.live.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 陪玩小时统计-日集合
 * <AUTHOR>
 * @date 2024/6/11 17:59
 */
@Data
@Accessors(chain = true)
public class PlayerCheckHourStatsDayDto {

    private Date day;

    private List<PlayerCheckHourStatsDto> detail;

    /**
     * 合计
     */
    private BigDecimal income;

    private Long charm;

    private Integer seatOrder;

    private Integer hostCnt;

}
