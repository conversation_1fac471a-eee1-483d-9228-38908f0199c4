package fm.lizhi.ocean.wavecenter.service.anchor.singer.config;

import cn.hutool.core.map.MapUtil;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerTypeEnum;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.SingerMenuConfigDTO;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.UserSingerGloryConfig;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.UserSingerGloryDTO;
import lombok.Data;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
public class XmSingerConfig implements CommonSingerConfig {

     /**
     * 歌曲风格配置
     */
    private String songStyleConfig = "[{\"id\":1,\"name\":\"国风(古风)\"},{\"id\":2,\"name\":\"R&<PERSON>(节奏布鲁斯)\"},{\"id\":3,\"name\":\"流行民谣\"},{\"id\":4,\"name\":\"流行摇滚\"},{\"id\":5,\"name\":\"流行说唱\"},{\"id\":6,\"name\":\"电子说唱\"},{\"id\":7,\"name\":\"旋律说唱\"},{\"id\":8,\"name\":\"粤语流行\"},{\"id\":9,\"name\":\"华语流行\"},{\"id\":10,\"name\":\"网络流行\"},{\"id\":11,\"name\":\"抒情流行\"},{\"id\":99, \"name\":\"ALL\"}]";

    /**
     * 歌手导入名单
     * key:歌手等级
     * value:用户ID列表
     */
    private Map<Integer, List<Long>> importSingerMap;

    /**
     * 音频审核配置
     */
    private String audioAuditConfig="{\"durationSec\":10,\"enabledVerifyProportion\":true,\"thresholdMusicProportion\":94,\"thresholdNoiseDB\":-45,\"thresholdSpeechProportion\":50}";

    /**
     * 歌手认证提报文案
     */
    private String singerAuditReportText="{\"prologue\":\"现在是「几点几分」我是「主播呢称」,我的考该歌曲名称为「歌曲名称\",\"recordNotice\":\"唱歌前，请按以下格式完成自我介绍，并录制1分半钟的考核歌曲\"}";



    /**
     * 是否开启点唱厅导入
     */
    private boolean enableImportSingHall = false;

    /**
     * 是否开启点唱厅淘汰
     */
    private boolean enableCleanSingHall = false;

    /**
     * 导入点唱厅要求的收入流水
     */
    private Long importSingHallGtAllIncome = 200000L;

    /**
     * 用户歌手勋章配置
     */
    private UserSingerGloryConfig userSingerGloryConfig = new UserSingerGloryConfig();

}
