package fm.lizhi.ocean.wavecenter.service.common.manager;

import fm.lizhi.common.romefs.javasdk.exception.RomeFsException;
import fm.lizhi.ocean.wavecenter.service.common.param.RomeFsPutParam;
import fm.lizhi.ocean.wavecenter.service.common.param.RomeFsTransferParam;
import fm.lizhi.ocean.wavecenter.service.common.result.RomeFsPutResult;

/**
 * <AUTHOR>
 */
public interface RomeFsManager {

    /**
     * 上传文件. 自动根据文件大小分片上传和重试, 在重试次数内会屏蔽异常, 超过重试次数会抛出异常. 参考
     * <a href="https://lizhi2021.feishu.cn/wiki/MB2rwxahqimokGky8RwceMhlnvf">罗马文件上传-快速开始(服务端)</a>
     *
     * @param param 上传参数
     * @return 上传结果
     * @see RomeFsException
     */
    RomeFsPutResult putObject(RomeFsPutParam param);

    /**
     * 文件转存,底层上传还是使用 {@link RomeFsManager#putObject(RomeFsPutParam)} }
     * @return 上传结果
     * @see RomeFsException
     */
    RomeFsPutResult uploadTransfer(RomeFsTransferParam param);



}
