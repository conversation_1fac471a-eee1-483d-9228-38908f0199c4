package fm.lizhi.ocean.wavecenter.service.common.aspect;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.common.service.CommonService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.BridgeMethodResolver;
import org.springframework.stereotype.Component;
import org.springframework.util.ClassUtils;

import javax.validation.ConstraintViolation;
import javax.validation.Path;
import javax.validation.Validator;
import javax.validation.executable.ExecutableValidator;
import java.lang.reflect.Method;
import java.util.Set;

/**
 * 服务校验切面, 对于使用@ServiceProvider注解的方法进行参数校验和返回值校验. 暂不支持groups.
 * <p>
 * 示例代码:
 * <pre>{@code
 * public interface MyService {
 *
 *     Result<Void> test(@Valid Param param);
 * }
 *
 * @ServiceProvider
 * public class MyServiceImpl implements MyService {
 *
 *     @Override
 *     public Result<Void> test(Param param) {
 *         return new Result<>(0, null);
 *     }
 * }
 * }</pre>
 */
@Aspect
@Component
@Slf4j
public class ServiceValidateAspect {

    /**
     * javax的校验器
     */
    @Autowired
    private Validator validator;

    /**
     * 校验方法参数和返回值
     *
     * @param joinPoint 切点
     * @return 如果校验不通过, 返回校验不通过的结果, 否则返回原方法的结果
     * @throws Throwable 异常
     */
    @Around("@within(fm.lizhi.commons.service.client.annotation.ServiceProvider)" +
            " && execution(public fm.lizhi.commons.service.client.pojo.Result *(..))")
    public Object validateAndReturnResult(ProceedingJoinPoint joinPoint) throws Throwable {
        Object target = joinPoint.getTarget();
        Method method = ((MethodSignature) (joinPoint.getSignature())).getMethod();
        Object[] args = joinPoint.getArgs();
        ExecutableValidator executableValidator = validator.forExecutables();
        Set<ConstraintViolation<Object>> parametersViolations;
        try {
            parametersViolations = executableValidator.validateParameters(target, method, args);
        } catch (IllegalArgumentException e) {
            log.trace("Failed to validate parameters", e);
            Method bridgedMethod = BridgeMethodResolver.findBridgedMethod(
                    ClassUtils.getMostSpecificMethod(method, target.getClass()));
            parametersViolations = executableValidator.validateParameters(target, bridgedMethod, args);
        }
        if (CollectionUtils.isNotEmpty(parametersViolations)) {
            return buildResult(parametersViolations, method);
        }
        Object result = joinPoint.proceed();
        Set<ConstraintViolation<Object>> returnValueViolations = executableValidator.validateReturnValue(
                target, method, result);
        if (CollectionUtils.isNotEmpty(returnValueViolations)) {
            return buildResult(returnValueViolations, method);
        }
        return result;
    }

    private Result<Object> buildResult(Set<ConstraintViolation<Object>> violations, Method method) {
        ConstraintViolation<Object> violation = violations.iterator().next();
        Path propertyPath = violation.getPropertyPath();
        String message = violation.getMessage();
        log.info("Service validation failed: method={}, propertyPath={}, message={}", method, propertyPath, message);
        return RpcResult.fail(CommonService.PARAM_ERROR, violation.getMessage());
    }
}
