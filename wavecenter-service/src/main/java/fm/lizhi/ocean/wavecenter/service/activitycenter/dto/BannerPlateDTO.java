package fm.lizhi.ocean.wavecenter.service.activitycenter.dto;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * banner位置信息
 */
@Data
@Accessors(chain = true)
public class BannerPlateDTO {

    /**
     * 板块ID
     */
    private String bannerPlateId;

    /**
     * 板块名称
     */
    private String bannerPlateName;

    /**
     * 板块位置
     */
    private Integer index;

    /**
     * 板块分类ID
     */
    private Long plateTypeId;

}
