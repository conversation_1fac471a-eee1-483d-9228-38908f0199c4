package fm.lizhi.ocean.wavecenter.service.anchor.singer.handler;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import fm.lizhi.ocean.wavecenter.service.common.handler.BaseHandlerFactory;

@Component
public class SingerPreAuditFilterFactory extends BaseHandlerFactory<String, SingerPreAuditFilter> {

    @Autowired
    private List<SingerPreAuditFilter> filters;

    @Override
    public void registerAllHandlers() {
        for (SingerPreAuditFilter filter : filters) {
            registerHandler(filter.getCodeEnum().getCode(), filter);
        }
    }
}
