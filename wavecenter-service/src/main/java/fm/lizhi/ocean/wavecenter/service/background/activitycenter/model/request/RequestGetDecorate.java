package fm.lizhi.ocean.wavecenter.service.background.activitycenter.model.request;

import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.DecorateEnum;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@Builder
public class RequestGetDecorate {

    /**
     * 类型 1：头像框，2：背景
     * @see DecorateEnum
     */
    private Integer type;

    /**
     * 装扮ID
     */
    private Long dressUpId;

    private String name;

    private int pageNo;

    private int pageSize;
}
