package fm.lizhi.ocean.wavecenter.service.permissions.handler;

import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.permissions.constants.RoleEnum;
import fm.lizhi.ocean.wavecenter.api.sign.bean.NjAndPlayerContractBean;
import fm.lizhi.ocean.wavecenter.api.sign.constant.ContractTypeEnum;
import fm.lizhi.ocean.wavecenter.api.sign.constant.SignRelationEnum;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserInFamilyBean;
import fm.lizhi.ocean.wavecenter.service.permissions.dto.RoleAuthRefDto;
import fm.lizhi.ocean.wavecenter.service.permissions.manager.RoleManager;
import fm.lizhi.ocean.wavecenter.service.sign.dto.QueryNonContractDTO;
import fm.lizhi.ocean.wavecenter.service.sign.manager.FamilyManager;
import fm.lizhi.ocean.wavecenter.service.sign.manager.NonContractManager;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/4/28 16:02
 */
@Component
public class RoleHandler {

    private static final Logger log = LoggerFactory.getLogger(RoleHandler.class);
    @Autowired
    private FamilyManager familyManager;
    @Autowired
    private RoleManager roleManager;
    @Autowired
    private NonContractManager nonContractManager;

    /**
     * 移除陪玩在家族下的授权
     * @param familyId
     * @param playerId
     */
    public void removePlayerWithFamilyAuth(Long familyId, Long playerId){
        roleManager.removeUsersWithFamilyAuth(familyId, Collections.singletonList(playerId));
    }

    /**
     * 解除厅在家族中的授权
     */
    public void removeRoomWithFamilyAuth(Long familyId, Long roomId){
        //删除roomId在familyId中的授权记录
        roleManager.removeUsersWithFamilyAuth(familyId, Collections.singletonList(roomId));

        //roomId下签约的主播也要移除在该公会下的授权
        int pageSize = 200;
        int total = 0;
        int pageNo = 1;
        while (pageNo == 1 || total > (pageNo-1) * pageSize) {
            PageBean<NjAndPlayerContractBean> pageBean = nonContractManager.queryList(QueryNonContractDTO.builder()
                    .type(ContractTypeEnum.SIGN)
                    .type(ContractTypeEnum.SUBJECT_CHANGE)
                    .status(SignRelationEnum.SIGN_SUCCESS)
                    .njId(roomId)
                    .pageNo(pageNo)
                    .pageSize(pageSize)
                    .build());
            if (CollectionUtils.isEmpty(pageBean.getList())) {
                log.info("removeRoomWithFamilyAuth page {} is empty. familyId={},roomId={}", pageNo, familyId, roomId);
                break;
            }

            List<Long> userIds = pageBean.getList().stream()
                    .map(NjAndPlayerContractBean::getPlayerUserId)
                    .collect(Collectors.toList());
            log.info("removeUsersWithFamilyAuth familyId={},userIds={}", familyId, JsonUtil.dumps(userIds));
            roleManager.removeUsersWithFamilyAuth(familyId, userIds);

            total = pageBean.getTotal();
            pageNo++;
        }

    }

    /**
     * 检查授权配置的签约关系是否依然成立
     * @return
     */
    public boolean checkRoleConfigSign(RoleAuthRefDto dto){
        Long userId = dto.getUserId();
        UserInFamilyBean userFamily = familyManager.getUserInFamily(userId);
        if (RoleEnum.FAMILY.getRoleCode().equals(dto.getRoleCode())
                || RoleEnum.FAMILY_ADMIN.getRoleCode().equals(dto.getRoleCode())) {
            if (Objects.equals(userFamily.getFamilyId(), dto.getSubjectId())) {
                return true;
            }
        }
        if (RoleEnum.ROOM.getRoleCode().equals(dto.getRoleCode())) {
            Long roomUserId = dto.getSubjectUserId();
            UserInFamilyBean roomFamily = familyManager.getUserInFamily(roomUserId);
            //授权用户和授权账号都属于同一个公会
            if (Objects.equals(roomFamily.getFamilyId(), userFamily.getFamilyId())) {
                return true;
            }
        }
        return false;
    }


}
