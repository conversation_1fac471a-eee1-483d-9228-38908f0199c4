package fm.lizhi.ocean.wavecenter.service.activitycenter.dto;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class SaveOfficialSeatResDTO {

    /**
     * 是否成功
     */
    private boolean success;

    /**
     * 错误提示
     */
    private String msg;

    /**
     * 业务主键ID
     */
    private long bizRecordId;

    public static SaveOfficialSeatResDTO success() {
        return new SaveOfficialSeatResDTO().setSuccess(true);
    }

    public static SaveOfficialSeatResDTO success(long bizRecordId) {
        return new SaveOfficialSeatResDTO().setSuccess(true).setBizRecordId(bizRecordId);
    }

    public static SaveOfficialSeatResDTO fail(String msg) {
        return new SaveOfficialSeatResDTO().setSuccess(false).setMsg(msg);
    }

}
