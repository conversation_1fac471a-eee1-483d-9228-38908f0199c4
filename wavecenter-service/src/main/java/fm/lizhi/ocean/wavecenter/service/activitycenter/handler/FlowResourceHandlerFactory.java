package fm.lizhi.ocean.wavecenter.service.activitycenter.handler;

import fm.lizhi.ocean.wavecenter.service.common.handler.BaseHandlerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 流量资源处理器工厂
 */
@Component
public class FlowResourceHandlerFactory extends BaseHandlerFactory<String, FlowResourceGiveHandler> {

    @Autowired
    private List<FlowResourceGiveHandler> handlers;


    @Override
    public void registerAllHandlers() {
        //遍历注册所有的处理器
        for (FlowResourceGiveHandler handler : handlers) {
            registerHandler(handler.getResourceCode(), handler);
        }
    }
}
