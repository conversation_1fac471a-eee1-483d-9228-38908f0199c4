package fm.lizhi.ocean.wavecenter.service.activitycenter.convert;

import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.OfficialOptionalTimeBean;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.ActivityOptionalOfficialTimeDTO;

@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.ERROR,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ActivityOfficialTimeConvert {

    ActivityOfficialTimeConvert I = Mappers.getMapper(ActivityOfficialTimeConvert.class);


    OfficialOptionalTimeBean officialOptionalTimeDTO2Bean(ActivityOptionalOfficialTimeDTO activityOptionalOfficialTimeDTO);

}
