package fm.lizhi.ocean.wavecenter.service.user.constants;

import java.util.HashMap;
import java.util.Map;

/**
 * 搜索类型
 */
public enum SearchType {
    /**
     * 搜索首次认证结果
     */
    RESULT(0, "搜索首次认证结果"),
    /**
     * 搜索首次认证记录
     */
    RECORD(1, "搜索首次认证记录"),
    /**
     * 搜索多次认证记录
     */
    RECORD_MANY(2, "搜索多次认证记录"),
    ;
    private int value;
    private String msg;

    private static Map<Integer, SearchType> map = new HashMap<>();

    static {
        for (SearchType object : SearchType.values()) {
            map.put(object.getValue(), object);
        }
    }

    SearchType(int value, String msg) {
        this.value = value;
        this.msg = msg;
    }

    public int getValue() {
        return value;
    }

    public String getMsg() {
        return msg;
    }

    /**
     * 根据值类型找枚举
     *
     * @param value 值
     * @return
     */
    public static SearchType from(int value) {
        return map.get(value);
    }
}