package fm.lizhi.ocean.wavecenter.service.background.activitycenter.convert;

import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.DecorateBean;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.resource.constants.PlatformDecorateTypeEnum;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.bean.DecorateInfoBean;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.model.dto.DecorateDTO;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.model.request.RequestGetDecorate;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ActivityDecorateConvert {
    ActivityDecorateConvert I = Mappers.getMapper(ActivityDecorateConvert.class);

    RequestGetDecorate convertRequestGetDecorate(fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestGetDecorate param);

    PageBean<DecorateBean> decorateDTO2DecorateBeanList(PageBean<DecorateDTO> target);

    List<DecorateBean> decorateDTO2DecorateBeanList(List<DecorateDTO> target);

    @Mappings({
            @Mapping(target = "id", source = "decorateId"),
            @Mapping(target = "name", source = "decorateName"),
            @Mapping(target = "type", source = "decorateTypeEnum"),
            @Mapping(target = "previewUrl", source = "previewUrl"),
    })
    DecorateBean decorateInfoBean2DecorateBean(DecorateInfoBean decorateInfoBean);

    List<DecorateBean> decorateInfoBean2DecorateBeans(List<DecorateInfoBean> list);

    default Integer decorateTypeEnumToType(PlatformDecorateTypeEnum decorateTypeEnum) {
        return decorateTypeEnum != null ? decorateTypeEnum.getType() : null;
    }
}
