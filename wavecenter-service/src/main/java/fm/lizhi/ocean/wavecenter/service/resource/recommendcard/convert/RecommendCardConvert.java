package fm.lizhi.ocean.wavecenter.service.resource.recommendcard.convert;

import fm.lizhi.ocean.wavecenter.api.resource.recommendcard.bean.RecommendCardUseRecordBean;
import fm.lizhi.ocean.wavecenter.service.resource.recommendcard.dto.RecommendCardUseRecordDTO;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/25 19:34
 */
@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface RecommendCardConvert {

    RecommendCardConvert I = Mappers.getMapper(RecommendCardConvert.class);

    @Mappings({
            @Mapping(source = "nums", target = "useNum"),
            @Mapping(source = "recommendTime", target = "recommendationTime"),
    })
    RecommendCardUseRecordBean useRecordDto2Bean(RecommendCardUseRecordDTO dto);

    List<RecommendCardUseRecordBean> useRecordDtos2Beans(List<RecommendCardUseRecordDTO> dtos);

}
