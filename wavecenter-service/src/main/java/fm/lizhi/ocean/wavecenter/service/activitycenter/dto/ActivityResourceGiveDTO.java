package fm.lizhi.ocean.wavecenter.service.activitycenter.dto;

import lombok.Data;

import java.util.Date;

@Data
public class ActivityResourceGiveDTO {

    /**
     * 主键
     */
    private Long id;

    /**
     * 要发放的资源ID
     */
    private Long resourceId;

    /**
     * 应用ID
     */
    private Integer appId;

    /**
     * 活动名称
     */
    private String activityName;

    /**
     * 活动ID
     */
    private Long activityId;

    /**
     * 活动开始时间
     */
    private Date startTime;

    /**
     * 活动结束时间
     */
    private Date endTime;

    /**
     * 状态，0：待发放，1：发放失败，2：超时失败，3：成功
     */
    private Integer status;

    /**
     * 错误码
     */
    private Integer errorCode;

    /**
     * 错误描述
     */
    private String errorMsg;

    /**
     * 重试次数
     */
    private Integer tryCount;

    /**
     * 资源类型，1：装扮，2：流量资源
     */
    private Integer type;

    /**
     * 可选值  TEST/PRE/PRO
     */
    private String deployEnv;

    /**
     * 修改时间
     */
    private Date modifyTime;

    /**
     * 创建时间
     */
    private Date createTime;


}
