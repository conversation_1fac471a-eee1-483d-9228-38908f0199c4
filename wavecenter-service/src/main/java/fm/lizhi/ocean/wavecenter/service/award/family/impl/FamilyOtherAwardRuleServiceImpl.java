package fm.lizhi.ocean.wavecenter.service.award.family.impl;

import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.award.family.bean.ListFamilySpecialRecommendCardNameBean;
import fm.lizhi.ocean.wavecenter.api.award.family.request.RequestClearFamilySpecialRecommendCardName;
import fm.lizhi.ocean.wavecenter.api.award.family.request.RequestListFamilySpecialRecommendCardName;
import fm.lizhi.ocean.wavecenter.api.award.family.request.RequestUploadFamilySpecialRecommendCardName;
import fm.lizhi.ocean.wavecenter.api.award.family.service.FamilyOtherAwardRuleService;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.base.util.JsonUtils;
import fm.lizhi.ocean.wavecenter.service.award.family.manager.FamilyOtherAwardRuleManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
@ServiceProvider
public class FamilyOtherAwardRuleServiceImpl implements FamilyOtherAwardRuleService {

    @Autowired
    private FamilyOtherAwardRuleManager familyOtherAwardRuleManager;

    @Override
    public Result<Void> uploadFamilySpecialRecommendCardName(RequestUploadFamilySpecialRecommendCardName request) {
        LogContext.addReqLog("request={}", JsonUtils.toJsonString(request));
        LogContext.addResLog("request={}", JsonUtils.toJsonString(request));
        return familyOtherAwardRuleManager.uploadFamilySpecialRecommendCardName(request);
    }

    @Override
    public Result<Void> clearFamilySpecialRecommendCardName(RequestClearFamilySpecialRecommendCardName request) {
        LogContext.addReqLog("request={}", JsonUtils.toJsonString(request));
        LogContext.addResLog("request={}", JsonUtils.toJsonString(request));
        return familyOtherAwardRuleManager.clearFamilySpecialRecommendCardName(request);
    }

    @Override
    public Result<PageBean<ListFamilySpecialRecommendCardNameBean>> listFamilySpecialRecommendCardName(RequestListFamilySpecialRecommendCardName request) {
        LogContext.addReqLog("request={}", JsonUtils.toJsonString(request));
        LogContext.addResLog("request={}", JsonUtils.toJsonString(request));
        Result<PageBean<ListFamilySpecialRecommendCardNameBean>> result = familyOtherAwardRuleManager.listFamilySpecialRecommendCardName(request);
        if (RpcResult.isSuccess(result)) {
            PageBean<ListFamilySpecialRecommendCardNameBean> pageBean = result.target();
            LogContext.addResLog("total={}`listSize={}", pageBean.getTotal(), pageBean.getList().size());
            if (log.isDebugEnabled()) {
                log.debug("listFamilySpecialRecommendCardName response={}", pageBean);
            }
        }
        return result;
    }
}
