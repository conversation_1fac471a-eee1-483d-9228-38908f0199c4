package fm.lizhi.ocean.wavecenter.service.datacenter.handler;

import fm.lizhi.commons.util.DateUtil;
import fm.lizhi.ocean.wavecenter.api.common.constants.DateType;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.CountDataBean;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.IGetKeyIndicatorsParam;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.IndicatorBean;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.IndicatorExtensionBean;
import fm.lizhi.ocean.wavecenter.base.util.CalculateUtil;
import fm.lizhi.ocean.wavecenter.common.utils.MyDateUtil;
import fm.lizhi.ocean.wavecenter.service.datacenter.manager.DataMetricsManager;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/4/27 10:55
 */
public abstract class AbsDataHandler<P extends IGetKeyIndicatorsParam, D> {

    @Autowired
    private DataMetricsManager dataMetricsManager;

    /**
     * 查询天关键指标
     * @param paramBean
     * @param day
     * @param queryValueMetrics
     * @return
     */
    protected abstract Map<String, String> getDayKeyIndicators(P paramBean, Date day, List<String> queryValueMetrics);

    /**
     * 查询周关键指标
     * @param paramBean
     * @param startDay
     * @param endDay
     * @param queryValueMetrics
     * @return
     */
    protected abstract Map<String, String> getWeekKeyIndicators(P paramBean, Date startDay, Date endDay, List<String> queryValueMetrics);

    /**
     * 查询月关键指标
     * @param paramBean
     * @param month
     * @param queryValueMetrics
     * @return
     */
    protected abstract Map<String, String> getMonthKeyIndicators(P paramBean, Date month, List<String> queryValueMetrics);

    /**
     * 查询日统计数据
     * @param paramBean
     * @param dayValues
     * @return key=日期，value=数据
     */
    protected abstract Map<Integer, D> getDaysData(IndicatorTrendParam paramBean, List<String> metrics, List<Integer> dayValues);

    /**
     * 关键指标趋势图数据统计
     * @param paramBean
     * @param metrics 需要查询的指标
     * @param dayValues 需要查询的日期
     * @return
     */
    public Map<String, List<CountDataBean>> getIndicatorTrend(IndicatorTrendParam paramBean, List<String> metrics, List<Integer> dayValues){

        //查询数据
        Map<Integer, D> daysData = getDaysData(paramBean, metrics, dayValues);

        //格式化或者计算
        Map<String, List<CountDataBean>> resultMap = new HashMap<>();
        for (String metric : metrics) {
            List<CountDataBean> result = new ArrayList<>(dayValues.size());
            for (Integer date : dayValues) {
                CountDataBean countDataBean = new CountDataBean();
                countDataBean.setDate(MyDateUtil.getDayValueDate(date));
                D data = daysData.get(date);
                if (data != null) {
                    String value = dataMetricsManager.formatMetricsValue(data, metric);
                    countDataBean.setValue(value);
                } else {
                    countDataBean.setValue("0");
                }
                result.add(countDataBean);
            }
            resultMap.put(metric, result);
        }

        return resultMap;
    }

    public Map<String, List<CountDataBean>> getIndicatorTrend(IndicatorTrendParam paramBean, List<String> metrics, int days){
        Date yesterday = DateUtil.getDayBefore(1);
        List<Integer> dates = new ArrayList<>(days);
        dates.add(MyDateUtil.getDateDayValue(yesterday));
        for (int i = 1; i < days; i++) {
            Date dayBefore = DateUtil.getDayBefore(yesterday, i);
            dates.add(MyDateUtil.getDateDayValue(dayBefore));
        }
        return getIndicatorTrend(paramBean, metrics, dates);
    }

    /**
     * 查询关键指标数据
     * @param paramBean
     * @return
     */
    public List<IndicatorBean> getKeyIndicators(P paramBean) {
        //参数校验
        DateType dateType = paramBean.getDateType();
        List<String> valueMetrics = paramBean.getValueMetrics();
        List<String> ratioMetrics = paramBean.getRatioMetrics();

        List<String> queryValueMetrics = new ArrayList<>();
        queryValueMetrics.addAll(valueMetrics);
        queryValueMetrics.addAll(ratioMetrics);
        if (CollectionUtils.isEmpty(queryValueMetrics)) {
            return Collections.emptyList();
        }

        //根据日期类型查询不同的表
        //日
        Date startDate = DateUtil.formatStrToDate(paramBean.getStartDate(), DateUtil.date_2);
        Map<String, String> indicatorsMap = new HashMap<>();
        if (dateType == DateType.DAY) {
            indicatorsMap = getDayKeyIndicators(paramBean, startDate, queryValueMetrics);
        }

        //周
        if (dateType == DateType.WEEK) {
            Date endDate = DateUtil.formatStrToDate(paramBean.getEndDate(), DateUtil.date_2);
            indicatorsMap = getWeekKeyIndicators(paramBean, startDate, endDate, queryValueMetrics);
        }

        //月
        if (dateType == DateType.MONTH) {
            indicatorsMap = getMonthKeyIndicators(paramBean, startDate, queryValueMetrics);
        }

        //转换数据
        List<IndicatorBean> indicatorBeanList = new ArrayList<>();
        for (Map.Entry<String, String> entry : indicatorsMap.entrySet()) {
            IndicatorBean indicatorBean = new IndicatorBean();
            indicatorBean.setMetric(entry.getKey());
            indicatorBean.setIndicator(entry.getValue());
            indicatorBeanList.add(indicatorBean);
        }

        //计算环比周期
        if (CollectionUtils.isNotEmpty(ratioMetrics)) {
            Map<String, IndicatorBean> indicatorMap = indicatorBeanList.stream().collect(Collectors.toMap(IndicatorBean::getMetric, v -> v));
            Map<String, String> beforeMap = new HashMap<>();
            //计算环比
            if (dateType == DateType.DAY) {
                Date dayBefore = DateUtil.getDayBefore(startDate, 1);
                beforeMap = getDayKeyIndicators(paramBean, dayBefore, queryValueMetrics);
            }

            if (dateType == DateType.WEEK) {
                Date startBefore = DateUtil.getDayBefore(startDate, 7);
                Date endDate = DateUtil.formatStrToDate(paramBean.getEndDate(), DateUtil.date_2);
                Date endBefore = DateUtil.getDayBefore(endDate, 7);
                beforeMap = getWeekKeyIndicators(paramBean, startBefore, endBefore, queryValueMetrics);
            }

            if (dateType == DateType.MONTH) {
                Date monthBefore = DateUtil.getMonthBefore(startDate, 1);
                beforeMap = getMonthKeyIndicators(paramBean, monthBefore, queryValueMetrics);
            }

            for (String ratioMetric : ratioMetrics) {
                IndicatorBean indicatorBean = indicatorMap.get(ratioMetric);
                if (indicatorBean == null) {
                    continue;
                }
                String valueStr = beforeMap.get(ratioMetric);
                IndicatorExtensionBean indicatorExtensionBean = new IndicatorExtensionBean();
                indicatorExtensionBean.setPreIndicator(valueStr);
                indicatorExtensionBean.setRatio(CalculateUtil.relativeRatio(valueStr, indicatorBean.getIndicator()));
                indicatorBean.setExtension(indicatorExtensionBean);
            }
        }

        return indicatorBeanList;
    }
}
