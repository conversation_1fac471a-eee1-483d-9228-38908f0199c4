package fm.lizhi.ocean.wavecenter.service.grow.manager;

import fm.lizhi.ocean.wavecenter.service.grow.dto.FamilyLevelDTO;

import java.util.Date;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025/3/20 15:53
 */
public interface FamilyLevelManager {

    /**
     * 查询公会周期等级
     * @param familyId
     * @param startTime
     * @return
     */
    Optional<FamilyLevelDTO> getFamilyLevel(Long familyId, Date startTime);

}
