package fm.lizhi.ocean.wavecenter.service.anchor.singer.dto;

import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerRecordOperateTypeEnum;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 构建歌手操作记录参数
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class BatchBuildSingerOperateRecordParamDTO {

    /**
     * 歌手信息
     */
    List<SingerInfoDTO> singerInfoList;
    /**
     * 操作类型
     */
    private SingerRecordOperateTypeEnum operateType;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 淘汰原因
     */
    private String eliminationReason;
}
