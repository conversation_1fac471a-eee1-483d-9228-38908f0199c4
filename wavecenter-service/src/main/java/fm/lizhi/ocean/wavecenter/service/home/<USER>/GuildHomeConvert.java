package fm.lizhi.ocean.wavecenter.service.home.convert;

import fm.lizhi.ocean.wavecenter.api.datacenter.bean.IncomeBean;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.RoomPerformanceBean;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.RoomPerformanceStatisticsBean;
import fm.lizhi.ocean.wavecenter.api.home.bean.MetricsDataBean;
import fm.lizhi.ocean.wavecenter.api.home.bean.TrendChartBean;
import fm.lizhi.ocean.wavecenter.api.home.response.ResponseGuildKeyDataSummary;
import fm.lizhi.ocean.wavecenter.api.home.response.ResponseGuildKeyDataTrendChart;
import fm.lizhi.ocean.wavecenter.api.home.response.ResponseGuildMarketMonitorSummary;
import fm.lizhi.ocean.wavecenter.api.home.response.ResponseGuildMarketMonitorTrendChart;
import fm.lizhi.ocean.wavecenter.service.datacenter.dto.DataRoomFamilyDayDTO;
import fm.lizhi.ocean.wavecenter.service.home.dto.GuildKeyDataSummaryDTO;
import fm.lizhi.ocean.wavecenter.service.home.dto.GuildKeyDataTrendChartDTO;
import fm.lizhi.ocean.wavecenter.service.home.dto.GuildMarketMonitorSummaryDTO;
import fm.lizhi.ocean.wavecenter.service.home.dto.GuildMarketMonitorTrendChartDTO;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.Date;
import java.util.List;

@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface GuildHomeConvert {

    GuildHomeConvert I = Mappers.getMapper(GuildHomeConvert.class);

    default Long dateToLong(Date date) {
        return date.getTime();
    }

    ResponseGuildKeyDataSummary toResponseGuildKeyDataSummary(GuildKeyDataSummaryDTO dto);


    @Mappings({
            @Mapping(target = "pre", source = "bean.per"),
    })
    MetricsDataBean buildMetricsDataBean(IncomeBean bean, Date startTime, Date endTime);

    List<ResponseGuildKeyDataTrendChart> toResponseGuildKeyDataTrendChart(List<GuildKeyDataTrendChartDTO> dto);

    @Mappings({
            @Mapping(target = "ratio", expression = "java(fm.lizhi.ocean.wavecenter.base.util.CalculateUtil.relativeRatio(pre, current))"),
            @Mapping(target = "warn", expression = "java(new java.math.BigDecimal(ratioWarning).compareTo(fm.lizhi.ocean.wavecenter.base.util.CalculateUtil.relativeRatioNotFormat(pre, current)) > 0)"),

    })
    TrendChartBean buildTrendChartBean(String current, String pre, Double ratioWarning);

    ResponseGuildMarketMonitorSummary toResponseGuildMarketMonitorSummary(GuildMarketMonitorSummaryDTO dto);

    List<ResponseGuildMarketMonitorTrendChart> toResponseGuildMarketMonitorTrendChart(List<GuildMarketMonitorTrendChartDTO> dto);


    @Mappings({
            @Mapping(source = "sumIncome", target = "allIncome"),
    })
    DataRoomFamilyDayDTO buildDataRoomFamilyDay(RoomPerformanceStatisticsBean bean);
    List<DataRoomFamilyDayDTO> buildDataRoomFamilyDays(List<RoomPerformanceStatisticsBean> list);
}
