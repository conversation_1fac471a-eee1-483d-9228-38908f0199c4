package fm.lizhi.ocean.wavecenter.service.activitycenter.dto;

import java.util.List;

import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityFlowResourceBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityProcessBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityApplyTypeEnum;
import lombok.Data;

/**
 * 活动参数DTO
 */
@Data
public class ActivityParamDTO {


    /**
     * 活动ID
     */
    private Long activityId;

    /**
     * 应用ID
     */
    private Integer appId;

    /**
     * 业务定义不超过10个字，稍微冗余
     */
    private String name;

    /**
     * 提报厅厅主ID
     */
    private Long njId;

    /**
     * 活动分类ID
     */
    private Long classId;

    /**
     * 活动开始时间
     */
    private Long startTime;

    /**
     * 活动结束时间
     */
    private Long endTime;

    /**
     * 申请类型，1：自主提报，2: 官方活动
     */
    private ActivityApplyTypeEnum applyType;

    /**
     * 联系人
     */
    private String contact;

    /**
     * 申请者uid
     */
    private Long applicantUid;

    /**
     * 联系方式
     */
    private String contactNumber;

    /**
     * 主持人ID
     */
    private Long hostId;

    /**
     * 陪档主播ID列表
     */
    private List<Long> accompanyNjIds;

    /**
     * 活动目标，不超过100字
     */
    private String goal;

    /**
     * 活动介绍，不超过100字
     */
    private String introduction;

    /**
     * 活动辅助道具图片地址，多个逗号分隔
     */
    private List<String> auxiliaryPropUrl;

    /**
     * 活动海报图片地址
     */
    private String posterUrl;

    /**
     * 玩法工具
     */
    private List<Integer> activityTool;

    /**
     * 房间公告，不超过500字
     */
    private String roomAnnouncement;

    /**
     * 房间公告图片，不超过3个
     */
    private List<String> roomAnnouncementImgUrl;

    /**
     * 房间背景ID列表
     */
    private List<Long> roomBackgroundIds;

    /**
     * 头像框ID列表
     */
    private List<Long> avatarWidgetIds;

    /**
     * 流量资源列表
     */
    private List<ActivityFlowResourceBean> flowResources;

    /**
     * 活动环节列表
     */
    private List<ActivityProcessBean> processList;

    /**
     * 模板ID
     */
    private Long templateId;

    /**
     * 最大官频位数量
     */
    private Integer maxSeatCount = 0;

    /**
     * 版本号
     */
    private Integer version = 0;

    /**
     * 是否来自后台的修改请求
     */
    private boolean fromBackend;

    /**
     * 活动类型
     * @see fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityApplyModelEnum
     */
    private Integer model;
}
