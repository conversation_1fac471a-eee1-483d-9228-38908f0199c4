package fm.lizhi.ocean.wavecenter.service.home.manager;

import fm.lizhi.ocean.wavecenter.api.home.request.RequestGetRoomKeyDataSummary;
import fm.lizhi.ocean.wavecenter.api.home.request.RequestGetRoomKeyDataTrendChart;
import fm.lizhi.ocean.wavecenter.api.home.request.RequestGetRoomMsgAnalysisPerformance;
import fm.lizhi.ocean.wavecenter.service.home.dto.RoomKeyDataSummaryDTO;
import fm.lizhi.ocean.wavecenter.service.home.dto.RoomKeyDataTrendChartDTO;
import fm.lizhi.ocean.wavecenter.service.home.dto.RoomMsgAnalysisPerformanceDTO;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface RoomHomeManager {


    /**
     * 获取房间关键数据
     */
    RoomKeyDataSummaryDTO getRoomKeyDataSummary(RequestGetRoomKeyDataSummary request);

    /**
     * 获取房间关键数据趋势图
     */
    List<RoomKeyDataTrendChartDTO> getRoomKeyDataTrendChart(RequestGetRoomKeyDataTrendChart request);

    /**
     * 获取私信拓客漏斗表现
     */
    RoomMsgAnalysisPerformanceDTO getRoomMsgAnalysisPerformance(RequestGetRoomMsgAnalysisPerformance request);
}
