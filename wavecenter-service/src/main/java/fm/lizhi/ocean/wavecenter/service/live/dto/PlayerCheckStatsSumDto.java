package fm.lizhi.ocean.wavecenter.service.live.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/6/12 20:30
 */
@Data
@Accessors(chain = true)
public class PlayerCheckStatsSumDto {
    private BigDecimal income;

    private Long charm;

    private Integer seatOrder;

    private Integer hostCnt;

    public static PlayerCheckStatsSumDto zero(){
        return new PlayerCheckStatsSumDto()
                .setIncome(BigDecimal.ZERO)
                .setCharm(0L)
                .setSeatOrder(0)
                .setHostCnt(0);
    }
}
