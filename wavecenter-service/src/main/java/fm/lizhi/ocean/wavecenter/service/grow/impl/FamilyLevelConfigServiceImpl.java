package fm.lizhi.ocean.wavecenter.service.grow.impl;

import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.grow.level.bean.FamilyLevelConfigAwardBean;
import fm.lizhi.ocean.wavecenter.api.grow.level.bean.FamilyLevelConfigBean;
import fm.lizhi.ocean.wavecenter.api.grow.level.request.RequestDeleteFamilyLevelConfig;
import fm.lizhi.ocean.wavecenter.api.grow.level.request.RequestGetFamilyLevelConfigList;
import fm.lizhi.ocean.wavecenter.api.grow.level.request.RequestSaveFamilyLevelConfig;
import fm.lizhi.ocean.wavecenter.api.grow.level.service.FamilyLevelConfigService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.domain.grow.entity.FamilyLevel;
import fm.lizhi.ocean.wavecenter.domain.grow.entity.FamilyLevelMedia;
import fm.lizhi.ocean.wavecenter.domain.grow.repository.FamilyLevelRepository;
import fm.lizhi.ocean.wavecenter.domain.system.Visitor;
import fm.lizhi.ocean.wavecenter.service.common.manager.IdManager;
import fm.lizhi.ocean.wavecenter.service.grow.manager.FamilyLevelConfigManager;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025/3/18 17:17
 */
@ServiceProvider
public class FamilyLevelConfigServiceImpl implements FamilyLevelConfigService {

    @Autowired
    private FamilyLevelConfigManager familyLevelConfigManager;
    @Autowired
    private FamilyLevelRepository familyLevelRepository;
    @Autowired
    private IdManager idManager;

    @Override
    public Result<Void> save(RequestSaveFamilyLevelConfig request) {
        LogContext.addReqLog("request={}", request);
        LogContext.addResLog("request={}", request);

        // 新增检查名称是否已存在，检查未删除的数据
        if (request.getId() == null) {
            List<FamilyLevelConfigBean> list = familyLevelConfigManager.getList(new RequestGetFamilyLevelConfigList()
                    .setName(request.getLevelName())
                    .setAppId(request.getAppId())
            );
            if (CollectionUtils.isNotEmpty(list)) {
                return RpcResult.fail(SAVE_LEVEL_EXIST);
            }
        }

        // 检查经验范围是否覆盖到旧的等级数据
        List<FamilyLevel> levels = familyLevelRepository.getAppFamilyLevels(request.getAppId());
        for (FamilyLevel level : levels) {
            if (request.getId() != null && request.getId().equals(level.getId())) {
                // 编辑的时候跳过自己
                continue;
            }
            if (Objects.equals(request.getMinIncome(), level.getWeekMinIncome())) {
                return RpcResult.fail(SAVE_LEVEL_INCOME_COVER);
            }
        }

        Long id = request.getId();
        if (id == null) {
            // ID复用已被删除的同名旧等级
            Optional<FamilyLevelConfigBean> optional = familyLevelConfigManager.getDeletedByName(request.getLevelName());
            if (optional.isPresent()) {
                id = optional.get().getId();
            } else {
                id = idManager.genId();
            }
        }

        FamilyLevelMedia.FamilyLevelMediaBuilder mediaBuilder = FamilyLevelMedia.builder();
        mediaBuilder.awardImgs(request.getAwardImgs())
                .backgroundColor(request.getBackgroundColor())
                .iconUrl(request.getLevelIcon())
                .medalUrl(request.getLevelMedal())
                .themColor(request.getThemColor());

        FamilyLevel familyLevel = new FamilyLevel(id
                , request.getAppId()
                , request.getLevelName()
                , request.getMinIncome()
                , mediaBuilder.build());

        Visitor visitor = new Visitor(request.getOperator(), request.getAppId());

        familyLevelRepository.save(familyLevel, visitor);

        return RpcResult.success();
    }

    @Override
    public Result<Void> delete(RequestDeleteFamilyLevelConfig request) {
        LogContext.addReqLog("request={}", request);
        LogContext.addResLog("request={}", request);
        // 删除
        familyLevelConfigManager.delete(request);
        return RpcResult.success();
    }

    @Override
    public Result<List<FamilyLevelConfigAwardBean>> list(RequestGetFamilyLevelConfigList request) {
        LogContext.addReqLog("request={}", request);
        LogContext.addResLog("request={}", request);
        List<FamilyLevelConfigAwardBean> awardlist = familyLevelConfigManager.getAwardlist(request);
        return RpcResult.success(awardlist);
    }
}
