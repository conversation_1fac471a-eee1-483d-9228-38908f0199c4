package fm.lizhi.ocean.wavecenter.service.home.manager;

import fm.lizhi.ocean.wavecenter.api.home.bean.MetricsDataBean;
import fm.lizhi.ocean.wavecenter.api.home.request.RequestGetGuildKeyDataSummary;
import fm.lizhi.ocean.wavecenter.api.home.request.RequestGetGuildKeyDataTrendChart;
import fm.lizhi.ocean.wavecenter.api.home.request.RequestGetGuildMarketMonitorSummary;
import fm.lizhi.ocean.wavecenter.api.home.request.RequestGetGuildMarketMonitorTrendChart;
import fm.lizhi.ocean.wavecenter.service.home.dto.GuildKeyDataTrendChartDTO;
import fm.lizhi.ocean.wavecenter.service.home.dto.GuildMarketMonitorSummaryDTO;
import fm.lizhi.ocean.wavecenter.service.home.dto.GuildMarketMonitorTrendChartDTO;

import java.util.List;

/**
 * 公会首页
 * <AUTHOR>
 */
public interface GuildHomeManager {

    /**
     * 关键数据汇总
     */
    MetricsDataBean getKeyDataSummary(RequestGetGuildKeyDataSummary request);

    /**
     * 关键数据趋势图
     */
    List<GuildKeyDataTrendChartDTO> getKeyDataTrendChart(RequestGetGuildKeyDataTrendChart request);

    /**
     * 公会大盘监控
     */
    GuildMarketMonitorSummaryDTO getMarketMonitorSummary(RequestGetGuildMarketMonitorSummary request);

    /**
     * 公会大盘监控-趋势图-厅收入
     */
    List<GuildMarketMonitorTrendChartDTO> getMarketMonitorTrendChartByRoomIncome(RequestGetGuildMarketMonitorTrendChart request);

    /**
     * 公会大盘监控-趋势图-累计收入
     */
    List<GuildMarketMonitorTrendChartDTO> getMarketMonitorTrendChartByRoomSumIncome(RequestGetGuildMarketMonitorTrendChart request);
}
