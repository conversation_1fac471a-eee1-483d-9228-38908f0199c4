package fm.lizhi.ocean.wavecenter.service.resource.config;

import fm.lizhi.ocean.lamp.common.config.annotation.JsonStringProperty;
import fm.lizhi.ocean.wavecenter.service.resource.shortnumber.dto.ShortNumberDTO;
import lombok.Data;

import java.util.Collections;
import java.util.List;

@Data
public class PpResourceConfig implements BizResourceConfig {

    @JsonStringProperty
    private List<ShortNumberDTO> shortNumberConfigs = Collections.emptyList();

    /**
     * 推荐卡名称列表
     */
    private String recommendCardPositionNameConfig = "热一A,热一B,热二A,热二B,热三A,热三B,热四A,热四B";
}
