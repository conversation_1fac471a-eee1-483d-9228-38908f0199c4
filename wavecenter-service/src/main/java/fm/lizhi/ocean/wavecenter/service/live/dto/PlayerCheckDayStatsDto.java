package fm.lizhi.ocean.wavecenter.service.live.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/6/11 16:24
 */
@Data
@Accessors(chain = true)
public class PlayerCheckDayStatsDto {
    /**
     * 天
     */
    private Date day;

    /**
     * 收入
     */
    private BigDecimal income;

    /**
     * 魅力值
     */
    private Long charm;

    /**
     * 麦序
     */
    private Integer seatOrder;

    /**
     * 上麦时长
     */
    private Integer upGuestDur;

    /**
     * 主持档
     */
    private Integer hostCnt;
}
