package fm.lizhi.ocean.wavecenter.service.grow.config;

import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.common.config.AbsBizConfig;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * <AUTHOR>
 * @date 2025/3/19 14:16
 */
@Data
@ConfigurationProperties(prefix = "wavecenter-grow")
public class GrowConfig extends AbsBizConfig<BizGrowConfig> {

    /**
     * 公会结算标识缓存时间
     */
    private Integer settleFamilyLevelTagExpireSeconds = 60 * 60 * 24 * 7;

    private HyGrowConfig hy;

    private PpGrowConfig pp;

    private XmGrowConfig xm;

    public GrowConfig() {
        PpGrowConfig ppConfig = new PpGrowConfig();
        XmGrowConfig xmConfig = new XmGrowConfig();
        HyGrowConfig hyConfig = new HyGrowConfig();
        this.pp = ppConfig;
        this.xm = xmConfig;
        this.hy = hyConfig;
        bizConfigMap.put(BusinessEvnEnum.PP.appId(), ppConfig);
        bizConfigMap.put(BusinessEvnEnum.XIMI.appId(), xmConfig);
        bizConfigMap.put(BusinessEvnEnum.HEI_YE.appId(), hyConfig);
    }

}
