package fm.lizhi.ocean.wavecenter.api.home.bean;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 基础指标
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class MetricsDataBean {

    /**
     * 开始时间
     */
    private Long startTime;

    /**
     * 结束时间
     */
    private Long endTime;


    /**
     * 当前值
     */
    private Double current;

    /**
     * 上一期的值
     */
    private Double pre;

    /**
     * 环比
     */
    private String ratio;


}
