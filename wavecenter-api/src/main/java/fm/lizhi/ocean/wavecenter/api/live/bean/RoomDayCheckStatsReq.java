package fm.lizhi.ocean.wavecenter.api.live.bean;


import fm.lizhi.ocean.wavecenter.api.common.util.ApiAssert;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class RoomDayCheckStatsReq {

    private Integer appId;
    private Long roomId;
    private Long userId;
    private Date startDate;
    private Date endDate;
    private Integer pageNo;
    private Integer pageSize;

    public static class RoomDayCheckStatsReqBuilder {
        public RoomDayCheckStatsReq build() {
            ApiAssert.notNull(appId, "appId is required");
            ApiAssert.notNull(startDate, "startDate is required");
            ApiAssert.notNull(endDate, "endDate is required");
            if (pageNo == null) {
                pageNo = 1;
            }
            if (pageSize == null) {
                pageSize = 20;
            }
            return new RoomDayCheckStatsReq(appId, roomId, userId, startDate, endDate, pageNo, pageSize);
        }
    }
}
