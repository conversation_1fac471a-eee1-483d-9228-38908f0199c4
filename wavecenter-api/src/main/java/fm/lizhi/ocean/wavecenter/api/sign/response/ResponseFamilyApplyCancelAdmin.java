package fm.lizhi.ocean.wavecenter.api.sign.response;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2024/10/25 19:52
 */
@Data
@Accessors(chain = true)
public class ResponseFamilyApplyCancelAdmin implements ResponseSignResult {

    private Integer code = SUCCESS_CODE;

    private String msg;

    private String contractUrl;

    private Long contractId;

    @Override
    public int getResultCode() {
        return this.code;
    }

    @Override
    public String getResultMsg() {
        return this.msg;
    }

}
