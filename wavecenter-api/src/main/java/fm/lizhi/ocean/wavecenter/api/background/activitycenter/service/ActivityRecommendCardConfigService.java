package fm.lizhi.ocean.wavecenter.api.background.activitycenter.service;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.bean.ActivityRecommendCardConfigBean;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestSaveActivityRecommendCard;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestUpdateActivityRecommendCard;

import java.util.List;

/**
 * 推荐卡
 * <AUTHOR>
 */
public interface ActivityRecommendCardConfigService {


    /**
     * 保存
     * <p>
     * 额外关注 {@link ActivityRecommendCardConfigService#RECOMMEND_CARD_REPEAT} & {@link ActivityRecommendCardConfigService#RECOMMEND_CARD_LEVEL_NOT_EXIST}
     */
    Result<Void> saveRecommendCard(RequestSaveActivityRecommendCard param);


    /**
     * 更新
     *
     * <p>
     * 额外关注 {@link ActivityRecommendCardConfigService#RECOMMEND_CARD_REPEAT} & {@link ActivityRecommendCardConfigService#RECOMMEND_CARD_LEVEL_NOT_EXIST}
     */
    Result<Void> updateRecommendCard(RequestUpdateActivityRecommendCard param);

    /**
     * 删除
     */
    Result<Void> deleteRecommendCard(Long id, Integer appId, String operator);


    /**
     * 查询列表
     */
    Result<List<ActivityRecommendCardConfigBean>> listByAppId(Integer appId);

    /**
     * 存在重复记录
     */
    int RECOMMEND_CARD_REPEAT = 2110001;

    /**
     * 等级不存在
     */
    int RECOMMEND_CARD_LEVEL_NOT_EXIST = 2110002;

    /**
     * 保存失败
     */
    int SAVE_RECOMMEND_CARD_FAIL = 2110101;

    /**
     * 更新失败
     */
    int UPDATE_RECOMMEND_CARD_FAIL = 2110201;

    /**
     * 删除失败
     */
    int DELETE_RECOMMEND_CARD_FAIL = 2110301;



}
