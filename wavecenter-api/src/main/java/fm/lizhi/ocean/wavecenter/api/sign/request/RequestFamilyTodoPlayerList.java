package fm.lizhi.ocean.wavecenter.api.sign.request;

import fm.lizhi.ocean.wavecenter.api.common.util.ApiAssert;
import lombok.Builder;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/10/16 15:34
 */
@Getter
@Builder
public class RequestFamilyTodoPlayerList {

    private Integer appId;

    private Long familyId;

    public static class RequestFamilyTodoPlayerListBuilder {
        public RequestFamilyTodoPlayerList build() {
            ApiAssert.notNull(appId, "appId is null");
            ApiAssert.notNull(familyId, "familyId is null");
            return new RequestFamilyTodoPlayerList(appId, familyId);
        }
    }

}
