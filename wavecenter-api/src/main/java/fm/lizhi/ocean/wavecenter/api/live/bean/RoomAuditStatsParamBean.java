package fm.lizhi.ocean.wavecenter.api.live.bean;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class RoomAuditStatsParamBean {

    private int appId;

    /**
     * 厅主ID
     */
    private long roomId;

    private Date startTime;

    private Date endTime;
}
