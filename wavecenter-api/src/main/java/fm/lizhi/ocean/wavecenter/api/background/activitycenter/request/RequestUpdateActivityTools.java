package fm.lizhi.ocean.wavecenter.api.background.activitycenter.request;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class RequestUpdateActivityTools {

    /**
     * 主键
     */
    @NotNull(message = "ID不能为空")
    private Long id;

    /**
     * 应用ID
     */
    @NotNull(message = "应用ID不能为空")
    private int appId;

    /**
     * 操作人
     */
    @NotNull(message = "操作人不能为空")
    private String operator;

    /**
     * 工具名称
     */
    private String name;

    /**
     * 类型, 1:玩法; 2: 工具
     */
    private Integer type;

    /**
     * 玩法工具描述
     */
    private String toolDesc;

    /**
     * 状态 0: 下架; 1: 上架
     */
    private Integer status;
}
