package fm.lizhi.ocean.wavecenter.api.live.bean;


import lombok.Data;

import java.math.BigDecimal;

@Data
public class GuildRoomHourStats implements IStatsEle{


    private String income;

    private Long charm;

    /**
     * 麦序
     */
    private Integer seatOrder;

    /**
     * 打卡主播数
     */
    private Integer checkPlayerNumber;


    @Override
    public BigDecimal foundIncomeNum() {
        if (null == income || income.isEmpty()) {
            return BigDecimal.ZERO;
        }
        return new BigDecimal(income);
    }

    @Override
    public String foundIncome() {
        return this.income;
    }

    @Override
    public Integer foundCharm() {
        if (this.charm == null) {
            return 0;
        }
        return Math.toIntExact(this.charm);
    }

    @Override
    public Integer foundSeatOrder() {
        return this.seatOrder;
    }

    @Override
    public Integer foundHostCnt() {
        return null;
    }

    @Override
    public Integer foundCheckPlayerNumber() {
        return this.checkPlayerNumber;
    }
}
