package fm.lizhi.ocean.wavecenter.api.user.service;


import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.common.bean.ContextRequest;
import fm.lizhi.ocean.wavecenter.api.user.bean.*;

/**
 * <AUTHOR>
 * @date 2024/4/10 17:53
 */
public interface UserFamilyService {

    /**
     * 获取用户所在的公会信息
     * @param appId
     * @param userId
     * @return
     */
    Result<UserInFamilyBean> getUserInFamily(int appId, long userId);

    /**
     * 查询用户家族信息
     * @param userId
     * @param contextRequest
     * @return
     */
    Result<UserInFamilyDetailBean> getUserInFamilyDetail(long userId, ContextRequest contextRequest);

    /**
     * 查询用户家族
     * 通过最新签约记录获取
     * @param appId
     * @param userId
     * @return
     */
    Result<Long> getUserFamilyId(int appId, long userId);

    /**
     * 获取陪玩最近签约厅主
     * @param appId
     * @param userId
     * @return
     */
    Result<Long> getUserNj(int appId, long userId);

    /**
     * 查询陪玩在公会下最近签约厅ID
     * @param appId
     * @param familyId
     * @param playerId
     * @return
     */
    Result<Long> getPlayerLastRoom(int appId, long familyId, long playerId);

    /**
     * 用户家族
     *
     * @param appId
     * @param userId
     * @return
     */
    Result<FamilyBean> getUserFamily(int appId, long userId);

    /**
     * 查询家族认证信息
     * @param appId
     * @param familyId
     * @return
     */
    Result<FamilyAuthBean> getUserFamilyAuth(int appId, long familyId);

    /**
     * 获取厅主的签约状态
     *
     * @param appId
     * @param njId
     * @return
     */
    Result<RoomSignBean> getRoomSignInfo(int appId, long familyId, long njId);

    /**
     * 获取家族信息
     * @param appId
     * @param familyId
     * @return
     */
    Result<FamilyBean> getFamily(int appId, long familyId);

    int USER_FAMILY_NOT_FOUND = 10001;

    int FAMILY_NOT_FOUND = 10002;

}
