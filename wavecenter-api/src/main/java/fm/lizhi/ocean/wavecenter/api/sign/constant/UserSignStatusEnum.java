package fm.lizhi.ocean.wavecenter.api.sign.constant;

/**
 * 用户签署状态枚举
 * <AUTHOR>
 * @date 2024/10/9 15:14
 */
public enum UserSignStatusEnum {

    /**
     * 未知
     */
    UN_KNOWN("UN_KNOWN")

    /**
     * 待签署
     */
    , WAIT_SIGN("WAIT_SIGN")

    /**
     * 签署中
     */
    , SIGNING("SIGNING")

    /**
     * 签署成功
     */
    , SIGN_SUCCESS("SIGN_SUCCEED")

    /**
     * 签署失败
     */
    , SIGN_FAILED("SIGN_FAILED")

    /**
     * 拒签
     */
    , REJECT("REJECT")
    ;


    private String code;

    UserSignStatusEnum(String code) {
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public static UserSignStatusEnum from(String code) {
        for (UserSignStatusEnum status : UserSignStatusEnum.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return UN_KNOWN;
    }

}
