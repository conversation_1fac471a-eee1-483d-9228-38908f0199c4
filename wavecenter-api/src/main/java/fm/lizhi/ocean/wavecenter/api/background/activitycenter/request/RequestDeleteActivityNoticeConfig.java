package fm.lizhi.ocean.wavecenter.api.background.activitycenter.request;

import fm.lizhi.ocean.wavecenter.api.common.annotation.AppEnumId;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;


/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class RequestDeleteActivityNoticeConfig {

    @NotNull(message = "id不能为空")
    private Long id;

    @AppEnumId
    private Integer appId;

    /**
     * 操作人
     */
    @NotNull(message = "操作人不能为空")
    private String operator;

}
