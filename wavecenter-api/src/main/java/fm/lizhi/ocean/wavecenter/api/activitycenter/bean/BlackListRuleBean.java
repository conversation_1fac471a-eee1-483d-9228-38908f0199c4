package fm.lizhi.ocean.wavecenter.api.activitycenter.bean;


import fm.lizhi.ocean.wavecenter.api.common.util.ApiAssert;
import lombok.*;

/**
 * 黑名单规则
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BlackListRuleBean extends ActivityRuleBaseAbstractBean {

    /**
     * 用户ID列表
     */
    private String userIds;

    @Override
    public void verify() {
        ApiAssert.notNull(userIds, "轮播厅数不能为空");
    }
}
