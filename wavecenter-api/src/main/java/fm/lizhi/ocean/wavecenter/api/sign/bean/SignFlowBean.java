package fm.lizhi.ocean.wavecenter.api.sign.bean;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2024/10/9 14:48
 */
@Data
@Accessors(chain = true)
public class SignFlowBean {

    private Long flowId;

    private Long contractId;

    /**
     * 状态
     * @see fm.lizhi.ocean.wavecenter.api.sign.constant.SignFlowStatusEnum
     */
    private String status;

    /**
     * 待确认状态
     * @see fm.lizhi.ocean.wavecenter.api.sign.constant.FlowConfirmStatusEnum
     */
    private String confirmStatus;

    /**
     * 发起人角色
     * @see fm.lizhi.ocean.wavecenter.api.permissions.constants.RoleEnum
     */
    private String createRole;

    private String type;

    /**
     * 是否有电子签
     */
    private Integer hasContract;

}
