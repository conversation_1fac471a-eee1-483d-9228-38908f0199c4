package fm.lizhi.ocean.wavecenter.api.live.bean;

import fm.lizhi.ocean.wavecenter.api.common.util.ApiAssert;
import fm.lizhi.ocean.wavecenter.api.user.constants.SingStatusEnum;
import lombok.Builder;
import lombok.Getter;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/6/11 15:53
 */
@Getter
@Builder
public class PlayerCheckHourStatsReq {

    private Integer appId;

    private Long familyId;

    private Long roomId;

    private Long playerId;

    private Date startDay;

    private Date endDay;

    private SingStatusEnum singStatus;

    public static class PlayerCheckHourStatsReqBuilder{

        public PlayerCheckHourStatsReq build(){
            ApiAssert.notNull(appId, "appId is null");
            ApiAssert.notNull(familyId, "familyId is null");
            ApiAssert.notNull(playerId, "playerId is null");
            ApiAssert.notNull(startDay, "startDay is null");
            ApiAssert.notNull(endDay, "endDay is null");
            if (singStatus == null) {
                singStatus = SingStatusEnum.SING;
            }
            return new PlayerCheckHourStatsReq(appId, familyId, roomId, playerId, startDay, endDay, singStatus);
        }

    }

}
