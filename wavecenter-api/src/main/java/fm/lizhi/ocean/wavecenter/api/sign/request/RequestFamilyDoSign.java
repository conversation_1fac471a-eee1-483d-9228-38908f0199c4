package fm.lizhi.ocean.wavecenter.api.sign.request;

import fm.lizhi.ocean.wavecenter.api.common.util.ApiAssert;
import lombok.Builder;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/10/23 20:26
 */
@Getter
@Builder
public class RequestFamilyDoSign {

    private Integer appId;

    private Long familyId;

    private Long curUserId;

    private String signId;

    private Long contractId;

    public static class RequestFamilyDoSignBuilder {
        public RequestFamilyDoSign build() {
            ApiAssert.notNull(appId, "appId is null");
            ApiAssert.notNull(familyId, "familyId is null");
            ApiAssert.notNull(curUserId, "curUserId is null");
            ApiAssert.hasText(signId, "signId is null");
            ApiAssert.notNull(contractId, "contractId is null");
            return new RequestFamilyDoSign(appId, familyId, curUserId, signId, contractId);
        }
    }

}
