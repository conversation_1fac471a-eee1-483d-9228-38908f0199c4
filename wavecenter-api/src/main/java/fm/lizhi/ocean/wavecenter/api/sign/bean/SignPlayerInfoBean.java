package fm.lizhi.ocean.wavecenter.api.sign.bean;

import fm.lizhi.ocean.wavecenter.api.user.bean.UserBean;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/6/13 20:35
 */
@Data
public class SignPlayerInfoBean {

    /**
     * 厅信息
     */
    private UserBean roomInfo;

    /**
     * 主播信息
     */
    private UserBean playerInfo;

    private Integer signStatus;

    /**
     * 结算比例 xx% 仅陪伴和西米有
     */
    private Integer settle;

    /**
     * 签约时间
     */
    private Date signDate;

    /**
     * 到期时间
     */
    private Date expireDate;

    /**
     * 解约时间
     */
    private Date stopDate;

    /**
     * 合同ID
     */
    private Long contractId;

}
