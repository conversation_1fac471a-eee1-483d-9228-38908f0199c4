package fm.lizhi.ocean.wavecenter.api.live.bean;

import lombok.*;

import java.util.Date;

@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AddAuditRecordBean {

    /**
     * 应用ID
     */
    private int appId;

    /**
     * 厅ID
     */
    private Long roomId;

    /**
     * 工会ID
     */
    private Long familyId;

    /**
     * 主播ID
     */
    private Long userId;

    /**
     * 操作类型
     */
    private Integer op;

    /**
     * 操作理由
     */
    private String reason;

    /**
     * 处罚时间
     */
    private Date insertTime;

    /**
     * 处罚时效时间
     */
    private String pushTime;

}
