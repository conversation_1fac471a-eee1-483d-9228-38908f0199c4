package fm.lizhi.ocean.wavecenter.api.activitycenter.response;

import lombok.Data;

import java.util.List;

/**
 * 获取基础活动配置响应, 主要用于活动模板层面的配置
 */
@Data
public class ResponseGetBaseActivityConfig {

    /**
     * 官频位时长限制列表, 单位为分钟
     */
    private List<Integer> officialSeatDurationLimits;

    /**
     * 官频位可选座位号列表
     */
    private List<Integer> officialSeatAvailableNumbers;

    /**
     * 官频位默认选中的座位号列表, 必须是可选座位号列表的子集
     */
    private List<Integer> officialSeatDefaultNumbers;
}
