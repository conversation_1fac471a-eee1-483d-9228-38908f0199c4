package fm.lizhi.ocean.wavecenter.api.datacenter.bean;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/20 12:03
 */
@Data
@Accessors(chain = true)
public class GuildRoomPerformanceResBean {

    private List<RoomPerformanceBean> performances;

    /**
     * 考核周期开始时间
     */
    private Date startDate;

    /**
     * 考核周期结束时间
     */
    private Date endDate;

    private Date flushTime;
}
