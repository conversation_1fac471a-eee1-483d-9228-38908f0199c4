package fm.lizhi.ocean.wavecenter.api.sign.request;

import fm.lizhi.ocean.wavecenter.api.sign.constant.OperateTypeEnum;
import lombok.Builder;
import lombok.Getter;
import lombok.NonNull;

/**
 * <AUTHOR>
 * @date 2024/10/11 20:36
 */
@Getter
@Builder
public class RequestAdminOperateSign {

    @NonNull
    private Integer appId;

    /**
     * 操作用户ID
     */
    @NonNull
    private Long curUserId;

    /**
     * 签约记录ID
     */
    @NonNull
    private Long playerSignId;

    /**
     * 签约状态  同意  不同意
     */
    @NonNull
    private OperateTypeEnum operateType;

}
