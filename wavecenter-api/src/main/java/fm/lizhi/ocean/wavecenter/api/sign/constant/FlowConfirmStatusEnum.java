package fm.lizhi.ocean.wavecenter.api.sign.constant;

/**
 * <AUTHOR>
 * @date 2024/10/26 16:39
 */
public enum FlowConfirmStatusEnum {

    /**
     * 待发起人签署
     */
    WAIT_CREATE_SIGN("WAIT_CREATE_SIGN")

    /**
     * 发起人已确认
     */
    , CREATE_CONFIRM("CREATE_CONFIRM")

    /**
     * 待接受方签署
     */
    , WAIT_TARGET_SIGN("WAIT_TARGET_SIGN")

    /**
     * 接受方已确认
     */
    , TARGET_CONFIRM("TARGET_CONFIRM")

    /**
     * 确认条件不通过
     */
    , CONDITION_NO_PASS("CONDITION_NO_PASS")

    /**
     * 已过期
     */
    , OVERDUE("OVERDUE")

    /**
     * 已执行过
     */
    , INVOKED("INVOKED")

    , CONTRACT_NOT_EXIST("CONTRACT_NOT_EXIST")

    , FAMILY_NOT_EXIST("FAMILY_NOT_EXIST")

    , SIGN_NOT_SUCCESS("SIGN_NOT_SUCCESS")

    , FAMILY_NJ_SIGN_NOT_EXIST("FAMILY_NJ_SIGN_NOT_EXIST")
    , STATUS_ERROR("STATUS_ERROR")
    , IN_CHANGE_COMPANY("IN_CHANGE_COMPANY")
    , CAN_OPEN_NUM_LIMIT("CAN_OPEN_NUM_LIMIT")
    , LIMIT_CHECK("LIMIT_CHECK")
    , UNION_CHECK("UNION_CHECK")
    , SIGN_STATUS_IS_NULL("SIGN_STATUS_IS_NULL")
    , TARGET_REJECT("TARGET_REJECT")
    ;

    private String code;

    FlowConfirmStatusEnum(String code) {
        this.code = code;
    }

    public String getCode() {
        return code;
    }
}
