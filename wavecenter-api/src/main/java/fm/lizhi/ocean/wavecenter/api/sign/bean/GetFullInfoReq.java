package fm.lizhi.ocean.wavecenter.api.sign.bean;

import fm.lizhi.ocean.wavecenter.api.common.util.ApiAssert;
import lombok.Builder;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/6/14 19:11
 */
@Getter
@Builder
public class GetFullInfoReq {

    private Long familyId;

    private Integer appId;

    public static class GetFullInfoReqBuilder{
        public GetFullInfoReq build(){
            ApiAssert.notNull(familyId, "familyId is required");
            ApiAssert.notNull(appId, "appId is required");
            return new GetFullInfoReq(familyId, appId);
        }
    }

}
