package fm.lizhi.ocean.wavecenter.api.background.activitycenter.response;

import fm.lizhi.ocean.wavecenter.api.background.activitycenter.bean.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class ResponseActivityConfigBean {

    /**
     * 应用信息列表
     */
    private List<ActivityBaseConfigBean> appInfoList;

    /**
     * 自动配置资源列表
     */
    private List<ActivityAutoConfigResourceBean> autoConfigResourceList;

    /**
     * 提报限制列表
     */
    private List<ActivityBaseConfigBean> applyRuleList;

    /**
     * 玩法工具列表
     */
    private List<ActivityToolsConfigBean> activityToolList;

    /**
     * 素材分类
     */
    private List<ActivityBaseConfigBean> fodderClassificationList;

    /**
     * 装扮类型
     */
    private List<ActivityBaseConfigBean> decorateTypeList;

    /**
     * 活动模板亮点标签列表
     */
    private List<ActivityTemplateHighlightConfigBean> heightLightList;

    /**
     * 大分类类型列表
     */
    private List<ActivityBigClassTypeConfigBean> bigClassTypeList;

    /**
     * 活动目标
     */
    private List<ActivityBaseConfigBean> activityGoalList;

    /**
     * 房间品类列表
     */
    private List<RoomCategoryBean> roomCategoryList;
}
