package fm.lizhi.ocean.wavecenter.api.background.activitycenter.service;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityToolsInfoBean;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestSaveActivityTools;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestUpdateActivityTools;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface ActivityToolsConfigService {


    /**
     * 保存
     */
    Result<Boolean> saveTools(@Valid RequestSaveActivityTools param);


    /**
     * 更新
     */
    Result<Boolean> updateTools(@Valid RequestUpdateActivityTools param);

    /**
     * 查询列表
     */
    Result<List<ActivityToolsInfoBean>> listByAppId(Integer appId);


    /**
     * 保存失败
     */
    int SAVE_TOOLS_FAIL = 2320001;

    /**
     * 更新失败
     */
    int UPDATE_TOOLS_FAIL = 2320101;
}
