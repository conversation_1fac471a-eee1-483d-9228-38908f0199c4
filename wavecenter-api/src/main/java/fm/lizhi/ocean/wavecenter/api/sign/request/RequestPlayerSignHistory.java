package fm.lizhi.ocean.wavecenter.api.sign.request;

import fm.lizhi.ocean.wavecenter.api.common.util.ApiAssert;
import fm.lizhi.ocean.wavecenter.api.sign.constant.ContractTypeEnum;
import fm.lizhi.ocean.wavecenter.api.sign.constant.SignRelationEnum;
import lombok.Builder;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/10/19 15:12
 */
@Getter
@Builder
public class RequestPlayerSignHistory {

    private Integer appId;

    private Long playerUserId;

    private ContractTypeEnum type;

    private SignRelationEnum status;

    @Builder.Default
    private Integer pageNo = 1;

    @Builder.Default
    private Integer pageSize = 1;

    public static class RequestPlayerSignHistoryBuilder {
        public RequestPlayerSignHistory build() {
            ApiAssert.notNull(appId, "appId is required");
            ApiAssert.notNull(playerUserId, "playerUserId is required");
            return new RequestPlayerSignHistory(appId, playerUserId, type, status, pageNo$value, pageSize$value);
        }
    }



}
