package fm.lizhi.ocean.wavecenter.api.award.family.bean;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2025/3/26 18:20
 */
@Data
@Accessors(chain = true)
public class FamilyAwardDeliverItemBean {

    private Long id;

    /**
     * 公会奖励发放记录id
     */
    private Long recordId;

    /**
     * 公会奖励发放执行id
     */
    private Long executionId;

    /**
     * 资源发放类型, 1-推荐卡, 2-座驾, 3-勋章, 4-短号, 5-新厅名额
     */
    private Integer resourceDeliverType;

    /**
     * 资源类型, 1-等级奖励推荐卡, 2-等级奖励座驾, 3-等级奖励勋章, 4-等级奖励短号, 101-PP等级推荐卡, 102-流水增长推荐卡, 103-新厅留存推荐卡, 104-0流失厅推荐卡, 105-特殊推荐卡, 501-等级初始新厅名额, 502-流水涨幅新厅名额, 503-流失厅数新厅名额, 504-新厅留存新厅名额
     */
    private Integer resourceType;

    /**
     * 资源数量, 冗余
     */
    private Integer resourceNumber;

    /**
     * 资源有效期, 冗余
     */
    private Integer resourceValidPeriod;

    /**
     * 资源id, 冗余
     */
    private Long resourceId;

    /**
     * 资源名称, 冗余
     */
    private String resourceName;

    /**
     * 资源图片路径, 冗余
     */
    private String resourceImage;

    /**
     * 状态, 冗余, 1-发放中, 2-发放成功, 3-发放失败, 4-已回收
     */
    private Integer status;

    /**
     * 错误码, 冗余, 用于记录失败信息, 成功时值为0
     */
    private Integer errorCode;

    /**
     * 错误文本, 冗余, 用于记录失败信息, 成功时值为空白
     */
    private String errorText;

}
