package fm.lizhi.ocean.wavecenter.api.live.service;

import java.util.List;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.live.request.RequestGetRecommendCardStatics;
import fm.lizhi.ocean.wavecenter.api.live.response.ResponseRecommendCardStaticsInfo;

import javax.validation.Valid;

public interface RecommendCardStaticsService {

    /**
     * 获取推荐卡曝光率
     * @param recommendCardRecordIds 推荐卡业务使用记录ID列表
     * @return 推荐卡曝光率列表
     */
    Result<List<ResponseRecommendCardStaticsInfo>> getRecommendCardStatics(@Valid RequestGetRecommendCardStatics recommendCardRecordIds);
}
