package fm.lizhi.ocean.wavecenter.api.user.service;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.user.bean.SaveUserRoleReqBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.*;

/**
 * <AUTHOR>
 * @date 2024/4/12 14:34
 */
public interface UserLoginService {

    /**
     * 手机号码登录
     * @param loginParam
     * @return
     */
    Result<UserTokenBean> phoneLogin(PhoneLoginParamBean loginParam);

    /**
     * 刷新token
     * @param refreshToken
     * @return
     */
    Result<UserTokenBean> refreshToken(int appId, String refreshToken);

    /**
     * 创建二维码
     * @param appId
     * @param oldQrCode
     * @return
     */
    Result<CreateQrCodeResBean> createQrCode(int appId, String oldQrCode, String deviceId);

    /**
     * 开始扫描二维码
     * @param appId
     * @param qrCodeKey
     * @return
     */
    Result<Void> beginScanQrCode(int appId, String qrCodeKey);

    /**
     * 二维码登录
     * @param qrCodeLoginParamBean
     * @return
     */
    Result<Void> qrCodeLogin(QrCodeLoginParamBean qrCodeLoginParamBean);

    /**
     * 查询二维码的token
     * @param appId
     * @param qrCodeKey
     * @return
     */
    Result<QrCodeResultBean> getQrCodeToken(int appId, String qrCodeKey);

    /**
     * 保存用户选择的角色
     * @param roleReqBean
     * @return
     */
    Result<Void> saveUserRole(SaveUserRoleReqBean roleReqBean);

    /**
     * 退出登录
     * @param appId
     * @param userId
     * @param deviceId
     * @return
     */
    Result<Void> logout(int appId, long userId, String deviceId);

    /**
     * 生成用户token, 仅提供给测试使用
     * @param appId
     * @param userId
     * @param deviceId
     * @return
     */
    Result<UserTokenBean> genUserToken(int appId, long userId, String deviceId);

    /**
     * 通过accessToken获取用户ID
     * @param accessToken
     * @return
     */
    Result<Long> getUserIdByAccessToken(String accessToken);

    /**
     * 获取用户登录的角色详细信息
     * @return
     */
    Result<LoginRoleInfoBean> getUserLoginRoleInfo(String accessToken);

    /**
     * 获取用户登录的角色信息
     * @return
     */
    Result<LoginRoleInfoBean> getUserLoginRoleSimpleInfo(String accessToken);

    /**
     * 通过业务token获取用户ID
     * @param appId
     * @param bizToken
     * @return
     */
    Result<Long> getUserIdByBizToken(int appId, String bizToken);

    /**
     * 登录未知错误
     */
    int LOGIN_ERROR = 1;

    /**
     * 鉴权失败
     */
    int AUTH_FAIL = 2;

    /**
     * 用户不存在
     */
    int USER_NOT_EXIST = 3;

    /**
     * 用户未认证
     */
    int USER_NO_VERIFY = 4;

    /**
     * 用户状态异常
     */
    int USER_INVALID = 5;

    /**
     * 用户被封禁
     */
    int USER_BAN = 6;

    /**
     * 风控不通过
     */
    int RISK_FAILED = 7;

    /**
     * refreshToken无效
     */
    int REFRESH_TOKEN_EXPIRED = 8;

    /**
     * refreshToken关联信息失效
     */
    int REFRESH_TOKEN_USER_NOT_FOUND = 9;

    /**
     * 二维码不存在
     */
    int QR_CODE_NOT_EXIST = 10;

    /**
     * 未登录
     */
    int NOT_LOGIN = 11;

    /**
     * 角色授权不存在
     */
    int ROLE_AUTH_NOT_EXIST = 12;

    /**
     * accessToken过期
     */
    int ACCESS_TOKEN_EXPIRED = 13;

    /**
     * 用户不在白名单中
     */
    int NOT_IN_WHITELIST = 14;

    /**
     * 业务token无效
     */
    int BIZ_TOKEN_USER_NOT_EXIST = 15;

}
