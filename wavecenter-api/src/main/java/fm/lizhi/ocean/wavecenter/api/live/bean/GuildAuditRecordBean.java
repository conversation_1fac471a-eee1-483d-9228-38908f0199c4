package fm.lizhi.ocean.wavecenter.api.live.bean;

import fm.lizhi.ocean.wavecenter.api.user.bean.RoomBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserBean;
import lombok.*;

import java.util.Date;

@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class GuildAuditRecordBean {

    /**
     * 签约厅的信息
     *
     */
    private RoomBean roomBean;

    /**
     * 陪玩
     */
    private UserBean player;

    /**
     * 操作类型
     */
    private Integer op;

    /**
     * 处罚时效时间
     */
    private String pushTime;

    /**
     * 操作理由
     */
    private String reason;

    /**
     * 处罚时间
     */
    private Date insertTime;

    /**
     * 违规场景
     */
    private String scene;

    /**
     * 公开录音文件 URL
     */
    private String publicContentUrl;
}
