package fm.lizhi.ocean.wavecenter.api.live.response;

import fm.lizhi.ocean.wavecenter.api.live.bean.WaveCheckInPlayerStatisticBean;
import fm.lizhi.ocean.wavecenter.api.live.bean.WaveCheckInUserBean;
import lombok.Data;

import java.util.List;

/**
 * 获取麦序福利主播明细统计的响应
 */
@Data
public class ResponseGetCheckInPlayerStatistic {

    /**
     * 麦序福利主播信息
     */
    private WaveCheckInUserBean player;

    /**
     * 麦序福利主播明细统计列表
     */
    private List<WaveCheckInPlayerStatisticBean> list;
}
