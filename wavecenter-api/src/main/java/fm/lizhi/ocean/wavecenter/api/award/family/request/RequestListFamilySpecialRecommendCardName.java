package fm.lizhi.ocean.wavecenter.api.award.family.request;

import fm.lizhi.ocean.wavecenter.api.common.annotation.AppEnumId;
import fm.lizhi.ocean.wavecenter.api.common.bean.IContextRequest;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * 列出公会特殊推荐卡名单请求
 */
@Data
public class RequestListFamilySpecialRecommendCardName implements IContextRequest {

    /**
     * 分页页码
     */
    @NotNull(message = "分页页码不能为空")
    @Min(value = 1, message = "分页页码不能小于1")
    private Integer pageNumber;

    /**
     * 分页大小
     */
    @NotNull(message = "分页大小不能为空")
    @Min(value = 1, message = "分页大小不能小于1")
    @Max(value = 1000, message = "分页大小不能大于1000")
    private Integer pageSize;

    /**
     * 应用id
     */
    @NotNull(message = "appId不能为空")
    @AppEnumId
    private Integer appId;

    @Override
    public Integer foundIdAppId() {
        return this.appId;
    }
}
