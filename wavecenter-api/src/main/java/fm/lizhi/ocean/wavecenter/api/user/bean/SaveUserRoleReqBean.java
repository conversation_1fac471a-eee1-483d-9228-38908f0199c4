package fm.lizhi.ocean.wavecenter.api.user.bean;

import fm.lizhi.ocean.wavecenter.api.common.util.ApiAssert;
import lombok.Builder;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/4/16 15:12
 */
@Getter
@Builder
public class SaveUserRoleReqBean {
    private Integer appId;
    private Long userId;
    private String deviceId;
    private Long roleConfigId;

    public static class SaveUserRoleReqBeanBuilder{
        public SaveUserRoleReqBean build(){
            ApiAssert.notNull(appId, "appId can not be null");
            ApiAssert.notNull(userId, "userId can not be null");
            ApiAssert.hasText(deviceId, "deviceId can not be null");
            return new SaveUserRoleReqBean(appId, userId, deviceId, roleConfigId);
        }
    }

}
