package fm.lizhi.ocean.wavecenter.api.background.activitycenter.bean;

import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityTemplateStatusEnum;
import lombok.Data;

/**
 * 活动模板分页bean, 用于运营后台活动模板列表展示
 */
@Data
public class ActivityTemplatePageBean {

    /**
     * 活动模板id
     */
    private Long id;

    /**
     * 应用id
     */
    private Integer appId;

    /**
     * 模板名称
     */
    private String name;

    /**
     * 大类id
     */
    private Long bigClassId;

    /**
     * 大类名称
     */
    private String bigClassName;

    /**
     * 分类id
     */
    private Long classId;

    /**
     * 分类名称
     */
    private String className;

    /**
     * 是否已删除
     */
    private Boolean deleted;

    /**
     * 封面地址
     */
    private String cover;

    /**
     * 上下架状态
     *
     * @see ActivityTemplateStatusEnum
     */
    private Integer status;

    /**
     * 权重
     */
    private Integer weight;

    /**
     * 是否热门推荐
     */
    private Boolean hotRec;

    /**
     * 热门推荐权重
     */
    private Integer hotWeight;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 修改时间
     */
    private Long modifyTime;

    /**
     * 操作人
     */
    private String operator;
}
