package fm.lizhi.ocean.wavecenter.api.live.bean;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class RoomDayStats implements IStatsEle{


    private String income;


    private Integer charm;

    /**
     * 麦序
     */
    private Integer seatOrder;

    /**
     * 主持档
     */
    private Integer hostCnt;


    @Override
    public BigDecimal foundIncomeNum() {
        if (income == null || income.isEmpty()) {
            return BigDecimal.ZERO;
        }
        return new BigDecimal(income);
    }

    @Override
    public String foundIncome() {
        return this.income;
    }

    @Override
    public Integer foundCharm() {
        return this.charm;
    }

    @Override
    public Integer foundSeatOrder() {
        return this.seatOrder;
    }

    @Override
    public Integer foundHostCnt() {
        return this.hostCnt;
    }

    @Override
    public Integer foundCheckPlayerNumber() {
        return null;
    }
}
