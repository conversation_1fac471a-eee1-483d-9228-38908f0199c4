package fm.lizhi.ocean.wavecenter.api.live.service;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.live.bean.*;

import java.util.List;

/**
 * 主播打卡统计
 *
 * @deprecated 已迁移至WaveCheckInDataService
 */
@Deprecated
public interface LivePlayerCheckInService {


    /**
     * 日统计-查询
     * @param req
     * @return
     */
    Result<List<PlayerCheckDayStatsBean>> dayStats(PlayerCheckDayStatsReq req);

    /**
     * 小时统计-查询
     * @param req
     * @return
     */
    Result<List<PlayerCheckHourStatsDayBean>> hourStats(PlayerCheckHourStatsReq req);

    /**
     * 汇总-日和小时通用
     * @param req
     * @return
     */
    Result<PlayerCheckStatsSumBean> sum(PlayerCheckHourStatsReq req);


}
