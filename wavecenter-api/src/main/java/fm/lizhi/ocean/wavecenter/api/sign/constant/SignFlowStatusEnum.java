package fm.lizhi.ocean.wavecenter.api.sign.constant;

/**
 * 签约流程状态
 * <AUTHOR>
 * @date 2024/10/9 14:49
 */
public enum SignFlowStatusEnum {

    /**
     * 进行中
     */
    RUNNING("RUNNING")

    /**
     * 已关闭
     */
    , CLOSED("CLOSED")
    ;

    private String code;

    SignFlowStatusEnum(String code) {
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public static SignFlowStatusEnum form(String code) {
        for (SignFlowStatusEnum item : SignFlowStatusEnum.values()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }
        return null;
    }
}
