package fm.lizhi.ocean.wavecenter.api.award.family.service;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.award.family.bean.ListFamilyLevelAwardRuleBean;
import fm.lizhi.ocean.wavecenter.api.award.family.request.*;
import fm.lizhi.ocean.wavecenter.api.award.family.response.ResponseGetFamilyLevelAwardRule;
import fm.lizhi.ocean.wavecenter.api.common.service.CommonService;

import javax.validation.Valid;
import java.util.List;

/**
 * 公会等级奖励规则服务, 前接口错误码以228开头.
 * <ul>
 *     <li>错误码规范参考<a href="https://lizhi2021.feishu.cn/wiki/Kulywk1KliQVGTktY56cny01nvg#JYrkdVrauo2bhbx0IeCcYSjsnMh">错误码规范</a></li>
 *     <li>状态码请维护在<a href="https://lizhi2021.feishu.cn/wiki/Ld0Rw5uvGiUimnkgez5cSVJ9n1c">创作者平台状态码维护文档</a></li>
 *     <li>通用错误码请见{@link CommonService}</li>
 * </ul>
 */
public interface FamilyLevelAwardRuleService {

    /**
     * 创建公会等级奖励规则
     *
     * @param request 请求参数
     * @return 创建结果
     */
    Result<Void> createFamilyLevelAwardRule(@Valid RequestCreateFamilyLevelAwardRule request);

    /**
     * 更新公会等级奖励规则
     *
     * @param request 请求参数
     * @return 更新结果
     */
    Result<Void> updateFamilyLevelAwardRule(@Valid RequestUpdateFamilyLevelAwardRule request);

    /**
     * 删除公会等级奖励规则
     *
     * @param request 请求参数
     * @return 删除结果
     */
    Result<Void> deleteFamilyLevelAwardRule(@Valid RequestDeleteFamilyLevelAwardRule request);

    /**
     * 列出公会等级奖励规则
     *
     * @param request 请求参数
     * @return 列表结果
     */
    Result<List<ListFamilyLevelAwardRuleBean>> listFamilyLevelAwardRule(@Valid RequestListFamilyLevelAwardRule request);

    /**
     * 获取公会等级奖励规则
     *
     * @param request 请求参数
     * @return 获取结果
     */
    Result<ResponseGetFamilyLevelAwardRule> getFamilyLevelAwardRule(@Valid RequestGetFamilyLevelAwardRule request);

    // ------------------ 方法00, createFamilyLevelAwardRule ------------------

    // ------------------ 方法01, updateFamilyLevelAwardRule ------------------

    // ------------------ 方法02, deleteFamilyLevelAwardRule ------------------

    // ------------------ 方法03, listFamilyLevelAwardRule ------------------

    // ------------------ 方法04, getFamilyLevelAwardRule ------------------
}
