package fm.lizhi.ocean.wavecenter.api.sign.bean;

import fm.lizhi.ocean.wavecenter.api.user.bean.UserBean;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/10/5 15:39
 */
@Data
public class MyRoomBean {

    /**
     * 签约记录ID
     */
    private Long contractId;

    /**
     * 简介
     */
    private String roomIntro;

    /**
     * 厅主
     */
    private UserBean roomUser;

    /**
     * 结算方式
     */
    private String settleType;

    /**
     * 分成比例
     */
    private Integer settlePercentage;

    /**
     * 家族类型
     */
    private String familyType;

    /**
     * 有效期
     */
    private Date signDealLineTime;

    /**
     * 签约时间
     */
    private Date signStartTime;

    /**
     * 发起时间
     */
    private Date createTime;


}
