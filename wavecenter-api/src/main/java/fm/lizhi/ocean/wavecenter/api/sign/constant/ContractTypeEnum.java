package fm.lizhi.ocean.wavecenter.api.sign.constant;

/**
 * <AUTHOR>
 * @date 2024/10/9 17:54
 */
public enum ContractTypeEnum {

    SIGN("SIGN")
    , CANCEL("CANCEL")
    , SUBJECT_CHANGE("SUBJECT_CHANGE")
    , REN<PERSON><PERSON>("RENEW")
    ;

    private String code;

    ContractTypeEnum(String code) {
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public static ContractTypeEnum from(String code) {
        for (ContractTypeEnum type : ContractTypeEnum.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
}
