package fm.lizhi.ocean.wavecenter.api.sign.request;

import fm.lizhi.ocean.wavecenter.api.common.util.ApiAssert;
import lombok.Builder;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/10/26 15:08
 */
@Getter
@Builder
public class RequestFamilyDoCancelAdmin {

    private Integer appId;

    private Long familyId;

    private Long contractId;

    private String signId;

    private Long currUserId;

    public static class RequestFamilyDoCancelAdminBuilder {
        public RequestFamilyDoCancelAdmin build() {
            ApiAssert.notNull(appId, "appId is null");
            ApiAssert.notNull(familyId, "familyId is null");
            ApiAssert.notNull(contractId, "contractId is null");
            ApiAssert.hasText(signId, "signId is null");
            ApiAssert.notNull(currUserId, "currUserId is null");
            return new RequestFamilyDoCancelAdmin(appId, familyId, contractId, signId, currUserId);
        }
    }

}
