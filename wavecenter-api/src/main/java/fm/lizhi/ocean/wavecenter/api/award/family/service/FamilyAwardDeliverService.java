package fm.lizhi.ocean.wavecenter.api.award.family.service;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.award.family.bean.AwardDeliverRecordV1Bean;
import fm.lizhi.ocean.wavecenter.api.award.family.bean.AwardDeliverRecordV2Bean;
import fm.lizhi.ocean.wavecenter.api.award.family.bean.FamilyAwardDeliverItemBean;
import fm.lizhi.ocean.wavecenter.api.award.family.request.RequestAwardDeliverListRecordV1;
import fm.lizhi.ocean.wavecenter.api.award.family.request.RequestAwardDeliverListRecordV2;
import fm.lizhi.ocean.wavecenter.api.award.family.request.RequestGetListDeliverItem;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;

import javax.validation.Valid;
import java.util.List;

/**
 * 公会奖励发放记录
 * 异常码234开头
 * <AUTHOR>
 * @date 2025/3/26 11:42
 */
public interface FamilyAwardDeliverService {

    /**
     * 奖励发放记录-适用PP
     * @param request
     * @return
     */
    Result<PageBean<AwardDeliverRecordV2Bean>> listRecordV2(@Valid RequestAwardDeliverListRecordV2 request);

    /**
     * 奖励发放记录-使用黑叶西米
     * @param request
     * @return
     */
    Result<PageBean<AwardDeliverRecordV1Bean>> listRecordV1(@Valid RequestAwardDeliverListRecordV1 request);

    /**
     * 查询发放记录奖励明细
     * @param request
     * @return
     */
    Result<List<FamilyAwardDeliverItemBean>> listDeliverItem(@Valid RequestGetListDeliverItem request);

}
