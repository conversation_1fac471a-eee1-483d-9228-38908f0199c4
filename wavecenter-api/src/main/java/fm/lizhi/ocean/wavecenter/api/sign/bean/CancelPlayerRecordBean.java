package fm.lizhi.ocean.wavecenter.api.sign.bean;

import fm.lizhi.ocean.wavecenter.api.user.bean.UserBean;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/10/8 14:04
 */
@Data
public class CancelPlayerRecordBean {

    private Long contractId;

    private UserBean roomInfo;

    private UserBean playerInfo;

    /**
     * 发起时间
     */
    private Date createTime;

    /**
     * 状态
     */
    private String status;

    private String type;

}
