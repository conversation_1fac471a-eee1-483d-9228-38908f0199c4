package fm.lizhi.ocean.wavecenter.api.live.bean;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class GuildRoomDayDetail implements IDetailEle{

    /**
     * 日的值 yy-mm-dd
     */
    private Date time;


    private String income;

    private Integer charm;

    /**
     * 麦序
     */
    private Integer seatOrder;

    /**
     * 打卡主播数
     */
    private Integer checkPlayerNumber;

    @Override
    public Date foundTime() {
        return this.time;
    }

    @Override
    public String foundIncome() {
        return this.income;
    }

    @Override
    public Integer foundCharm() {
        return this.charm;
    }

    @Override
    public Integer foundSeatOrder() {
        return this.seatOrder;
    }

    @Override
    public Integer foundHostCnt() {
        return null;
    }

    @Override
    public Integer foundCheckPlayerNumber() {
        return this.checkPlayerNumber;
    }

}
