package fm.lizhi.ocean.wavecenter.api.datacenter.service;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/22 20:31
 */
public interface RankDataService {

    /**
     * 厅主播 业绩龙虎榜
     * @param paramBean
     * @return
     */
    Result<RoomPlayerRankResBean> roomPlayer(RankGetRoomPlayerParamBean paramBean);

    /**
     * 公会主播 业绩龙虎榜
     * @param paramBean
     * @return
     */
    Result<GuildPlayerRankResBean> guildPlayer(RankGetGuildPlayerParamBean paramBean);

    /**
     * 签约主播排行榜
     * @param paramBean
     * @return
     */
    Result<PageBean<PlayerRankBean>> signPlayer(GetSignPlayerParamBean paramBean);

    /**
     * 厅的 业务龙虎榜
     * @param paramBean
     * @return
     */
    Result<List<RankRoomBean>> room(RankGetRoomParamBean paramBean);

    /**
     * 签约厅排行榜
     * @param paramBean
     * @return
     */
    Result<PageBean<RoomRankBean>> signRoom(GetSignRoomParamBean paramBean);

    int NOT_AUTH = 1;

    int AUTH_ERROR = 2;

}
