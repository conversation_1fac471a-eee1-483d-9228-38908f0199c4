package fm.lizhi.ocean.wavecenter.api.datacenter.bean;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/18 17:53
 */
@Data
@Accessors(chain = true)
public class RoomPlayerPerformanceResBean {

    private Integer total = 0;

    private List<PlayerPerformanceBean> players;

    /**
     * 考核周期开始时间
     */
    private Date startDate;

    /**
     * 考核周期结束时间
     */
    private Date endDate;

}
