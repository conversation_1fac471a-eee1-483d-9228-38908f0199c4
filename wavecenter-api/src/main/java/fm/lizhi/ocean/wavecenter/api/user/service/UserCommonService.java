package fm.lizhi.ocean.wavecenter.api.user.service;


import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.common.bean.ContextRequest;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.*;
import fm.lizhi.ocean.wavecenter.api.user.request.RequestGetAllGuildRoomsV2;
import fm.lizhi.ocean.wavecenter.api.user.request.RequestSearchUser;
import fm.lizhi.ocean.wavecenter.api.user.response.ResponseSearchUser;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/10 11:34
 */
public interface UserCommonService {


    /**
     * 用户角色信息
     * @param appId
     * @param userId
     * @return
     */
    Result<UserRoleInfoBean> getUserRoleInfo(int appId, long userId);


    /**
     * 查询公会下的厅列表
     * @param appId
     * @param familyId
     * @param pageNo
     * @param pageSize
     * @return
     */
    Result<PageBean<RoomSignBean>> getAllGuildRooms(int appId, long familyId, int pageNo, int pageSize);

    /**
     * 查询公会下的厅列表
     * 在getAllGuildRooms的基础上增加厅范围过滤
     * @return
     */
    Result<PageBean<RoomSignBean>> getAllGuildRoomsV2(@Valid RequestGetAllGuildRoomsV2 request);

    /**
     * 查询厅下所有主播，包括已解约的
     * @param appId
     * @param roomId
     * @param pageNo
     * @param pageSize
     * @return
     */
    Result<PageBean<PlayerSignBean>> getAllRoomPlayers(int appId, long roomId, int pageNo, int pageSize);


    /**
     * 查询工会下面的 主播信息
     *
     * @param pageNo
     * @param pageSize
     * @return
     */
    Result<PageBean<PlayerSignBean>> getAllGuildPlayer(QueryGuildPlayerBean req, int pageNo, int pageSize);

    /**
     * 获取用户信息
     * @param userId
     * @return
     */
    Result<UserBean> getUserById(int appId, long userId);

    /**
     * 批量获取用户信息
     * @param appId
     * @param userIds
     * @return
     */
    Result<List<UserBean>> getUserByIds(int appId, List<Long> userIds);

    /**
     * 获取用户信息
     * @param band
     * @return
     */
    Result<UserBean> getUserByBand(int appId, String band);

    /**
     * 获取主播签约状态
     * @param appId
     * @param userId
     * @return
     */
    Result<PlayerSignBean> getPlayerSignInfo(int appId, Long familyId, Long roomId, long userId);

    /**
     * 搜索用户
     * @param request
     * @return
     */
    Result<ResponseSearchUser> searchUser(RequestSearchUser request);

    /**
     * 根据关键词搜索用户
     * @param appId
     * @param keyword
     * @return
     */
    Result<UserBean> getUserByKeyWord(ContextRequest contextRequest, String keyword);

    /**
     * 用户未找到
     */
    int USER_NOT_FOUND = 1;

    /**
     * 用户是UGC
     */
    int USER_IS_UGC = 2;

}
