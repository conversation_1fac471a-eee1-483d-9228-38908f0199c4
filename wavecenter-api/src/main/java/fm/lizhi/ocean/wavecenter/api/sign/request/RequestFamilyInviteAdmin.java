package fm.lizhi.ocean.wavecenter.api.sign.request;

import fm.lizhi.ocean.wavecenter.api.common.util.ApiAssert;
import lombok.Builder;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/10/24 14:57
 */
@Getter
@Builder
public class RequestFamilyInviteAdmin {

    private Integer appId;

    private Long curUserId;

    private Long targetUserId;

    private Long familyId;

    private Integer duration;

    public static class RequestFamilyInviteAdminBuilder {
        public RequestFamilyInviteAdmin build() {
            ApiAssert.notNull(appId, "appId is null");
            ApiAssert.notNull(curUserId, "curUserId is null");
            ApiAssert.notNull(targetUserId, "targetUserId is null");
            ApiAssert.notNull(familyId, "familyId is null");
            if (duration == null) {
                //默认周期一年
                duration = 12;
            }
            return new RequestFamilyInviteAdmin(appId, curUserId, targetUserId, familyId, duration);
        }
    }




}
