package fm.lizhi.ocean.wavecenter.api.sign.bean;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 激活流程通用模型
 * <AUTHOR>
 * @date 2024/10/9 11:58
 */
@Data
@Accessors(chain = true)
public class ActFlowBean {

    /**
     * 流程信息
     */
    private FlowInfoBean flowInfo;

    /**
     * 进行中的流程的家族信息
     */
    private MyFamilyBean familyInfo;

    /**
     * 当前签约的家族信息
     */
    private MyFamilyBean signedFamilyInfo;

    /**
     * 进行中的流程的厅主信息
     */
    private MyRoomBean roomInfo;

    /**
     * 当前签约的厅主信息
     */
    private MyRoomBean signedRoomInfo;

}
