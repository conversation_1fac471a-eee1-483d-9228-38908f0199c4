package fm.lizhi.ocean.wavecenter.api.live.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 麦序福利任务计算规则枚举
 */
@AllArgsConstructor
@Getter
public enum CheckInTaskRuleTypeEnum {

    SCORE_BASED_TASKS(1, "收多少分，算多少任务"),

    INSUFFICIENT_SCORE_DEDUCTION(2, "分数不够过任务，要扣麦序"),

    FULL_TASK_COMPLETION(3, "任务整档过，补也整档补");

    private final Integer type;

    private final String desc;

    public static CheckInTaskRuleTypeEnum getEnumByType(Integer type) {
        if (null == type) {
            return null;
        }
        for (CheckInTaskRuleTypeEnum anEnum : CheckInTaskRuleTypeEnum.values()) {
            if (anEnum.type.equals(type)) {
                return anEnum;
            }
        }
        return null;
    }
}
