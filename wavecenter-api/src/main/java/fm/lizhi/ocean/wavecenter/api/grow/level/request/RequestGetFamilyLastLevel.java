package fm.lizhi.ocean.wavecenter.api.grow.level.request;

import fm.lizhi.ocean.wavecenter.api.common.annotation.AppEnumId;
import fm.lizhi.ocean.wavecenter.api.common.bean.IContextRequest;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2025/3/20 15:27
 */
@Data
@Accessors(chain = true)
public class RequestGetFamilyLastLevel implements IContextRequest {

    @NotNull
    @AppEnumId
    private Integer appId;

    @NotNull
    private Long familyId;

    @Override
    public Integer foundIdAppId() {
        return this.appId;
    }
}
