package fm.lizhi.ocean.wavecenter.api.sign.service;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.sign.bean.AdminSignPlayerRecordBean;
import fm.lizhi.ocean.wavecenter.api.sign.bean.TodoSignPlayerBean;
import fm.lizhi.ocean.wavecenter.api.sign.request.*;
import fm.lizhi.ocean.wavecenter.api.sign.response.*;

import java.util.List;

/**
 * 厅主签约
 * <AUTHOR>
 */
public interface SignAdminService {

    /**
     * 操作签约 用于和主播签约
     * @return
     */
    Result<ResponseAdminOperateSign> operateSign(RequestAdminOperateSign request);

    /**
     * 邀请用户签约
     * @param request
     * @return
     */
    Result<ResponseAdminInviteUser> inviteUser(RequestAdminInviteUser request);

    /**
     * 签署解约
     * @param request
     * @return
     */
    Result<ResponseAdminOperateCancel> operateCancel(RequestAdminOperateCancel request);

    /**
     * 取消解约申请
     * @param request
     * @return
     */
    Result<ResponseWithdrawCancel> withdrawCancel(RequestWithdrawCancel request);

    /**
     * 申请与主播解约
     * @param request
     * @return
     */
    Result<ResponseAdminApplyCancelPlayer> applyCancelPlayer(RequestAdminApplyCancelPlayer request);

    /**
     * 查询待办列表
     * @param request
     * @return
     */
    Result<List<TodoSignPlayerBean>> getTodoList(RequestAdminTodoList request);

    /**
     * 查询签约解约列表
     * @param request
     * @return
     */
    Result<PageBean<AdminSignPlayerRecordBean>> querySignPlayerList(RequestQuerySignPlayerList request);

    /**
     * 统计厅已签约主播数
     * @param appId
     * @param njId
     * @return
     */
    Result<Integer> countSignPlayerNum(int appId, long njId);

    /**
     * 签署与家族解约
     * @param request
     * @return
     */
    Result<ResponseAdminDoCancelFamily> doCancelFamily(RequestAdminDoCancelFamily request);

    /**
     * 申请与家族解约
     * @return
     */
    Result<ResponseAdminApplyCancelFamily> applyCancelFamily(RequestAdminApplyCancelFamily request);

    /**
     * 签约用户未完成实名认证
     */
    int OPERATE_SIGN_VERIFY_NOT_EXIST = SignErrorCode.REQ_USER_NO_PLATFORM_VERIFY;

    /**
     * 当前请求用户不是管理员
     */
    int INVITE_USER_REQ_NOT_ADMIN = 2030101;

    /**
     * 请求用户媒体信息不完整
     */
    int INVITE_USER_MEDIA_INFO_NOT_EXIST = SignErrorCode.REQ_USER_MEDIA_INFO_NO_EXIST;

    /**
     * 管理员未完成平台实名认证
     */
    int INVITE_USER_PLATFORM_VERIFY_NO_PASS = SignErrorCode.REQ_USER_NO_PLATFORM_VERIFY;

    /**
     * 陪玩已签约
     */
    int INVITE_USER_USER_SIGNED = SignErrorCode.PLAYER_SIGNED;

    /**
     * 申请解约失败
     */
    int APPLY_CANCEL_FAMILY_FAIL = 2030102;

}
