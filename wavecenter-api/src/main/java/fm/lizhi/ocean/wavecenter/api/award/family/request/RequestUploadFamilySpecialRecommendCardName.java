package fm.lizhi.ocean.wavecenter.api.award.family.request;

import fm.lizhi.ocean.wavecenter.api.award.family.bean.SaveFamilySpecialRecommendCardNameBean;
import fm.lizhi.ocean.wavecenter.api.common.annotation.AppEnumId;
import fm.lizhi.ocean.wavecenter.api.common.bean.IContextRequest;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 上传公会特殊推荐卡名单请求
 */
@Data
public class RequestUploadFamilySpecialRecommendCardName implements IContextRequest {

    /**
     * 应用id
     */
    @NotNull(message = "appId不能为空")
    @AppEnumId
    private Integer appId;

    /**
     * 名单列表
     */
    @NotEmpty(message = "名单列表不能为空")
    @Valid
    private List<SaveFamilySpecialRecommendCardNameBean> list;

    /**
     * 操作人
     */
    @NotNull(message = "操作人不能为空")
    private String operator;

    @Override
    public Integer foundIdAppId() {
        return this.appId;
    }
}
