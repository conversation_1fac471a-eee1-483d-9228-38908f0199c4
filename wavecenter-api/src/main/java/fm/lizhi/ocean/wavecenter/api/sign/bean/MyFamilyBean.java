package fm.lizhi.ocean.wavecenter.api.sign.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserBean;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/10/17 17:03
 */
@Data
public class MyFamilyBean {

    /**
     * 家族 ID
     */
    private Long familyId;

    /**
     * 合同ID
     */
    private Long contractId;

    /**
     * 签约ID
     */
    private String signId;

    /**
     * 家族长信息
     */
    private UserBean familyUser;

    /**
     * 家族名称
     */
    private String familyName;

    /**
     * 家族简介
     */
    private String familyIntro;

    /**
     * 家族头像
     */
    private String familyIconUrl;

    /**
     * 结算方式
     */
    private String settleType;

    /**
     * 分成比例
     */
    private Integer settlePercentage;

    /**
     * 有效期
     */
    private Date signDealLineTime;

    /**
     * 签约时间
     */
    private Date signStartTime;

    /**
     * 签约到期时间
     */
    private Date signExpireTime;

    /**
     * 发起时间
     */
    private Date createTime;

}
