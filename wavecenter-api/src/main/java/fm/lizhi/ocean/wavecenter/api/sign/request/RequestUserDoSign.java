package fm.lizhi.ocean.wavecenter.api.sign.request;

import fm.lizhi.ocean.wavecenter.api.common.util.ApiAssert;
import lombok.Builder;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/10/25 16:36
 */
@Getter
@Builder
public class RequestUserDoSign {

    private Integer appId;

    private Long curUserId;

    private Long contractId;

    public static class RequestUserDoSignBuilder {
        public RequestUserDoSign build() {
            ApiAssert.notNull(appId, "appId is null");
            ApiAssert.notNull(curUserId, "curUserId is null");
            ApiAssert.notNull(contractId, "contractId is null");
            return new RequestUserDoSign(appId, curUserId, contractId);
        }
    }

}
