package fm.lizhi.ocean.wavecenter.api.award.family.bean;

import lombok.Data;

/**
 * 列出公会等级奖励规则的结果bean
 */
@Data
public class ListFamilyLevelAwardRuleBean {

    /**
     * 规则id
     */
    private Long id;

    /**
     * 公会等级id
     */
    private Long levelId;

    /**
     * 公会等级名称
     */
    private String levelName;

    /**
     * 公会等级是否已删除
     */
    private Boolean levelDeleted;

    /**
     * 推荐卡数量
     */
    private Integer recommendCardNumber;

    /**
     * 推荐卡有效期, 默认单位为天
     */
    private Integer recommendCardValidPeriod;

    /**
     * 座驾id
     */
    private Long vehicleId;

    /**
     * 座驾名称
     */
    private String vehicleName;

    /**
     * 座驾图片
     */
    private String vehicleImage;

    /**
     * 座驾有效期, 默认单位为天
     */
    private Integer vehicleValidPeriod;

    /**
     * 勋章id
     */
    private Long medalId;

    /**
     * 勋章名称
     */
    private String medalName;

    /**
     * 勋章图片
     */
    private String medalImage;

    /**
     * 勋章有效期, 默认单位为天
     */
    private Integer medalValidPeriod;

    /**
     * 短号id
     */
    private Long shortNumberId;

    /**
     * 短号名称
     */
    private String shortNumberName;

    /**
     * 创建时间, 毫秒时间戳
     */
    private Long createTime;

    /**
     * 创建者
     */
    private String creator;

    /**
     * 修改时间, 毫秒时间戳
     */
    private Long modifyTime;

    /**
     * 修改者
     */
    private String modifier;
}
