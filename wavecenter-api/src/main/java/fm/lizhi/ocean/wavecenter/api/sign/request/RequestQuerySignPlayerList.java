package fm.lizhi.ocean.wavecenter.api.sign.request;

import fm.lizhi.ocean.wavecenter.api.common.util.ApiAssert;
import fm.lizhi.ocean.wavecenter.api.sign.constant.ContractTypeEnum;
import fm.lizhi.ocean.wavecenter.api.sign.constant.SignRelationEnum;
import lombok.Builder;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/10/17 15:46
 */
@Getter
@Builder
public class RequestQuerySignPlayerList {

    private Integer appId;

    private Long njId;

    private ContractTypeEnum type;

    private SignRelationEnum status;

    private String userBand;

    @Builder.Default
    private Integer pageNo = 1;

    @Builder.Default
    private Integer pageSize = 20;

    public static class RequestQuerySignPlayerListBuilder {
        public RequestQuerySignPlayerList build() {
            ApiAssert.notNull(appId, "appId is null");
            ApiAssert.notNull(njId, "njId is null");
            ApiAssert.notNull(type, "type is null");
            ApiAssert.notNull(status, "status is null");
            return new RequestQuerySignPlayerList(appId, njId, type, status, userBand, pageNo$value, pageSize$value);
        }
    }

}
