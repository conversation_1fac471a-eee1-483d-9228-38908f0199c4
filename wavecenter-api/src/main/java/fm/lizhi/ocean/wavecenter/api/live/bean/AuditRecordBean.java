package fm.lizhi.ocean.wavecenter.api.live.bean;

import lombok.*;

import java.util.Date;

@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AuditRecordBean {



    private Long id;

    /**
     * 应用ID
     */
    private Long appId;

    /**
     * 厅ID
     */
    private Long roomId;

    /**
     * 工会ID
     */
    private Long familyId;

    /**
     * 主播ID
     */
    private Long userId;

    /**
     * 操作类型
     */
    private Integer op;

    /**
     * 操作理由
     */
    private String reason;

    /**
     * 处罚时间
     */
    private Date insertTime;

    /**
     * 送审id
     */
    private String auditId;

    /**
     * 处罚时效时间
     */
    private String pushTime;

    /**
     * 违规场景
     */
    private String scene;

    private Integer sceneType;

    /**
     * 公开录音文件 URL
     */
    private String publicContentUrl;

}
