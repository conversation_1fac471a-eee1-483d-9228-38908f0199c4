package fm.lizhi.ocean.wavecenter.api.sign.request;

import fm.lizhi.ocean.wavecenter.api.common.util.ApiAssert;
import lombok.Builder;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/10/26 14:12
 */
@Getter
@Builder(toBuilder = true)
public class RequestAdminApplyCancelFamily {

    private Integer appId;

    private Long curUserId;

    private Long contractId;

    private Long familyId;

    public static class RequestAdminApplyCancelFamilyBuilder {
        public RequestAdminApplyCancelFamily build() {
            ApiAssert.notNull(appId, "appId is null");
            ApiAssert.notNull(curUserId, "curUserId is null");
            ApiAssert.notNull(contractId, "contractId is null");
            return new RequestAdminApplyCancelFamily(appId, curUserId, contractId, familyId);
        }
    }

}
