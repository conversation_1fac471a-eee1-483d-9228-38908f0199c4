package fm.lizhi.ocean.wavecenter.api.sign.response;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2024/10/14 14:10
 */
@Data
@Accessors(chain = true)
public class ResponsePlayerApplyCancel implements ResponseSignResult{

    private Integer code;

    private String msg;

    private Long contractId;

    @Override
    public int getResultCode() {
        return this.code;
    }

    @Override
    public String getResultMsg() {
        return this.msg;
    }

}
