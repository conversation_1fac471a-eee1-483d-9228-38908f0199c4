package fm.lizhi.ocean.wavecenter.api.award.family.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 公会奖励资源发放类型枚举. 与执行发放的接口相关
 */
@AllArgsConstructor
@Getter
public enum FamilyAwardResourceDeliverTypeEnum {

    /**
     * 推荐卡
     */
    RECOMMEND_CARD(1),
    /**
     * 座驾
     */
    VEHICLE(2),
    /**
     * 勋章
     */
    MEDAL(3),
    /**
     * 短号
     */
    SHORT_NUMBER(4),
    /**
     * 新厅名额
     */
    NEW_ROOM(5),
    ;

    private final Integer value;

    public static FamilyAwardResourceDeliverTypeEnum fromValue(Integer value) {
        for (FamilyAwardResourceDeliverTypeEnum type : FamilyAwardResourceDeliverTypeEnum.values()) {
            if (Objects.equals(type.getValue(), value)) {
                return type;
            }
        }
        return null;
    }
}
