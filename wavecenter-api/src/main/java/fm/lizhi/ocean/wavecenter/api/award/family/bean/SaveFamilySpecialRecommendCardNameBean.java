package fm.lizhi.ocean.wavecenter.api.award.family.bean;

import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * 保存公会特殊推荐卡名单的bean
 */
@Data
public class SaveFamilySpecialRecommendCardNameBean {

    /**
     * 用户id
     */
    @NotNull(message = "用户id不能为空")
    @Min(value = 1, message = "用户id必须大于0")
    private Long userId;

    /**
     * 推荐卡发放数量
     */
    @NotNull(message = "推荐卡数量不能为空")
    @Min(value = 1, message = "推荐卡数量必须大于0")
    private Integer number;
}
