package fm.lizhi.ocean.wavecenter.api.sign.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserInfoBean;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/6/13 15:04
 */
@Data
@Accessors(chain = true)
public class RoomSignInfoBean {

    private UserInfoBean roomInfo;

    private Integer signStatus;

    private Date signDate;

    private Date expireDate;

    private Date stopDate;

    private Integer signPlayerCnt;

    /**
     * 合同ID
     */
    private Long contractId;

}
