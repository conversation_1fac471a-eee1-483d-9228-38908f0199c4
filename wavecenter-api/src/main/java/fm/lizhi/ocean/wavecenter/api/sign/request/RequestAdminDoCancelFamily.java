package fm.lizhi.ocean.wavecenter.api.sign.request;

import fm.lizhi.ocean.wavecenter.api.common.util.ApiAssert;
import lombok.Builder;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/10/26 11:58
 */
@Getter
@Builder
public class RequestAdminDoCancelFamily {

    private Integer appId;

    private Long curUserId;

    private Long contractId;

    private String signId;

    public static class RequestAdminDoCancelFamilyBuilder {
        public RequestAdminDoCancelFamily build() {
            ApiAssert.notNull(appId, "appId is null");
            ApiAssert.notNull(curUserId, "curUserId is null");
            ApiAssert.notNull(contractId, "contractId is null");
            ApiAssert.hasText(signId, "signId is null");
            return new RequestAdminDoCancelFamily(appId, curUserId, contractId, signId);
        }
    }

}
