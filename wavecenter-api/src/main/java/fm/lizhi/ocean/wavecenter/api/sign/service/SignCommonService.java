package fm.lizhi.ocean.wavecenter.api.sign.service;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.sign.request.RequestGetContractViewUrl;
import fm.lizhi.ocean.wavecenter.api.sign.response.ResponseSignContractUrl;

/**
 * <AUTHOR>
 * @date 2024/10/22 20:08
 */
public interface SignCommonService {

    /**
     * 获取签约系统token
     * @param appId
     * @param userId
     * @return
     */
    Result<String> getSignToken(int appId, long userId);

    /**
     * 获取合同查看链接
     * @return
     */
    Result<String> getContractViewUrl(RequestGetContractViewUrl request);


    /**
     * 获取上上签合同链接
     */
    Result<ResponseSignContractUrl> getSignContractUrl(int appId, Long userId, String signId);

    /**
     * token不存在
     */
    int GET_SIGN_TOKEN_NOT_EXIST = 2170001;

    /**
     * 获取合同链接失败
     */
    int GET_CONTRACT_VIEW_URL_FAIL = 2170002;

    /**
     * 无权查看合同
     */
    int GET_CONTRACT_VIEW_URL_AUTH_FAIL = 2170003;

}
