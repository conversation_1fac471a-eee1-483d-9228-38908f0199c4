package fm.lizhi.ocean.wavecenter.api.datacenter.bean;

import fm.lizhi.ocean.wavecenter.api.user.bean.UserBean;
import lombok.Data;

/**
 * 签约厅明细dto
 * <AUTHOR>
 */
@Data
public class RoomIncomeDetailBean {
    /**
     * 厅主信息
     */
    private UserBean roomInfo;
    /**
     * 厅品类
     */
    private String cateName;
    /**
     * 收入
     */
    private Long income;
    /**
     * 魅力值
     */
    private Long charm;
    /**
     * 签约厅收礼（收入钻）
     */
    private Long signRoomIncome;
    /**
     * 个播收入（钻）
     */
    private Long  personalRoomIncome;
    /**
     * 厅贵族提成
     */
    private Long signRoomVipIncome;
    /**
     * 个播贵族收入
     */
    private Long personalRoomVipIncome;
    /**
     * 有收入主播数
     */
    private Integer playerPayCount;
    /**
     * 厅送礼总收入
     */
    private Long giftIncome;
    /**
     * 官方厅收礼收入
     */
    private Long officialIncome;
}
