package fm.lizhi.ocean.wavecenter.api.background.activitycenter.bean;

import lombok.Builder;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@Builder
public class ActivityLevelConfigBean {

    private Long id;

    /**
     * 应用ID
     */
    private Integer appId;

    /**
     * 等级名称
     */
    private String level;

    /**
     * 操作者
     */
    private String operator;

    /**
     * 修改时间
     */
    private Long modifyTime;


}