package fm.lizhi.ocean.wavecenter.api.live.request;

import lombok.Data;

import javax.validation.constraints.AssertTrue;
import javax.validation.constraints.NotNull;
import java.beans.Transient;

/**
 * 获取麦序福利厅汇总的请求
 */
@Data
public class RequestGetCheckInRoomSum {

    /**
     * 应用ID
     */
    @NotNull(message = "应用ID不能为空")
    private Integer appId;

    /**
     * 房间ID.
     */
    @NotNull(message = "房间ID不能为空")
    private Long roomId;

    /**
     * 开始时间毫秒时间戳, 包含
     */
    @NotNull(message = "开始时间不能为空")
    private Long startDate;

    /**
     * 结束时间毫秒时间戳, 包含
     */
    @NotNull(message = "结束时间不能为空")
    private Long endDate;

    /**
     * 家族ID. 如果是厅主视角则不传, 如果是家族长视角或高级管理视角则需要传家族ID(避免家族长看到别的家族数据).
     */
    private Long familyId;

    @AssertTrue(message = "时间范围不合法")
    @Transient
    private boolean isDateRangeValid() {
        if (startDate == null || endDate == null) {
            return true;
        }
        if (endDate < startDate) {
            return false;
        }
        // 暂不考虑闰秒
        return endDate - startDate < 7 * 24 * 60 * 60 * 1000L;
    }
}
