package fm.lizhi.ocean.wavecenter.api.live.bean;

import lombok.Data;

/**
 * 麦序福利用户打卡汇总数据, 可能来自多种统计时间范围
 */
@Data
public class WaveCheckInUserSumBean {

    /**
     * 排档数
     */
    private Integer scheduledCnt = 0;

    /**
     * 有效麦序
     */
    private Integer seatCnt = 0;

    /**
     * 主持档数
     */
    private Integer hostCnt = 0;

    /**
     * 主持档魅力值合计
     */
    private Long hostCharmSum = 0L;

    /**
     * 未完成任务分
     */
    private Long notDoneScore = 0L;

    /**
     * 有效麦序魅力值
     */
    private Long seatCharm = 0L;

    /**
     * 合计魅力值
     */
    private Long sumCharm = 0L;

    /**
     * 收光记录, 格式为"1280*1,2270*2"
     */
    private String lightGift = "";

    /**
     * 收光奖励
     */
    private Long lightGiftAmount = 0L;

    /**
     * 合计钻石值
     */
    private Long sumIncome = 0L;

    /**
     * 全麦记录 格式：1680*2,2270*2 （阶梯魅力值 * 全麦记录数）
     */
    private String allMicGift = "";

    /**
     * 全麦奖励
     */
    private Long allMicGiftAmount = 0L;

    /**
     * 日麦序奖励
     */
    private Long dayMicAmount = 0L;
}
