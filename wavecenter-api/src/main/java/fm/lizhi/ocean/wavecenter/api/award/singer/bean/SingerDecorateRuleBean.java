package fm.lizhi.ocean.wavecenter.api.award.singer.bean;

import fm.lizhi.ocean.wavecenter.api.resource.constants.PlatformDecorateTypeEnum;
import lombok.*;
import lombok.experimental.Accessors;

/**
 *
 * 歌手装扮规则配置
 *
 * <AUTHOR>
 * @date 2025-03-27 03:27:23
 */
@Data
@Accessors(chain = true)
public class SingerDecorateRuleBean {
    /**
     * ID
     */
    private Long id;

    /**
     * 应用ID
     */
    private Integer appId;

    /**
     * 歌手认证等级
     */
    private Integer singerType;

    /**
     * 曲风
     */
    private String songStyle;

    /**
     * 装扮类型
     * @see PlatformDecorateTypeEnum
     */
    private Integer decorateType;

    /**
     * 装扮ID
     */
    private Long decorateId;


    /**
     * 是否启用 0:停用，1: 启用
     */
    private Boolean enabled;

    /**
     * 是否删除 0未删除 1已删除
     */
    private Boolean deleted;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 更新时间
     */
    private Long modifyTime;

    /**
     * 操作人
     */
    private String operator;

}