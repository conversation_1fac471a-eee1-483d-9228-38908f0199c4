package fm.lizhi.ocean.wavecenter.api.sign.service;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.sign.bean.PlayerSignHistoryRecordBean;
import fm.lizhi.ocean.wavecenter.api.sign.request.*;
import fm.lizhi.ocean.wavecenter.api.sign.response.ResponsePlayerApplyCancel;
import fm.lizhi.ocean.wavecenter.api.sign.response.ResponsePlayerOperateCancel;
import fm.lizhi.ocean.wavecenter.api.sign.response.ResponseWithdrawCancel;

/**
 * 主播签约
 * <AUTHOR>
 */
public interface SignPlayerService {

    /**
     * 陪玩申请解约
     * @param request
     * @return
     */
    Result<ResponsePlayerApplyCancel> applyCancel(RequestPlayerApplyCancel request);

    /**
     * 取消解约申请
     * @param request
     * @return
     */
    Result<ResponseWithdrawCancel> withdrawCancel(RequestWithdrawCancel request);

    /**
     * 签署管理员的解约申请
     * @param request
     * @return
     */
    Result<ResponsePlayerOperateCancel> operateAdminCancel(RequestPlayerOperateCancel request);

    /**
     * 查询陪玩签约历史记录
     * @param request
     * @return
     */
    Result<PageBean<PlayerSignHistoryRecordBean>> querySignHistory(RequestPlayerSignHistory request);

    /**
     * 查询主播和厅是否存在过签约关系，包括历史的
     * @return
     */
    Result<Boolean> hasSignRecordWithRooms(RequestHasSignRecordWithRooms request);

}
