package fm.lizhi.ocean.wavecenter.api.activitycenter.request;

import fm.lizhi.ocean.wavecenter.api.common.annotation.AppEnumId;
import fm.lizhi.ocean.wavecenter.api.common.bean.IContextRequest;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 分页查询热门活动模板请求
 */
@Data
public class RequestPageHotActivityTemplate implements IContextRequest {

    /**
     * 用户 ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    /**
     * 品类
     */
    private List<Integer> categoryValue;

    /**
     * 分页页码
     */
    @NotNull(message = "分页页码不能为空")
    private Integer pageNo;

    /**
     * 分页大小
     */
    @NotNull(message = "分页大小不能为空")
    @Max(value = 1000, message = "分页大小不能超过1000")
    private Integer pageSize;

    /**
     * 应用id
     */
    @NotNull(message = "应用id不能为空")
    @AppEnumId
    private Integer appId;

    @Override
    public Integer foundIdAppId() {
        return this.appId;
    }
}
