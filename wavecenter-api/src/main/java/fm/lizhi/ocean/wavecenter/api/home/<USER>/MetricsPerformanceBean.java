package fm.lizhi.ocean.wavecenter.api.home.bean;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 指标表现
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class MetricsPerformanceBean {

    /**
     * 当前值
     */
    private Double current;

    /**
     * 上一期的值
     */
    private Double pre;

    /**
     * 环比
     */
    private String ratio;

    /**
     * 同行表现
     */
    private String performance;

    /**
     * 指标比率
     */
    private String metricsRate;



}
