package fm.lizhi.ocean.wavecenter.api.sign.request;

import fm.lizhi.ocean.wavecenter.api.sign.constant.OperateTypeEnum;
import lombok.Builder;
import lombok.Getter;
import lombok.NonNull;

/**
 * <AUTHOR>
 * @date 2024/10/14 15:24
 */
@Getter
@Builder
public class RequestFamilyReviewCancel {

    @NonNull
    private Integer appId;

    @NonNull
    private Long curUserId;

    @NonNull
    private Long playerSignId;

    @NonNull
    private OperateTypeEnum operateType;

}
