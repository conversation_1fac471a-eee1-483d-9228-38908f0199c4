package fm.lizhi.ocean.wavecenter.api.award.family.service;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.award.family.bean.ListFamilySpecialRecommendCardNameBean;
import fm.lizhi.ocean.wavecenter.api.award.family.request.RequestClearFamilySpecialRecommendCardName;
import fm.lizhi.ocean.wavecenter.api.award.family.request.RequestListFamilySpecialRecommendCardName;
import fm.lizhi.ocean.wavecenter.api.award.family.request.RequestUploadFamilySpecialRecommendCardName;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.common.service.CommonService;

import javax.validation.Valid;

/**
 * 公会等级奖励规则服务, 前接口错误码以229开头.
 * <ul>
 *     <li>错误码规范参考<a href="https://lizhi2021.feishu.cn/wiki/Kulywk1KliQVGTktY56cny01nvg#JYrkdVrauo2bhbx0IeCcYSjsnMh">错误码规范</a></li>
 *     <li>状态码请维护在<a href="https://lizhi2021.feishu.cn/wiki/Ld0Rw5uvGiUimnkgez5cSVJ9n1c">创作者平台状态码维护文档</a></li>
 *     <li>通用错误码请见{@link CommonService}</li>
 * </ul>
 */
public interface FamilyOtherAwardRuleService {

    /**
     * 上传公会特殊推荐卡名单
     *
     * @param request 请求参数
     * @return 上传结果
     */
    Result<Void> uploadFamilySpecialRecommendCardName(@Valid RequestUploadFamilySpecialRecommendCardName request);

    /**
     * 清空公会特殊推荐卡名单
     *
     * @param request 请求参数
     * @return 清空结果
     */
    Result<Void> clearFamilySpecialRecommendCardName(@Valid RequestClearFamilySpecialRecommendCardName request);

    /**
     * 列出公会特殊推荐卡名单
     *
     * @param request 请求参数
     * @return 列表结果
     */
    Result<PageBean<ListFamilySpecialRecommendCardNameBean>> listFamilySpecialRecommendCardName(@Valid RequestListFamilySpecialRecommendCardName request);

    // ------------------ 方法00, uploadFamilySpecialRecommendCardName ------------------

    // ------------------ 方法01, clearFamilySpecialRecommendCardName ------------------

    // ------------------ 方法02, listFamilySpecialRecommendCardName ------------------
}
