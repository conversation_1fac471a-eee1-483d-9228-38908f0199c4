package fm.lizhi.ocean.wavecenter.api.permissions.service;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageParamBean;
import fm.lizhi.ocean.wavecenter.api.permissions.bean.*;
import fm.lizhi.ocean.wavecenter.api.permissions.request.RequestGetRoleRoomDataScope;
import fm.lizhi.ocean.wavecenter.api.user.bean.RoomBean;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/11 11:05
 */
public interface RoleService {

    /**
     * 获取系统所有角色
     * @return
     */
    Result<List<RoleBean>> getAllRoles();

    /**
     * 获取可被授权的角色
     * @return
     */
    Result<List<RoleBean>> getAuthRoles();

    /**
     * 查询所有授权配置
     * @param appId
     * @param pageParamBean
     * @return
     */
    Result<PageBean<RoleAuthRefBean>> getAllAuthConfig(int appId, long createUserId, Long userId, String roleCode, PageParamBean pageParamBean);

    /**
     * 获取授权配置
     * @param roleConfigId
     * @return
     */
    Result<RoleAuthRefBean> getAuthConfig(long roleConfigId);

    /**
     * 新增角色授权配置
     * @param addRoleAuthRefBean
     * @return
     */
    Result<Void> addAuthConfig(int appId, AddRoleAuthRefBean addRoleAuthRefBean);

    /**
     * 修改角色授权配置状态
     * @param configId
     * @param status
     * @return
     */
    Result<List<ModifyAuthConfigUserVo>> modifyAuthConfigStatus(long configId, int status);

    /**
     * 获取用户的授权角色
     * @param appId
     * @param userId
     * @return
     */
    Result<List<RoleInfoAuthRefBean>> getUserAuthRoles(int appId, long userId);

    /**
     * 查询用户角色的厅数据范围
     * @param request
     * @return
     */
    Result<List<RoomBean>> getRoleRoomDataScope(RequestGetRoleRoomDataScope request);

    /**
     * 新增角色授权配置-授权账号角色不符合
     */
    int ADD_AUTH_CONFIG_ROLE_ERROR = 1;

    /**
     * 新增角色授权配置-无法确认授权账号
     */
    int ADD_AUTH_CONFIG_SUBJECT_ERROR = 2;

    /**
     * 角色授权配置已存在
     */
    int ADD_AUTH_CONFIG_EXIST = 3;

    int AUTH_CONFIG_NOT_EXIST = 4;

}
