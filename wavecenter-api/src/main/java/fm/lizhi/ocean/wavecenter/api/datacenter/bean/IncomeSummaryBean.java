package fm.lizhi.ocean.wavecenter.api.datacenter.bean;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 收入汇总
 */
@Data
@Accessors(chain = true)
public class IncomeSummaryBean {

    /**
     * 收入
     */
    private Long sum;

    /**
     * 签约厅收礼
     */
    private Long roomGift;

    /**
     * 个播收礼
     */
    private Long player;

    /**
     * 签约厅贵族提成
     */
    private Long roomVip;


    /**
     * 个播贵族提成
     */
    private Long playerVip;

    /**
     * 官方厅收入
     */
    private Long officialRoom;


    public static IncomeSummaryBean of(Long roomGift, Long player, Long roomVip, Long playerVip) {
        return new IncomeSummaryBean()
                .setSum(roomGift + player + roomVip + playerVip)
                .setRoomGift(roomGift)
                .setPlayer(player)
                .setRoomVip(roomVip)
                .setPlayerVip(playerVip);
}

    public static IncomeSummaryBean buildRoomAndPayerAndOfficial(Long roomGift, Long player, Long officialRoom) {
        return new IncomeSummaryBean()
                .setSum(roomGift + player + officialRoom)
                .setRoomGift(roomGift)
                .setPlayer(player)
                .setOfficialRoom(officialRoom);
    }

    public static IncomeSummaryBean buildRoomAndOfficial(Long roomGift, Long officialRoom) {
        return new IncomeSummaryBean()
                .setSum(roomGift + officialRoom)
                .setRoomGift(roomGift)
                .setOfficialRoom(officialRoom);
    }

}
