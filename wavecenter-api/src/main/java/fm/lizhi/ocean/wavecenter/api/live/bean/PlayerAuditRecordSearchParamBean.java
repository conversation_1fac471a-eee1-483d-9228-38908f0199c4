package fm.lizhi.ocean.wavecenter.api.live.bean;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class PlayerAuditRecordSearchParamBean {

    /**
     * 应用ID
     */
    private int appId;

    /**
     * 主播ID
     */
    private Long userId;

    /**
     * 操作类型
     */
    private Integer op;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;
}
