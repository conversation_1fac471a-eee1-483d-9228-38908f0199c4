package fm.lizhi.ocean.wavecenter.api.activitycenter.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.Objects;

@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum ActivityApplyTypeEnum {
    /**
     * 用户提报
     */
    NJ_APPLY(1),

    /**
     * 官方提报
     */
    OFFICIAL_APPLY(2),

    ;

    private Integer applyType;

    public static ActivityApplyTypeEnum getApplyTypeEnum(Integer applyType) {
        for (ActivityApplyTypeEnum applyTypeEnum : values()) {
            if (Objects.equals(applyTypeEnum.applyType, applyType)) {
                return applyTypeEnum;
            }
        }
        return null;
    }
}
