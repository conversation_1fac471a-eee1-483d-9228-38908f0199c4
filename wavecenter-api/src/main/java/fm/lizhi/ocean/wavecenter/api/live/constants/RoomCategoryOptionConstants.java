package fm.lizhi.ocean.wavecenter.api.live.constants;

import java.util.HashMap;
import java.util.Map;

/**
 * 平台厅品类选项值
 * <AUTHOR>
 * @date 2025/5/9 15:00
 */
public class RoomCategoryOptionConstants {

    public static final Integer UNLIMITED_VALUE = 0;

    /**
     * key = 选项值
     * value = 显示名称
     */
    public static final Map<Integer, String> valueMap = new HashMap<>();

    static {
        valueMap.put(UNLIMITED_VALUE, "不限");
        for (RoomCategoryEnum value : RoomCategoryEnum.values()) {
            valueMap.put(value.getValue(), value.getDisplayName());
        }
    }
}
