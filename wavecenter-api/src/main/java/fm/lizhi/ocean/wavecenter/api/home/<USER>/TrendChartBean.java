package fm.lizhi.ocean.wavecenter.api.home.bean;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 趋势图
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class TrendChartBean {

    /**
     * 当前值
     */
    private Double current;

    /**
     * 上期值
     */
    private Double pre;

    /**
     * 环比
     */
    private String ratio;

    /**
     * 是否达到告警阈值
     */
    private Boolean warn;


}
