package fm.lizhi.ocean.wavecenter.api.background.activitycenter.response;

import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.UserActivitySimpleInfoBean;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * @description: 查询用户的提报活动信息列表
 * @author: guoyibin
 * @create: 2024/10/23 14:49
 */
@Data
@Accessors(chain = true)
public class ResponseQueryUserActivitiesBean {

    /**
     * 总数量
     */
    private Integer total;

    /**
     * 活动信息列表
     */
    private List<UserActivitySimpleInfoBean> list;
}
