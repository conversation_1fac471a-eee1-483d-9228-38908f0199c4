package fm.lizhi.ocean.wavecenter.api.award.family.service;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.award.family.request.RequestGetFamilyWeekAwardV1;
import fm.lizhi.ocean.wavecenter.api.award.family.request.RequestGetFamilyWeekAwardV2;
import fm.lizhi.ocean.wavecenter.api.award.family.response.ResponseGetFamilyWeekAwardV1;
import fm.lizhi.ocean.wavecenter.api.award.family.response.ResponseGetFamilyWeekAwardV2;
import fm.lizhi.ocean.wavecenter.api.common.service.CommonService;

import javax.validation.Valid;

/**
 * 公会奖励服务, 提供主播视角的查询接口, 接口错误码以237开头.
 * <ul>
 *     <li>错误码规范参考<a href="https://lizhi2021.feishu.cn/wiki/Kulywk1KliQVGTktY56cny01nvg#JYrkdVrauo2bhbx0IeCcYSjsnMh">错误码规范</a></li>
 *     <li>状态码请维护在<a href="https://lizhi2021.feishu.cn/wiki/Ld0Rw5uvGiUimnkgez5cSVJ9n1c">创作者平台状态码维护文档</a></li>
 *     <li>通用错误码请见{@link CommonService}</li>
 * </ul>
 */
public interface FamilyAwardService {

    /**
     * 获取公会周奖励V1(陪伴/西米), 注意如果没有对应周期的奖励, ResponseGetFamilyWeekAwardV1为null
     *
     * @param request 请求
     * @return 响应
     */
    Result<ResponseGetFamilyWeekAwardV1> getFamilyWeekAwardV1(@Valid RequestGetFamilyWeekAwardV1 request);

    /**
     * 获取公会周奖励V2(PP), 注意如果没有对应周期的奖励, ResponseGetFamilyWeekAwardV2为null
     *
     * @param request 请求
     * @return 响应
     */
    Result<ResponseGetFamilyWeekAwardV2> getFamilyWeekAwardV2(@Valid RequestGetFamilyWeekAwardV2 request);

    // ------------------ 方法00, getFamilyWeekAwardV1 ------------------

    // ------------------ 方法01, getFamilyWeekAwardV2 ------------------
}
