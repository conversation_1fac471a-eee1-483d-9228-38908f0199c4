package fm.lizhi.ocean.wavecenter.api.sign.request;

import fm.lizhi.ocean.wavecenter.api.sign.constant.OperateTypeEnum;
import lombok.Builder;
import lombok.Getter;
import lombok.NonNull;

/**
 * <AUTHOR>
 * @date 2024/10/14 10:58
 */
@Getter
@Builder
public class RequestUserSignAdminInvite {

    @NonNull
    private Integer appId;

    /**
     * 操作用户ID
     */
    @NonNull
    private Long curUserId;

    /**
     * 签约记录ID
     */
    @NonNull
    private Long playerSignId;

    /**
     * 签署意见
     */
    @NonNull
    private OperateTypeEnum operateTypeEnum;

}
