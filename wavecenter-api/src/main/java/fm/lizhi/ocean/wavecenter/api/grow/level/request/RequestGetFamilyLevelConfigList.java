package fm.lizhi.ocean.wavecenter.api.grow.level.request;

import fm.lizhi.ocean.wavecenter.api.common.bean.IContextRequest;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2025/3/18 18:09
 */
@Data
@Accessors(chain = true)
public class RequestGetFamilyLevelConfigList implements IContextRequest {

    private String name;

    private Integer appId;

    @Override
    public Integer foundIdAppId() {
        return this.appId;
    }
}
