package fm.lizhi.ocean.wavecenter.api.activitycenter.service;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.activitycenter.response.ResponseGetActivityNotice;

public interface ActivityNoticeService {

    /**
     * 获取活动公告配置
     * @param appId 应用id
     * @return 活动公告配置
     */
    Result<ResponseGetActivityNotice> getNoticeConfig(Integer appId, Long userId);

    /**
     * 获取活动公告配置参数无效
     */
    int GET_NOTICE_PARAM_INVALID = 22500000;

    /**
     * 获取活动公告配置失败
     */
    int GET_NOTICE_FAIL = 22500001;

}
