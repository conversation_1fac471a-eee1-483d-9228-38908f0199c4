package fm.lizhi.ocean.wavecenter.api.live.bean;


import fm.lizhi.ocean.wavecenter.api.user.bean.RoomBean;
import lombok.Data;

import java.util.List;

@Data
public class GuildRoomHourStatsRes implements IDetailList<GuildRoomHourDetail, GuildRoomHourStats>{


    private RoomBean room;

    /**
     * 合计
     */
    private GuildRoomHourStats stats;

    /**
     * 明细
     */
    private List<GuildRoomHourDetail> detail;

    @Override
    public List<GuildRoomHourDetail> foundDetail() {
        return this.detail;
    }

    @Override
    public GuildRoomHourStats foundStats() {
        return this.stats;
    }
}
