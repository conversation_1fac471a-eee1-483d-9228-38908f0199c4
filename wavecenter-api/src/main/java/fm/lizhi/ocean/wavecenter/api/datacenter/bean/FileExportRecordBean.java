package fm.lizhi.ocean.wavecenter.api.datacenter.bean;

import lombok.*;

import java.util.Date;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 文件导出记录
 *
 * @date 2024-04-19 11:30:35
 */
@NoArgsConstructor
@Data
public class FileExportRecordBean {

    private Long id;

    /**
     * 业务
     */
    private Integer appId;

    /**
     * 文件状态 1: 导出中, 2: 完成导出, 3=导出失败
     */
    private Integer fileStatus;

    /**
     * 文件路径
     */
    private String filePath;

    /**
     * 文件名
     */
    private String fileName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date modifyTime;

    /**
     * 文件所属用户ID
     */
    private Long userId;

    /**
     * 0=未删除, 1=已删除
     */
    private Integer deleted;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", appId=").append(appId);
        sb.append(", fileStatus=").append(fileStatus);
        sb.append(", filePath=").append(filePath);
        sb.append(", fileName=").append(fileName);
        sb.append(", createTime=").append(createTime);
        sb.append(", modifyTime=").append(modifyTime);
        sb.append(", userId=").append(userId);
        sb.append(", deleted=").append(deleted);
        sb.append("]");
        return sb.toString();
    }
}