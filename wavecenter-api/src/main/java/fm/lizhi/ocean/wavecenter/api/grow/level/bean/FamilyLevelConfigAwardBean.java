package fm.lizhi.ocean.wavecenter.api.grow.level.bean;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/18 18:08
 */
@Data
public class FamilyLevelConfigAwardBean {

    private Long id;

    /**
     * 等级名称
     */
    private String levelName;

    /**
     * 等级值
     */
    private Integer levelValue;

    /**
     * 最小经验值
     */
    private Integer minExp;

    /**
     * 角标
     */
    private String levelIcon;

    /**
     * 勋章
     */
    private String levelMedal;

    /**
     * 主题色
     */
    private String themColor;

    /**
     * 底色
     */
    private String backgroundColor;

    /**
     * 最近操作人
     */
    private String modifyUser;

    /**
     * 奖励宣传图
     */
    private List<String> awardImgs;

}
