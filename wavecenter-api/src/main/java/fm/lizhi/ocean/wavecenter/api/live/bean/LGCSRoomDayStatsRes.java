package fm.lizhi.ocean.wavecenter.api.live.bean;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/25 19:29
 */
@Data
@Accessors(chain = true)
public class LGCSRoomDayStatsRes {

    private Integer total;

    private List<GuildRoomDayStatsRes> list = new ArrayList<>();

    private List<TimeStatsBean> timeStats = new ArrayList<>();

    private TimeStatsSumBean timeStatSum;

}
