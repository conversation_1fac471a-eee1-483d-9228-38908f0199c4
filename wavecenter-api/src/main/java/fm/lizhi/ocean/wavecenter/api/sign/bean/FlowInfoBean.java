package fm.lizhi.ocean.wavecenter.api.sign.bean;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2024/10/17 17:00
 */
@Data
@Accessors(chain = true)
public class FlowInfoBean {

    /**
     * 创作者侧流程ID
     */
    private Long flowId;

    /**
     * 合同ID
     */
    private Long contractId;

    /**
     * 签约ID
     */
    private String signId;

    /**
     * 流程类型：SIGN=签约，CANCEL=解约
     */
    private String type;

    /**
     * 状态
     * @see fm.lizhi.ocean.wavecenter.api.sign.constant.SignRelationEnum
     */
    private String status;

    /**
     * 是否本人发起
     */
    private Boolean selfCreate;

    /**
     * 发起人角色
     * ROOM=管理员发起, PLAYER=主播发起, FAMILY=家族发起
     */
    private String createUser;

    /**
     * 关联类型 WITH_FAMILY=与家族签约 WITH_ROOM=与厅签约
     */
    private String relationType;

}
