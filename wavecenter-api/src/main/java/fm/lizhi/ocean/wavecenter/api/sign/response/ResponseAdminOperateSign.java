package fm.lizhi.ocean.wavecenter.api.sign.response;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2024/10/11 20:38
 */
@Data
@Accessors(chain = true)
public class ResponseAdminOperateSign implements ResponseSignResult{
    private Integer code;
    private String msg;

    @Override
    public int getResultCode() {
        return this.code;
    }

    @Override
    public String getResultMsg() {
        return this.msg;
    }
}
