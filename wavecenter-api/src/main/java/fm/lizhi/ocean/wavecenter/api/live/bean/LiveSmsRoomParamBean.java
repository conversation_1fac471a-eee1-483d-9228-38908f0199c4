package fm.lizhi.ocean.wavecenter.api.live.bean;

import fm.lizhi.ocean.wavecenter.api.common.util.ApiAssert;
import fm.lizhi.ocean.wavecenter.api.common.constants.DateType;
import fm.lizhi.ocean.wavecenter.api.common.constants.MetricsEnum;
import fm.lizhi.ocean.wavecenter.api.common.constants.OrderType;
import lombok.Builder;
import lombok.Getter;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/20 15:35
 */
@Getter
@Builder
public class LiveSmsRoomParamBean {

    private Integer appId;

    private Long userId;

    private Integer pageNo = 1;

    private Integer pageSize = 20;

    /**
     * 排序字段
     */
    private String orderMetrics;

    /**
     * 排序类型
     */
    private OrderType orderType;

    private DateType dateType;

    private Date startDate;

    private Date endDate;

    /**
     * 是否过滤私信用户数为0的厅
     */
    private boolean filterZero;

    private Long roomId;

    private Long familyId;

    /**
     * 厅数据范围
     * @since 1.3.1
     */
    private List<Long> roomIds;

    public static class LiveSmsRoomParamBeanBuilder{
        public LiveSmsRoomParamBean build(){
            ApiAssert.notNull(appId, "appId can not be null");
            ApiAssert.notNull(userId, "appId can not be null");
            ApiAssert.notNull(dateType, "dateType can not be null");
            ApiAssert.notNull(startDate, "dateType can not be null");
            ApiAssert.notNull(familyId, "familyId can not be null");

            if (dateType == DateType.WEEK) {
                ApiAssert.notNull(endDate, "endDate can not be null");
            }

            if (orderMetrics == null) {
                orderMetrics = MetricsEnum.SIGN_PLAYER_CNT.getValue();
            }
            if (orderType == null) {
                orderType = OrderType.DESC;
            }

            if (pageNo == null || pageNo <= 0) {
                pageNo = 1;
            }
            if (pageSize == null || pageSize <= 0) {
                pageSize = 20;
            }
            if (pageSize > 50) {
                pageSize = 50;
            }

            return new LiveSmsRoomParamBean(appId, userId, pageNo
                    , pageSize
                    , orderMetrics
                    , orderType
                    , dateType
                    , startDate
                    , endDate
                    , filterZero
                    , roomId
                    , familyId
                    , roomIds
            );
        }
    }

}
