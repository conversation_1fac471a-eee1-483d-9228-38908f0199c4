package fm.lizhi.ocean.wavecenter.api.background.activitycenter.bean;


import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 玩法配置
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class ActivityToolsConfigBean{

    /**
     * 描述
     */
    private String toolDesc;

    /**
     * 玩法工具值
     */
    private int type;

    /**
     * 名称
     */
    private String name;

    /**
     * 工具类型, 1:玩法; 2: 工具
     */
    private Integer toolType;

}
