package fm.lizhi.ocean.wavecenter.api.user.response;

import fm.lizhi.ocean.wavecenter.api.user.bean.SearchFamilyBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.SearchRoomBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserRoleInfoBean;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2024/10/21 14:36
 */
@Data
@Accessors(chain = true)
public class ResponseSearchUser {

    private UserRoleInfoBean userInfo;

    private SearchFamilyBean familyInfo;

    private SearchRoomBean roomInfo;

}
