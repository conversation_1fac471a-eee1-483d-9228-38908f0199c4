package fm.lizhi.ocean.wavecenter.api.sign.request;

import fm.lizhi.ocean.wavecenter.api.common.util.ApiAssert;
import lombok.Builder;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/10/23 14:38
 */
@Getter
@Builder
public class RequestUserApplyAdmin {

    private Integer appId;

    private Long familyId;

    private Long targetUserId;

    private Long curUserId;

    public static class RequestUserApplyAdminBuilder {
        public RequestUserApplyAdmin build() {
            ApiAssert.notNull(appId, "appId is null");
            ApiAssert.notNull(curUserId, "curUserId is null");
            return new RequestUserApplyAdmin(appId, familyId, targetUserId, curUserId);
        }
    }

}
