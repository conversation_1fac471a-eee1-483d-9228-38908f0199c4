package fm.lizhi.ocean.wavecenter.api.user.bean;

import fm.lizhi.ocean.wavecenter.api.common.util.ApiAssert;
import lombok.Builder;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/4/15 15:31
 */
@Getter
@Builder
public class QrCodeLoginParamBean {
    private Integer appId;
    private String qrCodeKey;
    private String businessToken;
    private String ip;
    private String deviceType;
    private String clientVersion;

    public static class QrCodeLoginParamBeanBuilder{
        public QrCodeLoginParamBean build(){
            ApiAssert.notNull(appId, "appId can not be null");
            ApiAssert.hasText(qrCodeKey, "qrCodeKey can not be null");
            ApiAssert.hasText(businessToken, "businessToken can not be null");
            ApiAssert.hasText(ip, "ip can not be null");
            return new QrCodeLoginParamBean(appId, qrCodeKey, businessToken, ip, deviceType, clientVersion);
        }
    }

}
