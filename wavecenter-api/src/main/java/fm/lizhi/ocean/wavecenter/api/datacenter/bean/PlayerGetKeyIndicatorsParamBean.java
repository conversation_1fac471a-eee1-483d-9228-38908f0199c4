package fm.lizhi.ocean.wavecenter.api.datacenter.bean;

import fm.lizhi.ocean.wavecenter.api.common.constants.DateType;
import fm.lizhi.ocean.wavecenter.api.common.util.ApiAssert;
import lombok.Builder;
import lombok.Getter;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/19 14:19
 */
@Getter
@Builder
public class PlayerGetKeyIndicatorsParamBean implements IGetKeyIndicatorsParam{

    private Integer appId;

    /**
     * @see DateType
     */
    private DateType dateType;

    /**
     * 开始时间 YYYY-MM-DD
     */
    private String startDate;

    private String endDate;

    /**
     * 只查询值的指标
     */
    private List<String> valueMetrics;

    /**
     * 需要查询值和环比的指标
     */
    private List<String> ratioMetrics;

    /**
     * 陪玩ID
     */
    private Long playerId;

    private Long roomId;

    private Long familyId;

    public static class PlayerGetKeyIndicatorsParamBeanBuilder{
        public PlayerGetKeyIndicatorsParamBean build(){
            ApiAssert.notNull(appId, "appId is required");
            ApiAssert.notNull(dateType, "dateType is required");
            ApiAssert.hasText(startDate, "startDate is required");
            ApiAssert.notNull(playerId, "playerId is required");

            if (dateType == DateType.WEEK) {
                ApiAssert.hasText(endDate, "endDate is required");
            }

            if (valueMetrics == null) {
                valueMetrics = Collections.emptyList();
            }
            if (ratioMetrics == null) {
                ratioMetrics = Collections.emptyList();
            }

            return new PlayerGetKeyIndicatorsParamBean(appId, dateType, startDate, endDate, valueMetrics, ratioMetrics, playerId, roomId, familyId);
        }
    }

}
