package fm.lizhi.ocean.wavecenter.api.sign.service;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.sign.request.*;
import fm.lizhi.ocean.wavecenter.api.sign.response.*;

/**
 * 普通用户签约
 * <AUTHOR>
 */
public interface SignUserService {

    /**
     * 申请成为主播
     * @param request
     * @return
     */
    Result<ResponseUserApplyPlayer> applyPlayer(RequestUserApplyPlayer request);

    /**
     * 签署管理员申请
     * @return
     */
    Result<ResponseUserSignAdminInvite> signAdminInvite(RequestUserSignAdminInvite request);

    /**
     * 检查用户实名状态
     * @return
     */
    Result<ResponseUserIdentifyStatus> identifyStatus(RequestUserIdentifyStatus request);

    /**
     * 用户签约信息检查
     * @param request
     * @return
     */
    Result<ResponseUserInfoStatus> infoStatus(RequestUserInfoStatus request);

    /**
     * 申请成为管理员（厅主）
     * @param request
     * @return
     */
    Result<ResponseUserApplyAdmin> applyAdmin(RequestUserApplyAdmin request);

    /**
     * 签署合同
     * @return
     */
    Result<ResponseUserDoSign> doSign(RequestUserDoSign request);

    //----------错误码-------------
    /**
     * 系统升级，功能临时关闭
     */
    int APPLY_PLAYER_SYSTEM_FUNCTION_CLOSE = 2010001;

    /**
     * 目标用户不是管理员
     */
    int APPLY_PLAYER_TARGET_NOT_ROOM = 2010002;

    /**
     * 签约限制检查失败
     */
    int APPLY_PLAYER_SIGN_LIMIT_CHECK_FAIL = 2010003;

    /**
     * 请求用户-平台实名认证未通过
     */
    int APPLY_PLAYER_REQ_USER_PLATFORM_VERIFY_NO_PASS = SignErrorCode.REQ_USER_NO_PLATFORM_VERIFY;

    /**
     * 性别未设置
     */
    int APPLY_PLAYER_REQ_USER_GENDER_NOT_SET = SignErrorCode.REQ_USER_GENDER_NO_SETTING;

    /**
     * 媒体资料不完整
     */
    int APPLY_PLAYER_REQ_USER_MEDIA_INFO_NOT_EXIST = SignErrorCode.REQ_USER_MEDIA_INFO_NO_EXIST;

    /**
     * 目标用户不存在
     */
    int APPLY_PLAYER_TARGET_USER_NOT_EXIST = 2010008;

    /**
     * 请求用户已签约
     */
    int APPLY_PLAYER_REQ_SIGNED = SignErrorCode.PLAYER_SIGNED;

    /**
     * 实名身份证没有关联用户
     */
    int APPLY_PLAYER_USER_VERIFY_NO_REF_USER = 2010010;

    /**
     * 目标用户未加入家族
     */
    int APPLY_PLAYER_TARGET_USER_NO_FAMILY = 2010011;

    /**
     * 请求用户其他实名账号已加入其他家族
     */
    int APPLY_PLAYER_JOINED_FAMILY = 2010012;

    /**
     * 未进行实名认证
     */
    int SIGN_ADMIN_INVITE_NO_VERIFY = SignErrorCode.REQ_USER_NO_PLATFORM_VERIFY;

    /**
     * 未完成上上签实名认证
     */
    int APPLY_ADMIN_NO_SIGN_VERIFY = SignErrorCode.REQ_USER_NO_SIGN_VERIFY;

    /**
     * 未完成平台实名认证
     */
    int APPLY_ADMIN_NO_PLATFORM_VERIFY = SignErrorCode.REQ_USER_NO_PLATFORM_VERIFY;

    /**
     * 用户媒体信息不完整
     */
    int APPLY_ADMIN_USER_INFO_NOT_EXIST = SignErrorCode.REQ_USER_MEDIA_INFO_NO_EXIST;

    /**
     * 目标用户未加入家族
     */
    int APPLY_ADMIN_TARGET_USER_NO_FAMILY = 2010013;

    /**
     * 请求用户其他实名账号已加入其他家族
     */
    int APPLY_ADMIN_JOINED_FAMILY = 2010014;

    /**
     * 签署失败
     */
    int DO_SIGN_FAIL = 2010015;

    /**
     * 未完成主播中心认证
     */
    int APPLY_PLAYER_PLAYER_CENTER = SignErrorCode.PLAYER_CENTER_AUTH_NULL;


}
