package fm.lizhi.ocean.wavecenter.api.sign.constant;

/**
 * <AUTHOR>
 * @date 2024/10/22 18:14
 */
public enum IdentifyStatusEnum {

    /**
     * 未完成
     */
    UNFINISHED("UNFINISHED")

    /**
     * 已完成(通过)
     */
    , FINISHED("FINISHED")

    /**
     * 审核中
     */
    , WAIT_AUDIT("WAIT_AUDIT")
    ;

    private String code;

    IdentifyStatusEnum(String code) {
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public static IdentifyStatusEnum getEnum(String code) {
        for (IdentifyStatusEnum value : IdentifyStatusEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

}
