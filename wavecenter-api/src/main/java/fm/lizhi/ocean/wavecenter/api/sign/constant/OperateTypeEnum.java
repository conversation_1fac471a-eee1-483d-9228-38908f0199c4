package fm.lizhi.ocean.wavecenter.api.sign.constant;

/**
 * <AUTHOR>
 * @date 2024/10/11 20:52
 */
public enum OperateTypeEnum {

    AGREE("AGREE")
    , REJECT("REJECT")
    ;

    private String code;

    OperateTypeEnum(String code) {
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public static OperateTypeEnum getByCode(String code) {
        for (OperateTypeEnum operateTypeEnum : OperateTypeEnum.values()) {
            if (operateTypeEnum.getCode().equals(code)) {
                return operateTypeEnum;
            }
        }
        return null;
    }
}
