package fm.lizhi.ocean.wavecenter.api.sign.bean;

import fm.lizhi.ocean.wavecenter.api.user.bean.UserBean;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/10/15 14:51
 */
@Data
@Accessors(chain = true)
public class TodoSignPlayerBean implements IAdminSignRecord{

    /**
     * 合同ID playerSignId
     */
    private Long contractId;

    /**
     * 解约记录的前合同ID
     */
    private Long oldContractId;

    /**
     * 主播信息
     */
    private UserBean playerInfo;

    /**
     * 厅主信息
     */
    private UserBean roomInfo;

    /**
     * 发起用户角色
     */
    private String createUser;

    /**
     * 是否本人发起,即管理员
     */
    private boolean selfCreate;

    /**
     * 类型
     * SIGN=签约，CANCEL=解约
     */
    private String signType;

    /**
     * 状态
     */
    private String status;

    /**
     * 签署有效期（解约的自动解约时间） 签署截止时间
     */
    private Date signDeadline;

    /**
     * 签约生效开始时间
     */
    private Date startTime;

    private Date oldStartTime;

    /**
     * 签约生效结束时间
     */
    private Date endTime;

    /**
     * 解约时间
     */
    private Date stopTime;

    /**
     * 签约申请发起时间
     */
    private Date createTime;

    @Override
    public String findStatus() {
        return this.status;
    }

    @Override
    public String findCreateUser() {
        return this.createUser;
    }

    @Override
    public void changeStatus(String status) {
        this.status = status;
    }
}
