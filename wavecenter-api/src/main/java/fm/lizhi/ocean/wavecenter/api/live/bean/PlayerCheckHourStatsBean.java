package fm.lizhi.ocean.wavecenter.api.live.bean;

import fm.lizhi.ocean.wavecenter.api.user.bean.RoomBean;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 陪玩小时统计指标
 * <AUTHOR>
 * @date 2024/6/11 17:58
 */
@Data
@Accessors(chain = true)
public class PlayerCheckHourStatsBean {

    /**
     * 档期开始时间
     */
    private Date startTime;

    private Date endTime;

    private BigDecimal income;

    private Long charm;

    private Long charmValue;

    /**
     * 是否是主持,0: 不是主持  1：主持
     */
    private Integer isHost;

    /**
     * 0：未打卡，1：已打卡，2：确认打卡
     */
    private Integer checkInStatus;

    /**
     * 打卡厅
     */
    private RoomBean room;

    /**
     * 备注
     */
    private String remark;

}
