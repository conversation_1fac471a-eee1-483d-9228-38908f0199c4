package fm.lizhi.ocean.wavecenter.api.sign.bean;

import fm.lizhi.ocean.wavecenter.api.user.bean.UserInfoBean;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/10/15 14:51
 */
@Data
@Accessors(chain = true)
public class TodoSignBean implements ISignRecord{

    private Long familyId;

    /**
     * 合同ID
     */
    private Long contractId;

    /**
     * 签约ID
     */
    private String signId;

    /**
     * 厅主信息
     */
    private UserInfoBean roomInfo;

    /**
     * 下级人数
     */
    private Integer signCount;

    /**
     * 结算方式
     */
    private String settleType;

    /**
     * 分成比例
     */
    private Integer settlePercentage;

    /**
     * 发起时间
     */
    private Date createTime;

    /**
     * 类型
     * SIGN=签约，CANCEL=解约
     */
    private String signType;

    /**
     * 状态
     */
    private String status;

    /**
     * 管理员签署状态
     * @see fm.lizhi.ocean.wavecenter.api.sign.constant.UserSignStatusEnum
     */
    private String njSignStatus;

    /**
     * 家族签署状态
     * @see fm.lizhi.ocean.wavecenter.api.sign.constant.UserSignStatusEnum
     */
    private String familySignStatus;

    /**
     * 签署有效期
     */
    private Date signDeadline;

    @Override
    public String findStatus() {
        return this.status;
    }

    @Override
    public String findNjSignStatus() {
        return this.njSignStatus;
    }

    @Override
    public String findFamilySignStatus() {
        return this.familySignStatus;
    }

    @Override
    public void changeStatus(String status) {
        this.status = status;
    }

    @Override
    public String findSignId() {
        return this.signId;
    }
}
