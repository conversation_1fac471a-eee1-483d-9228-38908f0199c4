package fm.lizhi.ocean.wavecenter.api.live.bean;


import lombok.Data;

import java.util.Date;

@Data
public class GuildRoomHourDetail implements IDetailEle{

    /**
     * 日的值 yyyy-MM-dd HH:mm
     */
    private Date time;

    private String income;

    private Long charm;

    /**
     * 麦序
     */
    private Integer seatOrder;

    /**
     * 打卡主播数
     */
    private Integer checkPlayerNumber;

    @Override
    public Date foundTime() {
        return this.time;
    }

    @Override
    public String foundIncome() {
        return this.income;
    }

    @Override
    public Integer foundCharm() {
        if (this.charm == null) {
            return 0;
        }
        return Math.toIntExact(this.charm);
    }

    @Override
    public Integer foundSeatOrder() {
        return this.seatOrder;
    }

    @Override
    public Integer foundHostCnt() {
        return null;
    }

    @Override
    public Integer foundCheckPlayerNumber() {
        return this.checkPlayerNumber;
    }
}
