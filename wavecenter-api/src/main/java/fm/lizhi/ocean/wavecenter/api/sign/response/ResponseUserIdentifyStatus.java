package fm.lizhi.ocean.wavecenter.api.sign.response;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2024/10/22 18:13
 */
@Data
@Accessors(chain = true)
public class ResponseUserIdentifyStatus {

    /**
     * 平台实名
     * @see fm.lizhi.ocean.wavecenter.api.sign.constant.IdentifyStatusEnum
     */
    private String platformStatus;

    /**
     * 上上签实名
     * @see fm.lizhi.ocean.wavecenter.api.sign.constant.IdentifyStatusEnum
     */
    private String bestSignStatus;

}
