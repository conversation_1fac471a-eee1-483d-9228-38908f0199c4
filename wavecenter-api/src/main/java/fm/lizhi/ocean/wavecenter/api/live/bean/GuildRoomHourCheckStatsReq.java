package fm.lizhi.ocean.wavecenter.api.live.bean;


import fm.lizhi.ocean.wavecenter.api.common.util.ApiAssert;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class GuildRoomHourCheckStatsReq {

    private Integer appId;
    private Long familyId;
    private Long roomId;
    private Date startDate;
    private Date endDate;

    public static class GuildRoomHourCheckStatsReqBuilder {
        public GuildRoomHourCheckStatsReq build() {
            ApiAssert.notNull(appId, "appId is required");
            ApiAssert.notNull(familyId, "familyId is required");
            ApiAssert.notNull(startDate, "startDate is required");
            ApiAssert.notNull(endDate, "endDate is required");
            return new GuildRoomHourCheckStatsReq(appId, familyId, roomId, startDate, endDate);
        }
    }
}
