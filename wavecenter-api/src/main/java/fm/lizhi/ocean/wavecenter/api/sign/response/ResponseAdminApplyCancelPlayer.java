package fm.lizhi.ocean.wavecenter.api.sign.response;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2024/10/14 16:57
 */
@Data
@Accessors(chain = true)
public class ResponseAdminApplyCancelPlayer implements ResponseSignResult{

    private Integer code;

    private String msg;

    private Long contractId;

    @Override
    public int getResultCode() {
        return this.code;
    }

    @Override
    public String getResultMsg() {
        return this.msg;
    }
}
