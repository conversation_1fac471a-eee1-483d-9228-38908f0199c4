package fm.lizhi.ocean.wavecenter.api.sign.bean;

import fm.lizhi.ocean.wavecenter.api.user.bean.UserBean;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/10/19 15:10
 */
@Data
public class PlayerSignHistoryRecordBean implements IAdminSignRecord{

    /**
     * 合同ID
     */
    private Long contractId;

    private UserBean roomInfo;

    /**
     * 发起时间
     */
    private Date createTime;

    /**
     * 解约时间
     */
    private Date stopTime;

    /**
     * 签约时间
     */
    private Date startTime;

    /**
     * 解约的原合同签约时间
     */
    private Date oldStartTime;

    /**
     * 状态
     */
    private String status;

    /**
     * 类型 SIGN签约 CANCEL解约
     */
    private String type;

    /**
     * 发起人角色
     * ROOM=管理员发起, PLAYER=主播发起, FAMILY=家族发起
     */
    private String createUser;

    @Override
    public String findStatus() {
        return this.status;
    }

    @Override
    public String findCreateUser() {
        return this.createUser;
    }

    @Override
    public void changeStatus(String status) {
        this.status = status;
    }
}
