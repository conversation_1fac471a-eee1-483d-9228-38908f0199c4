package fm.lizhi.ocean.wavecenter.api.user.bean;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;

@AllArgsConstructor
@Getter
@Builder
public class GuildPlayerBean {

    /**
     * 工会id
     */
    private long id;

    /**
     * appID
     */
    private int appId;

    /**
     * 签约状态
     * 1=已签约
     * 0=已解约
     * -1: 查询全部
     */
    private int status;

    public GuildPlayerBean() {
        this.status = -1;
    }

}
