package fm.lizhi.ocean.wavecenter.api.sign.response;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2024/10/26 14:14
 */
@Data
@Accessors(chain = true)
public class ResponseAdminApplyCancelFamily implements ResponseSignResult{

    private Integer code = SUCCESS_CODE;

    private String msg;

    private String contractUrl;

    private String signId;

    private Long contractId;

    @Override
    public int getResultCode() {
        return this.code;
    }

    @Override
    public String getResultMsg() {
        return this.msg;
    }

}
