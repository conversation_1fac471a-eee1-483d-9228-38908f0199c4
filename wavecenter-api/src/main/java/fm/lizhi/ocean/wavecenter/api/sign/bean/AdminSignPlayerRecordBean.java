package fm.lizhi.ocean.wavecenter.api.sign.bean;

import fm.lizhi.ocean.wavecenter.api.user.bean.UserBean;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/10/8 14:13
 */
@Data
public class AdminSignPlayerRecordBean implements IAdminSignRecord{

    private Long contractId;

    private Long oldContractId;

    private UserBean playerInfo;

    private String status;

    /**
     * 是否本人发起,即管理员
     */
    private boolean isSelfCreate;

    /**
     * 类型 SIGN签约 CANCEL解约
     */
    private String type;

    /**
     * 发起人角色
     * ROOM=管理员发起, PLAYER=主播发起, FAMILY=家族发起
     */
    private String createUser;

    /**
     * 签署有效期（解约的自动解约时间） 签署截止时间
     */
    private Date signDeadline;

    /**
     * 签约生效开始时间
     */
    private Date startTime;

    /**
     * 解约合同 原合同的签约生效时间
     */
    private Date oldStartTime;

    /**
     * 签约生效结束时间
     */
    private Date endTime;

    /**
     * 解约时间
     */
    private Date stopTime;

    /**
     * 签约申请发起时间
     */
    private Date createTime;

    @Override
    public String findStatus() {
        return this.status;
    }

    @Override
    public String findCreateUser() {
        return this.createUser;
    }

    @Override
    public void changeStatus(String status) {
        this.status = status;
    }
}
