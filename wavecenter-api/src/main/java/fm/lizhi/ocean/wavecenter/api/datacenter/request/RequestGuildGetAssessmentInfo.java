package fm.lizhi.ocean.wavecenter.api.datacenter.request;

import fm.lizhi.ocean.wavecenter.api.common.annotation.AppEnumId;
import fm.lizhi.ocean.wavecenter.api.common.bean.IContextRequest;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/10 20:14
 */
@Data
public class RequestGuildGetAssessmentInfo implements IContextRequest {

    @AppEnumId
    @NotNull(message = "appId is null")
    private Integer appId;

    @NotNull(message = "familyId is null")
    private Long familyId;

    /**
     * 厅数据范围
     */
    private List<Long> roomIds;

    @Override
    public Integer foundIdAppId() {
        return this.appId;
    }
}
