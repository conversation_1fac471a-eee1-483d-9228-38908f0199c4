package fm.lizhi.ocean.wavecenter.api.activitycenter.constants;

import lombok.Getter;

@Getter
public enum ActivityStatusEnum {

    /**
     * 未开始
     */
    UN_START(1),
    /**
     * 进行中
     */
    START(2),
    /**
     * 已结束
     */
    END(3),

    /**
     * 无效的活动
     * 未审核，但当前时间已过活动开始时间
     */
    INVALID(4),
    ;

    ActivityStatusEnum(Integer status) {
        this.status = status;
    }



    private final Integer status;

}
