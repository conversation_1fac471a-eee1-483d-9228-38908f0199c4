package fm.lizhi.ocean.wavecenter.api.award.family.request;

import fm.lizhi.ocean.wavecenter.api.award.family.bean.SaveFamilyLevelAwardItemBean;
import fm.lizhi.ocean.wavecenter.api.common.annotation.AppEnumId;
import fm.lizhi.ocean.wavecenter.api.common.bean.IContextRequest;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 更新公会等级奖励规则请求
 */
@Data
public class RequestUpdateFamilyLevelAwardRule implements IContextRequest {

    /**
     * 规则id
     */
    @NotNull(message = "规则id不能为空")
    private Long id;

    /**
     * 应用id
     */
    @NotNull(message = "appId不能为空")
    @AppEnumId
    private Integer appId;

    /**
     * 公会等级奖励条目
     */
    @NotEmpty(message = "公会等级奖励条目不能为空")
    @Valid
    private List<SaveFamilyLevelAwardItemBean> items;

    /**
     * 操作人
     */
    @NotNull(message = "操作人不能为空")
    private String operator;

    @Override
    public Integer foundIdAppId() {
        return this.appId;
    }
}
