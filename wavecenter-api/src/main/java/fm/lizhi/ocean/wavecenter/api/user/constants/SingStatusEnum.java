package fm.lizhi.ocean.wavecenter.api.user.constants;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/4/10 16:51
 */
public enum SingStatusEnum {

    /**
     * 已签约
     */
    SING(1)
    /**
     * 未签约
     */
    , STOP(0);

    @Getter
    private int value;

    SingStatusEnum(int value) {
        this.value = value;
    }

    public static SingStatusEnum fromValue(Integer value) {

        if (value == null) {
            return null;
        }

        for (SingStatusEnum status : SingStatusEnum.values()) {
            if (status.value == value) {
                return status;
            }
        }
        return null;
    }
}
