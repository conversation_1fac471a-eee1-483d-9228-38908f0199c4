package fm.lizhi.ocean.wavecenter.api.sign.request;

import fm.lizhi.ocean.wavecenter.api.common.util.ApiAssert;
import lombok.Builder;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/10/15 14:56
 */
@Getter
@Builder
public class RequestFamilyTodoRoomList {

    private Integer appId;

    private Long familyId;

    public static class RequestFamilyTodoRoomListBuilder {
        public RequestFamilyTodoRoomList build() {
            ApiAssert.notNull(appId, "appId is null");
            ApiAssert.notNull(familyId, "familyId is null");
            return new RequestFamilyTodoRoomList(appId, familyId);
        }
    }

}
