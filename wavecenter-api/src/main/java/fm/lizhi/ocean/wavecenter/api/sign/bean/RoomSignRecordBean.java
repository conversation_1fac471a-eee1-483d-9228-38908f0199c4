package fm.lizhi.ocean.wavecenter.api.sign.bean;

import fm.lizhi.ocean.wavecenter.api.user.bean.UserInfoBean;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/10/8 11:23
 */
@Data
@Accessors(chain = true)
public class RoomSignRecordBean implements ISignRecord{

    /**
     * 合同ID
     */
    private Long contractId;

    /**
     * 解约记录的原合同
     */
    private Long oldContractId;

    /**
     * 签约ID
     */
    private String signId;

    /**
     * 厅主信息
     */
    private UserInfoBean roomInfo;

    private Long familyId;

    /**
     * 结算方式
     */
    private String settleType;

    /**
     * 分成比例
     */
    private Integer settlePercentage;

    /**
     * 下级人数
     */
    private Integer signCount;

    /**
     * 状态
     */
    private String status;

    /**
     * 管理员签署状态
     * @see fm.lizhi.ocean.wavecenter.api.sign.constant.UserSignStatusEnum
     */
    private String njSignStatus;

    /**
     * 家族签署状态
     * @see fm.lizhi.ocean.wavecenter.api.sign.constant.UserSignStatusEnum
     */
    private String familySignStatus;

    /**
     * 签署截止时间
     */
    private Date signDeadline;

    /**
     * 签约完成时间
     */
    private Date signFinishTime;

    /**
     * 合同开始时间
     */
    private Date beginTime;

    /**
     * 解约合同 原签约合同的开始时间
     */
    private Date oldBeginTime;

    /**
     * 合同到期时间
     */
    private Date expireTime;

    /**
     * 原合同到期时间
     */
    private Date oldExpireTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 终止/解约合同时间
     */
    private Date stopTime;

    @Override
    public String findStatus() {
        return this.status;
    }

    @Override
    public String findNjSignStatus() {
        return this.njSignStatus;
    }

    @Override
    public String findFamilySignStatus() {
        return this.familySignStatus;
    }

    @Override
    public void changeStatus(String status) {
        this.status = status;
    }

    @Override
    public String findSignId() {
        return this.signId;
    }
}
