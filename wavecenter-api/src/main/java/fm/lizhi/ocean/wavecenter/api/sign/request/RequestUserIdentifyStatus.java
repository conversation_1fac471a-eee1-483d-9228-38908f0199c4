package fm.lizhi.ocean.wavecenter.api.sign.request;

import fm.lizhi.ocean.wavecenter.api.common.util.ApiAssert;
import lombok.Builder;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/10/22 18:11
 */
@Getter
@Builder
public class RequestUserIdentifyStatus {

    private Integer appId;

    private Long userId;

    public static class RequestUserIdentifyStatusBuilder {
        public RequestUserIdentifyStatus build() {
            ApiAssert.notNull(appId, "appId is null");
            ApiAssert.notNull(userId, "userId is null");
            return new RequestUserIdentifyStatus(appId, userId);
        }
    }

}
