package fm.lizhi.ocean.wavecenter.api.live.constants;

import lombok.Getter;

/**
 * 平台厅品类值
 * <AUTHOR>
 * @date 2025/5/9 15:00
 */
public enum RoomCategoryEnum {

    /**
     * 娱乐
     */
    AMUSEMENT(1, "娱乐厅")

    /**
     * 点唱
     */
    , SING(2, "点唱厅")
    ;

    @Getter
    private Integer value;

    @Getter
    private String displayName;

    RoomCategoryEnum(Integer value, String displayName) {
        this.value = value;
        this.displayName = displayName;
    }

    public static RoomCategoryEnum getByValue(Integer value) {
        for (RoomCategoryEnum roomCategoryEnum : values()) {
            if (roomCategoryEnum.value.equals(value)) {
                return roomCategoryEnum;
            }
        }
        return null;
    }
}
