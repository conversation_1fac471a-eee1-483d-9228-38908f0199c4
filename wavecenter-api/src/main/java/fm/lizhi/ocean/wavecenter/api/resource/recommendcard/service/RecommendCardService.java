package fm.lizhi.ocean.wavecenter.api.resource.recommendcard.service;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.resource.recommendcard.bean.*;
import fm.lizhi.ocean.wavecenter.api.resource.recommendcard.request.*;
import fm.lizhi.ocean.wavecenter.api.resource.recommendcard.response.RewardResultBean;

import javax.validation.Valid;
import java.util.List;

/**
 * 推荐卡服务
 * 异常码 233开头
 * <AUTHOR>
 * @date 2025/3/21 14:22
 */
public interface RecommendCardService {

    /**
     * 获取用户库存
     * @param request
     * @return
     */
    Result<RecommendCardUserStockBean> getUserStock(@Valid RequestGetUserStock request);

    /**
     * 奖励推荐卡
     * @param request
     * @return
     */
    Result<RewardResultBean> rewardRecommendCard(@Valid RequestRewardRecommendCard request);

    /**
     * 查询推荐卡使用记录
     * @param request
     * @return
     */
    Result<PageBean<RecommendCardUseRecordBean>> getUseRecordForManagement(@Valid RequestGetUseRecord request);

    /**
     * 查询推荐卡发送记录
     * @return
     */
    Result<PageBean<RecommendCardSendRecordBean>> getSendRecord(@Valid RequestGetSendRecord request);

    /**
     * 批量发放推荐卡
     * @param request
     * @return
     */
    Result<List<BatchSendUserResultBean>> batchSend(@Valid RequestBatchSend request);

    /**
     * 回收推荐卡
     * @param request
     * @return
     */
    Result<Void> recycle(@Valid RequestRecycle request);

    /**
     * 查询推荐卡分配记录
     * @param request
     * @return
     */
    Result<PageBean<RecommendAllocationRecordBean>> getAllocationRecord(@Valid RequestGetAllocationRecord request);

    /**
     * 查询推荐卡候选列表
     * @param request
     * @return
     */
    Result<PageBean<AllocationItemBean>> getAllocationItemList(@Valid RequestGetAllocationItemList request);

    /**
     * 查询公会推荐卡使用记录
     * @param request
     * @return
     */
    Result<PageBean<RecommendCardUseRecordBean>> getFamilyUseRecord(@Valid RequestGetFamilyUseRecord request);

    /**
     * 用户不存在
     */
    int GET_USER_STOCK_USER_NOT_EXIST = 2330001;

    /**
     * 签约厅数超过限制
     */
    int GET_FAMILY_USE_RECORD_MAX_ROOM_COUNT = 2330101;

    /**
     * 推荐卡发放用户不一致
     */
    int BATCH_SEND_USER_NOT_VALIDATE = 2330201;

    /**
     * 回收失败
     */
    int RECYCLE_FAIL = 2330301;



}
