package fm.lizhi.ocean.wavecenter.api.live.bean;


import fm.lizhi.ocean.wavecenter.api.common.util.ApiAssert;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class RoomHourCheckStatsReq {

    private Integer appId;
    private Long roomId;
    private Date startDate;
    private Date endDate;

    public static class RoomHourCheckStatsReqBuilder {
        public RoomHourCheckStatsReq build() {
            ApiAssert.notNull(appId, "appId is required");
            ApiAssert.notNull(startDate, "startDate is required");
            ApiAssert.notNull(endDate, "endDate is required");
            return new RoomHourCheckStatsReq(appId, roomId, startDate, endDate);
        }
    }
}
