package fm.lizhi.ocean.wavecenter.api.sign.response;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2024/10/26 12:00
 */
@Data
@Accessors(chain = true)
public class ResponseFamilyDoCancelAdmin implements ResponseSignResult{

    private Integer code = SUCCESS_CODE;

    private String msg;

    private String contractUrl;

    @Override
    public int getResultCode() {
        return this.code;
    }

    @Override
    public String getResultMsg() {
        return this.msg;
    }

}
