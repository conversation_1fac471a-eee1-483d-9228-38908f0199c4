package fm.lizhi.ocean.wavecenter.api.live.bean;


import lombok.Data;

import java.util.Date;

@Data
public class RoomDayDetail implements IDetailEle{

    /**
     * 日的值 yy-mm-dd
     */
    private Date time;


    private String income;

    private Integer charm;

    /**
     * 麦序
     */
    private Integer seatOrder;


    /**
     * 主持档
     */
    private Integer hostCnt;

    @Override
    public Date foundTime() {
        return this.time;
    }

    @Override
    public String foundIncome() {
        return this.income;
    }

    @Override
    public Integer foundCharm() {
        return this.charm;
    }

    @Override
    public Integer foundSeatOrder() {
        return this.seatOrder;
    }

    @Override
    public Integer foundHostCnt() {
        return this.hostCnt;
    }

    @Override
    public Integer foundCheckPlayerNumber() {
        return null;
    }

}
