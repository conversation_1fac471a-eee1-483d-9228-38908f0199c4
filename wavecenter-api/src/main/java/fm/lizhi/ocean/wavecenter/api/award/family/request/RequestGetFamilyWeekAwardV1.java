package fm.lizhi.ocean.wavecenter.api.award.family.request;

import fm.lizhi.ocean.wavecenter.api.common.annotation.AppEnumId;
import fm.lizhi.ocean.wavecenter.api.common.bean.IContextRequest;
import lombok.Data;

import javax.validation.constraints.AssertTrue;
import javax.validation.constraints.NotNull;
import java.beans.Transient;
import java.time.DayOfWeek;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;

/**
 * 获取公会周奖励V1的请求
 */
@Data
public class RequestGetFamilyWeekAwardV1 implements IContextRequest {

    /**
     * 应用id
     */
    @NotNull(message = "appId不能为空")
    @AppEnumId
    private Integer appId;

    /**
     * 公会id
     */
    @NotNull(message = "公会id不能为空")
    private Long familyId;

    /**
     * 奖励周期开始时间
     */
    @NotNull(message = "奖励周期开始时间不能为空")
    private Long awardStartTime;

    @Transient
    @AssertTrue(message = "奖励周期开始时间必须是周一的00:00:00.000")
    private boolean isAwardStartTimeValid() {
        if (awardStartTime == null) {
            return true;
        }
        LocalDateTime localDateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(awardStartTime), ZoneId.systemDefault());
        return localDateTime.getDayOfWeek() == DayOfWeek.MONDAY
                && localDateTime.getHour() == 0
                && localDateTime.getMinute() == 0
                && localDateTime.getSecond() == 0
                && localDateTime.getNano() == 0;
    }

    @Override
    public Integer foundIdAppId() {
        return this.appId;
    }
}
