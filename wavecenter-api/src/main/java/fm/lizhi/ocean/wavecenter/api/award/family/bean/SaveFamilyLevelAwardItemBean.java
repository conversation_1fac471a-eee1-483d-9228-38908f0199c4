package fm.lizhi.ocean.wavecenter.api.award.family.bean;

import fm.lizhi.ocean.wavecenter.api.award.family.constants.FamilyAwardResourceTypeEnum;
import fm.lizhi.ocean.wavecenter.api.award.family.constants.FamilyAwardTypeEnum;
import lombok.Data;

import javax.validation.constraints.AssertTrue;
import javax.validation.constraints.NotNull;
import java.beans.Transient;
import java.util.Objects;

/**
 * 保存公会等级奖励条目
 */
@Data
public class SaveFamilyLevelAwardItemBean {

    /**
     * 资源类型
     */
    @NotNull(message = "资源类型不能为空")
    private Integer resourceType;

    /**
     * 资源数量 默认为1
     */
    private Integer resourceNumber = 1;

    /**
     * 资源有效期, 默认单位为天
     */
    private Integer resourceValidPeriod;

    /**
     * 资源id
     */
    private Long resourceId;

    @Transient
    @AssertTrue(message = "资源类型不合法")
    private boolean isResourceTypeValid() {
        if (resourceType == null) {
            return true;
        }
        for (FamilyAwardResourceTypeEnum resourceTypeEnum : FamilyAwardResourceTypeEnum.values()) {
            if (Objects.equals(resourceTypeEnum.getValue(), resourceType)
                    && Objects.equals(resourceTypeEnum.getAwardType(), FamilyAwardTypeEnum.LEVEL)) {
                return true;
            }
        }
        return false;
    }
}
