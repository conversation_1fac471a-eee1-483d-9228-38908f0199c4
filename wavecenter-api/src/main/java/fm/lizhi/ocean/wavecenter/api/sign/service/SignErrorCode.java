package fm.lizhi.ocean.wavecenter.api.sign.service;

/**
 * 签约通用异常码
 * <AUTHOR>
 * @date 2024/10/14 17:55
 */
public interface SignErrorCode {

    /**
     * 请求用户未完成平台实名认证
     */
    int REQ_USER_NO_PLATFORM_VERIFY = 2060001;

    /**
     * 请求用户未完成上上签实名认证
     */
    int REQ_USER_NO_SIGN_VERIFY = 2060002;

    /**
     * 对方用户未完成平台实名认证
     */
    int TAR_USER_NO_PLATFORM_VERIFY = 2060003;

    /**
     * 对方用户未完成上上签实名认证
     */
    int TAR_USER_NO_SIGN_VERIFY = 2060004;

    /**
     * 对方用户性别信息未设置
     */
    int TAR_USER_GENDER_NO_SETTING = 2060005;

    /**
     * 请求用户性别信息未设置
     */
    int REQ_USER_GENDER_NO_SETTING = 2060006;

    /**
     * 请求用户媒体信息未设置
     */
    int REQ_USER_MEDIA_INFO_NO_EXIST = 2060007;

    /**
     * 同实名账号已加入其他家族
     */
    int USER_SAME_ID_ACCOUNT_JOIN_FAMILY = 2060008;

    /**
     * 同实名其他账号处于冷冻期
     */
    int USER_SAME_ID_ACCOUNT_FREEZE = 2060009;

    /**
     * 陪玩已签约
     */
    int PLAYER_SIGNED = 2060012;

    /**
     * 未完成主播中心认证
     */
    int PLAYER_CENTER_AUTH_NULL = 2060013;

}
