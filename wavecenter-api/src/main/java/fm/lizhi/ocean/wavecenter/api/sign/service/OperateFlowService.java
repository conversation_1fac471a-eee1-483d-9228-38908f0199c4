package fm.lizhi.ocean.wavecenter.api.sign.service;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.sign.constant.SignFlowStatusEnum;

/**
 * 流程操作接口
 * <AUTHOR>
 * @date 2024/10/10 15:20
 */
public interface OperateFlowService {

    /**
     * 修改状态
     * @param appId
     * @param flowId
     * @param status
     * @return
     */
    Result<Void> changeStatus(int appId, long flowId, SignFlowStatusEnum status);

}
