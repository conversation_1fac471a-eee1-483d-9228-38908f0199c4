package fm.lizhi.ocean.wavecenter.api.activitycenter.service;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityReportDataDetailBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityReportDataGiftBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityReportDataPlayerBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityReportDataSummaryBean;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;

import java.util.List;

/**
 * 活动数据
 * <AUTHOR>
 */
public interface ActivityReportDataService {


    /**
     * 活动数据汇总
     */
    Result<ActivityReportDataSummaryBean> getReportSummary(Long activityId, int appId);


    /**
     * 活动数据趋势图
     */
    Result<List<ActivityReportDataDetailBean>> getReportDetail(Long activityId, int appId);


    /**
     * 活动数据主播表现
     */
    Result<PageBean<ActivityReportDataPlayerBean>> pageReportPlayer(Long activityId, int appId, int pageNo, int pageSize);


    /**
     * 活动数据送礼明细
     */
    Result<PageBean<ActivityReportDataGiftBean>> pageReportGift(Long activityId, int appId, int pageNo, int pageSize);


    /**
     * 获取活动数据汇总失败
     */
    int GET_REPORT_SUMMARY_FAIL = 2150001;


    /**
     * 获取活动数据趋势图失败
     */
    int GET_REPORT_DETAIL_FAIL = 21500101;


    /**
     * 获取活动数据主播表现失败
     */
    int PAGE_REPORT_PLAYER_FAIL = 21500201;


    /**
     * 获取活动数据送礼明细失败
     */
    int PAGE_REPORT_GIFT_FAIL = 21500301;


}
