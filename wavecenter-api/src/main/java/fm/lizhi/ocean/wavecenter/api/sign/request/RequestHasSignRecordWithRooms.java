package fm.lizhi.ocean.wavecenter.api.sign.request;

import fm.lizhi.ocean.wavecenter.api.common.annotation.AppEnumId;
import fm.lizhi.ocean.wavecenter.api.common.bean.IContextRequest;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/5 16:16
 */
@Data
@Accessors(chain = true)
public class RequestHasSignRecordWithRooms implements IContextRequest {

    @AppEnumId
    @NotNull(message = "appId is null")
    private Integer appId;

    @NotNull(message = "playerId is null")
    private Long playerId;

    @NotEmpty(message = "njIds is empty")
    private List<Long> njIds;

    @Override
    public Integer foundIdAppId() {
        return this.appId;
    }
}
