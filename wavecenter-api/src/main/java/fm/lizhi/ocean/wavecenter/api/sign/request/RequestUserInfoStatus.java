package fm.lizhi.ocean.wavecenter.api.sign.request;

import fm.lizhi.ocean.wavecenter.api.common.util.ApiAssert;
import lombok.Builder;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/10/23 10:17
 */
@Getter
@Builder
public class RequestUserInfoStatus {

    private Integer appId;

    private Long userId;

    public static class RequestUserInfoStatusBuilder {
        public RequestUserInfoStatus build() {
            ApiAssert.notNull(appId, "appId is null");
            ApiAssert.notNull(userId, "userId is null");
            return new RequestUserInfoStatus(appId, userId);
        }
    }

}
