package fm.lizhi.ocean.wavecenter.api.sign.response;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2024/10/24 15:01
 */
@Data
@Accessors(chain = true)
public class ResponseFamilyInviteAdmin implements ResponseSignResult{

    private Integer code = 0;

    private String msg;

    private String signId;

    private String contractUrl;

    private Long contractId;

    @Override
    public int getResultCode() {
        return this.code;
    }

    @Override
    public String getResultMsg() {
        return this.msg;
    }




}
