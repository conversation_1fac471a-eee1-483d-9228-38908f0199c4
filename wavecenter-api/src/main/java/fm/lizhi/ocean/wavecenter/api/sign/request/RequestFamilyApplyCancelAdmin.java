package fm.lizhi.ocean.wavecenter.api.sign.request;

import fm.lizhi.ocean.wavecenter.api.common.util.ApiAssert;
import lombok.Builder;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/10/25 19:49
 */
@Getter
@Builder
public class RequestFamilyApplyCancelAdmin {

    private Integer appId;

    private Long contractId;

    private Long curUserId;

    private Long familyId;

    private Long targetUserId;

    public static class RequestFamilyApplyCancelAdminBuilder {
        public RequestFamilyApplyCancelAdmin build() {
            ApiAssert.notNull(appId, "appId is null");
            ApiAssert.notNull(contractId, "contractId is null");
            ApiAssert.notNull(curUserId, "curUserId is null");
            ApiAssert.notNull(familyId, "familyId is null");
            ApiAssert.notNull(targetUserId, "targetUserId is null");
            return new RequestFamilyApplyCancelAdmin(appId, contractId, curUserId, familyId, targetUserId);
        }
    }

}
