package fm.lizhi.ocean.wavecenter.api.live.bean;


import fm.lizhi.ocean.wavecenter.api.user.bean.UserBean;
import lombok.Data;

import java.util.List;

@Data
public class RoomHourStatsRes implements IDetailList<RoomHourDetail, RoomHourStats>{


    private UserBean player;

    /**
     * 合计
     */
    private RoomHourStats stats;

    /**
     * 明细
     */
    private List<RoomHourDetail> detail;

    @Override
    public List<RoomHourDetail> foundDetail() {
        return this.detail;
    }

    @Override
    public RoomHourStats foundStats() {
        return this.stats;
    }
}
