package fm.lizhi.ocean.wavecenter.api.activitycenter.request;

import fm.lizhi.ocean.wavecenter.api.common.annotation.AppEnumId;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class RequestUserCancelActivity {

    /**
     * 活动ID
     */
    @NotNull(message = "活动ID不能为空")
    private Long activityId;

    /**
     * 取消原因
     */
    private String reason;

    /**
     * 操作的用户ID
     */
    @NotNull(message = "操作者不能为空")
    private Long operateUserId;

    @AppEnumId
    private Integer appId;

    /**
     * 版本号
     */
    @NotNull(message = "版本号不能为空")
    private Integer version;

}
