package fm.lizhi.ocean.wavecenter.api.award.family.response;

import fm.lizhi.ocean.wavecenter.api.award.family.bean.GetFamilyLevelAwardItemBean;
import lombok.Data;

import java.util.List;

/**
 * 获取公会等级奖励规则的响应
 */
@Data
public class ResponseGetFamilyLevelAwardRule {

    /**
     * 规则id
     */
    private Long id;

    /**
     * 公会等级id
     */
    private Long levelId;

    /**
     * 公会等级名称
     */
    private String levelName;

    /**
     * 公会等级是否已删除
     */
    private Boolean levelDeleted;

    /**
     * 奖励条目列表
     */
    private List<GetFamilyLevelAwardItemBean> items;

    /**
     * 创建时间, 毫秒时间戳
     */
    private Long createTime;

    /**
     * 创建者
     */
    private String creator;

    /**
     * 修改时间, 毫秒时间戳
     */
    private Long modifyTime;

    /**
     * 修改者
     */
    private String modifier;
}
