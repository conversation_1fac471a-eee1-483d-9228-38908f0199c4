package fm.lizhi.ocean.wavecenter.api.sign.service;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.sign.bean.ActFlowBean;

/**
 * <AUTHOR>
 * @date 2024/10/9 11:42
 */
public interface QueryFlowService {

    /**
     * 查询管理员激活的解约流程
     * @param appId
     * @param curUserId
     * @return
     */
    Result<ActFlowBean> adminActCancelFlow(int appId, long curUserId);

    /**
     * 查询主播激活的解约流程
     * @param appId
     * @param curUserId
     * @return
     */
    Result<ActFlowBean> playerActCancelFlow(int appId, long curUserId);

    /**
     * 查询用户激活的签约流程
     * @param appId
     * @param curUserId
     * @return
     */
    Result<ActFlowBean> userActSignFlow(int appId, long curUserId);

}
