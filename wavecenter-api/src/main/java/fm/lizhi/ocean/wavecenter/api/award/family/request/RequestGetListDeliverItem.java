package fm.lizhi.ocean.wavecenter.api.award.family.request;

import fm.lizhi.ocean.wavecenter.api.common.annotation.AppEnumId;
import fm.lizhi.ocean.wavecenter.api.common.bean.IContextRequest;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2025/3/26 18:28
 */
@Data
@Accessors(chain = true)
public class RequestGetListDeliverItem implements IContextRequest {

    @NotNull(message = "appId is null")
    @AppEnumId
    private Integer appId;

    @NotNull(message = "recordId is null")
    private Long recordId;

    @Override
    public Integer foundIdAppId() {
        return this.appId;
    }
}
