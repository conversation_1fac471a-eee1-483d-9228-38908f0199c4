package fm.lizhi.ocean.wavecenter.api.sign.response;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2024/10/23 20:52
 */
@Data
@Accessors(chain = true)
public class ResponseFamilyDoSign implements ResponseSignResult{

    private Integer code = 0;

    private String msg;

    /**
     * 合同URL
     */
    private String contractUrl;

    @Override
    public int getResultCode() {
        return this.code;
    }

    @Override
    public String getResultMsg() {
        return this.msg;
    }

}
