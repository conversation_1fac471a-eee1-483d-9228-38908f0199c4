package fm.lizhi.ocean.wavecenter.api.sign.bean;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 家族和管理员合同信息
 * <AUTHOR>
 * @date 2024/10/9 14:37
 */
@Data
@Accessors(chain = true)
public class FamilyAndNjContractBean {

    private Long id;

    /**
     * 合同ID
     */
    private Long contractId;

    /**
     * 签约ID
     */
    private String signId;

    /**
     * 状态
     * @see fm.lizhi.ocean.wavecenter.api.sign.constant.SignRelationEnum
     */
    private String status;

    /**
     * 类型 SIGN签约 CANCEL解约
     */
    private String type;

    /**
     * 发起人角色
     * ADMIN=管理员发起, PLAYER=主播发起, FAMILY=家族发起
     */
    private String createUser;

    /**
     * 主播用户ID
     */
    private Long njUserId;

    /**
     * 家族ID
     */
    private Long familyId;

    /**
     * 管理员签署状态
     * @see fm.lizhi.ocean.wavecenter.api.sign.constant.UserSignStatusEnum
     */
    private String njSignStatus;

    /**
     * 家族签署状态
     * @see fm.lizhi.ocean.wavecenter.api.sign.constant.UserSignStatusEnum
     */
    private String familySignStatus;


    /**
     * 签署截止时间
     */
    private Date signDeadline;

    /**
     * 签约完成时间
     */
    private Date signFinishTime;

    /**
     * 签约时长 月
     */
    private Integer duration;

    /**
     * 合同开始时间
     */
    private Date beginTime;

    /**
     * 合同到期时间
     */
    private Date expireTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date modifyTime;

    /**
     * 终止/解约合同时间
     */
    private Date stopTime;

}
