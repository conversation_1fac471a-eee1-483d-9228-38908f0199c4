package fm.lizhi.ocean.wavecenter.api.user.bean;

import fm.lizhi.ocean.wavecenter.api.common.util.ApiAssert;
import lombok.Builder;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/4/12 14:54
 */
@Getter
@Builder
public class PhoneLoginParamBean {

    private Integer appId;
    private String ip;
    private String authCode;
    private String phone;
    private String deviceId;
    private String deviceType;
    private String clientVersion;

    public static class PhoneLoginParamBeanBuilder{

        public PhoneLoginParamBean build(){

            ApiAssert.notNull(appId, "appId can not be null");
            ApiAssert.hasText(ip, "ip can not be null");
            ApiAssert.hasText(authCode, "authCode can not be null");
            ApiAssert.hasText(phone, "phone can not be null");
            ApiAssert.hasText(deviceId, "adviceId can not be null");

            return new PhoneLoginParamBean(appId, ip, authCode, phone, deviceId, deviceType, clientVersion);
        }

    }
}
