package fm.lizhi.ocean.wavecenter.api.live.bean;


import fm.lizhi.ocean.wavecenter.api.user.bean.RoomBean;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class GuildRoomDayStatsRes implements IDetailList<GuildRoomDayDetail, GuildRoomDayStats>{


    private RoomBean room;

    /**
     * 合计
     */
    private GuildRoomDayStats stats;

    /**
     * 明细
     */
    private List<GuildRoomDayDetail> detail;

    @Override
    public List<GuildRoomDayDetail> foundDetail() {
        return this.detail;
    }

    @Override
    public GuildRoomDayStats foundStats() {
        return this.stats;
    }
}
