package fm.lizhi.ocean.wavecenter.api.sign.response;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2024/10/23 14:40
 */
@Data
@Accessors(chain = true)
public class ResponseUserApplyAdmin implements ResponseSignResult{

    /**
     * 签约ID
     */
    private String signId;

    /**
     * 合同ID
     */
    private Long contractId;

    /**
     * 合同URL
     */
    private String contractUrl;

    /**
     * 成功为0
     */
    private Integer code;

    /**
     * 消息, 一般是异常消息
     */
    private String msg;

    @Override
    public int getResultCode() {
        return this.code;
    }

    @Override
    public String getResultMsg() {
        return this.msg;
    }

}
