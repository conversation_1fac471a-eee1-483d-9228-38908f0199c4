package fm.lizhi.ocean.wavecenter.api.grow.level.service;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.grow.level.bean.FamilyLastLevelBean;
import fm.lizhi.ocean.wavecenter.api.grow.level.request.RequestGetFamilyLastLevel;

import javax.validation.Valid;

/**
 * 公会等级服务
 * <AUTHOR>
 * @date 2025/3/20 15:13
 */
public interface FamilyLevelService {

    /**
     * 查询公会最近周期的等级
     * @param request
     * @return
     */
    Result<FamilyLastLevelBean> getFamilyLastLevel(@Valid RequestGetFamilyLastLevel request);

    /**
     * 公会最近周期的等级不存在
     */
    int GET_FAMILY_LAST_LEVEL_NOT_EXIST = 2310001;

}
