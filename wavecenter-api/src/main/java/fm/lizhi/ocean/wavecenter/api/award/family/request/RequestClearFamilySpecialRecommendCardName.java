package fm.lizhi.ocean.wavecenter.api.award.family.request;

import fm.lizhi.ocean.wavecenter.api.common.annotation.AppEnumId;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 清空公会特殊推荐卡名单的请求
 */
@Data
public class RequestClearFamilySpecialRecommendCardName {

    /**
     * 应用id
     */
    @NotNull(message = "appId不能为空")
    @AppEnumId
    private Integer appId;

    /**
     * 操作人
     */
    @NotNull(message = "操作人不能为空")
    private String operator;
}
