package fm.lizhi.ocean.wavecenter.api.sign.response;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2024/10/25 16:37
 */
@Data
@Accessors(chain = true)
public class ResponseUserDoSign implements ResponseSignResult{

    /**
     * 成功为0
     */
    private Integer code = 0;

    /**
     * 消息, 一般是异常消息
     */
    private String msg;

    private String contractUrl;

    @Override
    public int getResultCode() {
        return this.code;
    }

    @Override
    public String getResultMsg() {
        return this.msg;
    }

}
