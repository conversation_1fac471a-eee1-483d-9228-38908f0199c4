package fm.lizhi.ocean.wavecenter.api.user.request;

import fm.lizhi.ocean.wavecenter.api.common.util.ApiAssert;
import lombok.Builder;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/10/21 14:40
 */
@Getter
@Builder
public class RequestSearchUser {

    private Integer appId;

    /**
     * 用户波段号
     */
    private String band;

    public static class RequestSearchUserBuilder {

        public RequestSearchUser build() {
            ApiAssert.notNull(appId, "appId is required");
            return new RequestSearchUser(appId, band);
        }

    }

}
