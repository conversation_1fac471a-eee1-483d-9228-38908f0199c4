package fm.lizhi.ocean.wavecenter.api.sign.request;

import fm.lizhi.ocean.wavecenter.api.common.util.ApiAssert;
import lombok.Builder;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/10/28 19:08
 */
@Getter
@Builder
public class RequestGetContractViewUrl {

    private Integer appId;

    private Long curUserId;

    private Long contractId;

    public static class RequestGetContractViewUrlBuilder {
        public RequestGetContractViewUrl build() {
            ApiAssert.notNull(appId, "appId is null");
            ApiAssert.notNull(curUserId, "curUserId is null");
            ApiAssert.notNull(contractId, "contractId is null");
            return new RequestGetContractViewUrl(appId, curUserId, contractId);
        }
    }

}
