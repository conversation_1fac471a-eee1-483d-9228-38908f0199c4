package fm.lizhi.ocean.wavecenter.api.activitycenter.bean;

import fm.lizhi.ocean.wavecenter.api.user.bean.UserBean;
import lombok.Data;

/**
 * 活动简单信息
 */
@Data
public class ActivitySimpleInfoBean {

    /**
     * 活动ID
     */
    private Long id;

    /**
     * 活动名称
     */
    private String name;

    /**
     * 活动报名类型
     */
    private Integer applyType;

    /**
     * 活动审核状态
     */
    private Integer auditStatus;

    /**
     * 活动状态
     */
    private Integer activityStatus;

    /**
     * 活动联系人
     */
    private String contact;

    /**
     * 活动开始时间
     */
    private Long startTime;

    /**
     * 活动结束时间
     */
    private Long endTime;

    /**
     * 活动联系方式号码
     */
    private String contactNumber;

    /**
     * 主持信息
     */
    private UserBean hostInfo;

    /**
     * 审核原因
     */
    private String auditReason;

    /**
     * 活动分类名称
     */
    private String className;

    /**
     * 大分类名称
     */
    private String bigClassName;

    /**
     * 分类ID
     */
    private Long classId;

    /**
     * 活动申请时间
     */
    private Long applyTime;

    /**
     * 等级名称
     */
    private String levelName;

    /**
     * 模板名称
     */
    private String templateName;

    /**
     * 申请用户信息
     */
    private UserBean applyUserInfo;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 主持ID
     */
    private Long hostId;
}
