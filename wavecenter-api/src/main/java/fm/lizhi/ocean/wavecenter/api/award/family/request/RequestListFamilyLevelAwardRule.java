package fm.lizhi.ocean.wavecenter.api.award.family.request;

import fm.lizhi.ocean.wavecenter.api.common.annotation.AppEnumId;
import fm.lizhi.ocean.wavecenter.api.common.bean.IContextRequest;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 列出公会等级奖励规则的请求
 */
@Data
public class RequestListFamilyLevelAwardRule implements IContextRequest {

    /**
     * 应用id
     */
    @NotNull(message = "appId不能为空")
    @AppEnumId
    private Integer appId;

    @Override
    public Integer foundIdAppId() {
        return this.appId;
    }
}
