package fm.lizhi.ocean.wavecenter.api.sign.bean;

import fm.lizhi.ocean.wavecenter.api.common.util.ApiAssert;
import lombok.Builder;
import lombok.Getter;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/13 15:07
 */
@Getter
@Builder
public class GMSSignRoomPageListReq {

    private Integer appId;

    private Long familyId;

    /**
     * 签约厅ID
     */
    private Long roomId;

    /**
     * 签约时间-开始
     */
    private Date signStartDate;

    /**
     * 签约时间-结束
     */
    private Date signEndDate;

    /**
     * 到期时间-开始
     */
    private Date expireStartDate;

    /**
     * 到期时间-结束
     */
    private Date expireEndDate;

    /**
     * 解约时间-开始
     */
    private Date stopStartDate;

    /**
     * 解约时间-结束
     */
    private Date stopEndDate;

    /**
     * 签约状态
     * 1=已签约
     * 0=已解约
     */
    private Integer signStatus;

    private Integer pageNo;

    private Integer pageSize;

    /**
     * 厅数据范围
     * @since 1.4.0
     */
    private List<Long> roomIds;

    public static class GMSSignRoomPageListReqBuilder{

        public GMSSignRoomPageListReq build(){
            ApiAssert.notNull(appId, "appId is null");
            ApiAssert.notNull(familyId, "familyId is null");
            if (pageNo == null || pageNo < 0) {
                pageNo = 1;
            }
            if (pageSize == null || pageSize < 0) {
                pageSize = 10;
            }

            return new GMSSignRoomPageListReq(appId, familyId, roomId
                    , signStartDate, signEndDate
                    , expireStartDate, expireEndDate
                    , stopStartDate, stopEndDate
                    , signStatus, pageNo, pageSize
                    , roomIds
            );
        }
    }

}
