package fm.lizhi.ocean.wavecenter.api.background.activitycenter.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

import fm.lizhi.ocean.wavecenter.api.common.annotation.AppEnumId;

/**
 * 删除活动大类请求
 */
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class RequestDeleteActivityBigClass {

    /**
     * 活动大类ID
     */
    @NotNull(message = "活动大类id不能为空")
    private Long id;

    /**
     * 操作人
     */
    @NotNull(message = "操作人不能为空")
    private String operator;

    /**
     * 应用ID
     */
    @AppEnumId
    private Integer appId;
}
