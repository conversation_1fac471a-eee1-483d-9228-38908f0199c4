package fm.lizhi.ocean.wavecenter.api.user.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2024/4/20 13:32
 */
@Data
@Accessors(chain = true)
public class FamilyAuthBean {

    /**
     * 公会id
     */
    private Long familyId;

    /**
     * 公会名称
     */
    private String familyName;

    /**
     * 1=认证通过，2=未认证
     */
    private Integer authType;

    /**
     * 认证企业
     */
    private String authCompany;

    /**
     * 家族长波段号
     */
    private String familyUserBand;

    /**
     * 公会等级ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long familyLevelId;

    /**
     * 公会头像url
     */
    private String familyPhotoUrl;

    /**
     * 家族长名称
     */
    private String familyUserName;

    /**
     * 公会等级信息
     */
    private FamilyLevelInfoBean familyLevelInfo;

}
