package fm.lizhi.ocean.wavecenter.api.award.family.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 公会奖励资源类型
 */
@AllArgsConstructor
@Getter
public enum FamilyAwardResourceTypeEnum {

    /**
     * 等级规则: 推荐卡
     */
    RECOMMEND_CARD(1, FamilyAwardTypeEnum.LEVEL, FamilyAwardResourceDeliverTypeEnum.RECOMMEND_CARD),
    /**
     * 等级规则: 座驾
     */
    VEHICLE(2, FamilyAwardTypeEnum.LEVEL, FamilyAwardResourceDeliverTypeEnum.VEHICLE),
    /**
     * 等级规则: 勋章
     */
    MEDAL(3, FamilyAwardTypeEnum.LEVEL, FamilyAwardResourceDeliverTypeEnum.MEDAL),
    /**
     * 等级规则: 短号
     */
    SHORT_NUMBER(4, FamilyAwardTypeEnum.LEVEL, FamilyAwardResourceDeliverTypeEnum.SHORT_NUMBER),
    /**
     * PP等级推荐卡
     */
    PP_LEVEL_RECOMMEND_CARD(101, FamilyAwardTypeEnum.OTHER, FamilyAwardResourceDeliverTypeEnum.RECOMMEND_CARD),
    /**
     * PP流水增长推荐卡
     */
    PP_FLOW_GROWTH_RECOMMEND_CARD(102, FamilyAwardTypeEnum.OTHER, FamilyAwardResourceDeliverTypeEnum.RECOMMEND_CARD),
    /**
     * PP新厅留存推荐卡
     */
    PP_NEW_ROOM_RETAIN_RECOMMEND_CARD(103, FamilyAwardTypeEnum.OTHER, FamilyAwardResourceDeliverTypeEnum.RECOMMEND_CARD),
    /**
     * PP零流失厅推荐卡
     */
    PP_ZERO_LOST_ROOM_RECOMMEND_CARD(104, FamilyAwardTypeEnum.OTHER, FamilyAwardResourceDeliverTypeEnum.RECOMMEND_CARD),
    /**
     * PP特殊推荐卡
     */
    PP_SPECIAL_RECOMMEND_CARD(105, FamilyAwardTypeEnum.OTHER, FamilyAwardResourceDeliverTypeEnum.RECOMMEND_CARD),
    /**
     * PP等级初始新厅名额
     */
    PP_LEVEL_NEW_ROOM(501, FamilyAwardTypeEnum.OTHER, FamilyAwardResourceDeliverTypeEnum.NEW_ROOM),
    /**
     * PP流水涨幅新厅名额
     */
    PP_FLOW_GROWTH_NEW_ROOM(502, FamilyAwardTypeEnum.OTHER, FamilyAwardResourceDeliverTypeEnum.NEW_ROOM),
    /**
     * PP流失厅数新厅名额
     */
    PP_LOST_ROOM_NEW_ROOM(503, FamilyAwardTypeEnum.OTHER, FamilyAwardResourceDeliverTypeEnum.NEW_ROOM),
    /**
     * PP新厅留存新厅名额
     */
    PP_NEW_ROOM_RETAIN_NEW_ROOM(504, FamilyAwardTypeEnum.OTHER, FamilyAwardResourceDeliverTypeEnum.NEW_ROOM),
    ;

    private final Integer value;

    private final FamilyAwardTypeEnum awardType;

    private final FamilyAwardResourceDeliverTypeEnum deliverType;

    public static FamilyAwardResourceTypeEnum fromValue(Integer value) {
        for (FamilyAwardResourceTypeEnum type : FamilyAwardResourceTypeEnum.values()) {
            if (type.getValue().equals(value)) {
                return type;
            }
        }
        return null;
    }
}
