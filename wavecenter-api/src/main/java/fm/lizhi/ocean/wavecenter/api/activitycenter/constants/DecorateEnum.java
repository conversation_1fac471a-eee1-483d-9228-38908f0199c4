package fm.lizhi.ocean.wavecenter.api.activitycenter.constants;

import lombok.Getter;

/**
 * 装扮枚举
 *
 * <AUTHOR>
 */
@Getter
public enum DecorateEnum implements TypeNameProvider {

    /**
     * 头像框
     */
    AVATAR(1, "头像框"),

    /**
     * 背景
     */
    BACKGROUND(2, "房间背景"),

    /**
     * 勋章
     */
    MEDAL(4, "勋章"),

    /**
     * 官方认证
     */
    USER_GLORY(5, "官方认证"),

    ;


    private final Integer type;

    private final String name;

    DecorateEnum(int type, String name) {
        this.type = type;
        this.name = name;
    }

    public static String getDecorateName(Integer type) {
        for (DecorateEnum decorateEnum : DecorateEnum.values()) {
            if (decorateEnum.getType().equals(type)) {
                return decorateEnum.getName();
            }
        }
        return "";
    }

}
