package fm.lizhi.ocean.wavecenter.api.live.bean;


import fm.lizhi.ocean.wavecenter.api.common.util.ApiAssert;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class RoomDayCalendarReq {

    private Integer appId;
    private Long roomId;
    private Date startDate;
    private Date endDate;

    public static class RoomDayCalendarReqBuilder {
        public RoomDayCalendarReq build() {
            ApiAssert.notNull(appId, "appId is required");
            ApiAssert.notNull(startDate, "startDate is required");
            ApiAssert.notNull(endDate, "endDate is required");
            return new RoomDayCalendarReq(appId, roomId,startDate, endDate);
        }
    }
}
