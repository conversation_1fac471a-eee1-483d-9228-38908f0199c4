package fm.lizhi.ocean.wavecenter.api.permissions.service;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.permissions.bean.GetUserPermissionBean;
import fm.lizhi.ocean.wavecenter.api.permissions.request.RequestGetUserRoomDataScope;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/27 15:11
 */
public interface UserPermissionService {

    Result<GetUserPermissionBean> getUserPermission(int appId, long userId, String deviceId);

    Result<String> getUserRoleCode(int appId, long userId);

    /**
     * 查询用户的厅数据权限范围
     * @param request
     * @return
     */
    Result<List<Long>> getUserRoomDataScope(RequestGetUserRoomDataScope request);

}
