package fm.lizhi.ocean.wavecenter.api.sign.service;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.sign.bean.*;
import fm.lizhi.ocean.wavecenter.api.sign.request.RequestPlayerHallInfo;
import fm.lizhi.ocean.wavecenter.api.sign.response.ResponsePlayerHallInfo;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/**
 * 公会管理
 *
 * <AUTHOR>
 * @date 2024/6/13 14:51
 */
public interface GuildManageService {

    /**
     * 签约厅列表
     *
     * @return
     */
    Result<PageBean<RoomSignInfoBean>> signRoomPageList(GMSSignRoomPageListReq req);

    /**
     * 查询签约主播列表
     *
     * @param req
     * @return
     */
    Result<PageBean<SignPlayerInfoBean>> signPlayerPageList(GMSSignPlayerPageListReq req);

    /**
     * 查询公会信息
     *
     * @param req
     * @return
     */
    Result<GuildFullInfoBean> getFullInfo(GetFullInfoReq req);

    /**
     * 根据陪玩ID查询出厅主信息和厅主签约陪玩列表
     *
     * @param req 请求参数
     * @return 结果
     */
    Result<ResponsePlayerHallInfo> playerHallInfo(@NotNull @Valid RequestPlayerHallInfo req);

    /**
     * 用户未签约厅主
     */
    int PLAYER_HALL_INFO_HALL_NOT_EXIST = 22000000;

}
