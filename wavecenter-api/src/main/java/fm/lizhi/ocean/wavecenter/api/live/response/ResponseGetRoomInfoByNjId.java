package fm.lizhi.ocean.wavecenter.api.live.response;

import lombok.Data;

/**
 * 通过厅主ID查询直播房间信息的响应
 */
@Data
public class ResponseGetRoomInfoByNjId {

    /**
     * 直播间ID
     */
    private Long id;

    /**
     * 电台ID
     */
    private Long radioId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 直播间名称
     */
    private String name;

    /**
     * 状态, 0:正常, 1:封禁
     */
    private Integer status;
}
