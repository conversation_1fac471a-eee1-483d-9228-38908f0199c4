package fm.lizhi.ocean.wavecenter.api.activitycenter.bean;

import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityApplyTypeEnum;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageParamBean;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * 查询活动日历列表
 */
@Data
@Builder
public class RequestQueryActivityListBean {

    /**
     * 分页参数
     */
    private PageParamBean pageParam;


    private Integer appId;


    /**
     * 最大活动开始时间
     */
    private Long maxStartTime;

    /**
     * 分类ID
     */
    private Long classId;

    /**
     * 活动结束时间
     */
    private Long minStartTime;

    /**
     * 活动状态
     *
     * @see fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityStatusEnum
     */
    private Integer activityStatus;

    /**
     * 活动名
     */
    private String name;

    /**
     * 厅主ID
     */
    private Long njId;

    /**
     * 活动申请类型
     *
     * @see ActivityApplyTypeEnum
     */
    private Integer applyType;

    /**
     * 活动审核状态
     *
     * @see fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityAuditStatusEnum
     */
    private List<Integer> auditStatus;

    /**
     * 活动申请开始时间
     */
    private Long applyStartTime;

    /**
     * 活动申请结束时间
     */
    private Long applyEndTime;

    /**
     * 模板名称
     */
    private String templateName;

    /**
     * 等级ID
     */
    private Long levelId;

    /**
     * 活动ID
     */
    private Long activityId;

    
}

