package fm.lizhi.ocean.wavecenter.api.home.service;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.home.request.RequestGetGuildKeyDataSummary;
import fm.lizhi.ocean.wavecenter.api.home.request.RequestGetGuildKeyDataTrendChart;
import fm.lizhi.ocean.wavecenter.api.home.request.RequestGetGuildMarketMonitorSummary;
import fm.lizhi.ocean.wavecenter.api.home.request.RequestGetGuildMarketMonitorTrendChart;
import fm.lizhi.ocean.wavecenter.api.home.response.ResponseGuildKeyDataSummary;
import fm.lizhi.ocean.wavecenter.api.home.response.ResponseGuildKeyDataTrendChart;
import fm.lizhi.ocean.wavecenter.api.home.response.ResponseGuildMarketMonitorSummary;
import fm.lizhi.ocean.wavecenter.api.home.response.ResponseGuildMarketMonitorTrendChart;

import javax.validation.Valid;
import java.util.List;

/**
 * 公会首页
 * <AUTHOR>
 */
public interface GuildHomeService {


    /**
     * 关键数据汇总
     */
    Result<ResponseGuildKeyDataSummary> getKeyDataSummary(@Valid RequestGetGuildKeyDataSummary request);


    /**
     * 关键数据趋势图
     *
     */
    Result<List<ResponseGuildKeyDataTrendChart>> getKeyDataTrendChart(@Valid RequestGetGuildKeyDataTrendChart request);


    /**
     * 公会大盘监控
     */
    Result<ResponseGuildMarketMonitorSummary> getMarketMonitorSummary(@Valid RequestGetGuildMarketMonitorSummary request);

    /**
     * 公会大盘监控-趋势图
     */
    Result<List<ResponseGuildMarketMonitorTrendChart>> getMarketMonitorTrendChart(@Valid RequestGetGuildMarketMonitorTrendChart request);


}
