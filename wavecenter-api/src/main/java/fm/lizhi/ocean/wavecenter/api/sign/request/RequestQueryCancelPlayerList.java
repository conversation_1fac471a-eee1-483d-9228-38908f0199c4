package fm.lizhi.ocean.wavecenter.api.sign.request;

import fm.lizhi.ocean.wavecenter.api.common.util.ApiAssert;
import fm.lizhi.ocean.wavecenter.api.sign.constant.ContractTypeEnum;
import fm.lizhi.ocean.wavecenter.api.sign.constant.SignRelationEnum;
import lombok.Builder;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/10/17 11:36
 */
@Getter
@Builder
public class RequestQueryCancelPlayerList {

    private Integer appId;

    private Long familyId;

    private String userBand;

    private ContractTypeEnum type;

    private SignRelationEnum status;

    @Builder.Default
    private Integer pageNo = 1;

    @Builder.Default
    private Integer pageSize = 20;

    public static class RequestQueryCancelPlayerListBuilder{
        public RequestQueryCancelPlayerList build() {
            ApiAssert.notNull(appId, "appId is null");
            ApiAssert.notNull(familyId, "familyId is null");
            ApiAssert.notNull(type, "type is null");
            ApiAssert.notNull(status, "status is null");
            return new RequestQueryCancelPlayerList(appId, familyId, userBand, type, status, pageNo$value, pageSize$value);
        }
    }

}
