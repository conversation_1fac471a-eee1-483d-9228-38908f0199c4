package fm.lizhi.ocean.wavecenter.api.sign.service;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.sign.bean.CancelPlayerRecordBean;
import fm.lizhi.ocean.wavecenter.api.sign.bean.RoomSignRecordBean;
import fm.lizhi.ocean.wavecenter.api.sign.bean.TodoSignBean;
import fm.lizhi.ocean.wavecenter.api.sign.bean.TodoSignPlayerBean;
import fm.lizhi.ocean.wavecenter.api.sign.request.*;
import fm.lizhi.ocean.wavecenter.api.sign.response.*;

import java.util.List;

/**
 * 家族长签约
 * <AUTHOR>
 */
public interface SignFamilyService {

    /**
     * 审批解约
     * @param request
     * @return
     */
    Result<ResponseFamilyReviewCancel> reviewCancel(RequestFamilyReviewCancel request);

    /**
     * 待签约列表 家族长和厅主
     * @return
     */
    Result<List<TodoSignBean>> todoRoomList(RequestFamilyTodoRoomList request);

    /**
     * 查询待审批的陪玩列表
     * @param request
     * @return
     */
    Result<List<TodoSignPlayerBean>> todoPlayerList(RequestFamilyTodoPlayerList request);

    /**
     * 查询签约解约列表
     * @return
     */
    Result<PageBean<RoomSignRecordBean>> queryRoomSignList(RequestQueryRoomSignList request);

    /**
     * 查询陪玩解约申请列表
     * @param request
     * @return
     */
    Result<PageBean<CancelPlayerRecordBean>> queryCancelPlayerList(RequestQueryRoomSignList request);

    /**
     * 统计家族可开厅数
     * @param appId
     * @param familyId
     * @return
     */
    Result<Integer> countCanOpenRoomNum(int appId, long familyId);

    /**
     * 统计家族已签约厅数
     * @param appId
     * @param familyId
     * @return
     */
    Result<Integer> countSignRoomNum(int appId, long familyId);

    /**
     * 签署合同
     * @param request
     * @return
     */
    Result<ResponseFamilyDoSign> doSign(RequestFamilyDoSign request);

    /**
     * 邀请用户签约为管理员
     * @return
     */
    Result<ResponseFamilyInviteAdmin> inviteAdmin(RequestFamilyInviteAdmin request);

    /**
     * 申请解约管理员
     * @param request
     * @return
     */
    Result<ResponseFamilyApplyCancelAdmin> applyCancelAdmin(RequestFamilyApplyCancelAdmin request);

    /**
     * 家族解约管理员
     * @param request
     * @return
     */
    Result<ResponseFamilyDoCancelAdmin> doCancelAdmin(RequestFamilyDoCancelAdmin request);

    /**
     * 生成合同url失败
     */
    int DO_SIGN_URL_GEN_FAIL = 2040001;

    /**
     * 解约失败
     */
    int APPLY_CANCEL_ADMIN_FAIL = 2040002;

}
