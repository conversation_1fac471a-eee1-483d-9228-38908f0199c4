package fm.lizhi.ocean.wavecenter.api.user.bean;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/4/10 17:52
 */
@Data
@Accessors(chain = true)
public class FamilyBean {

    private Long id;

    /**
     * 家族名称
     */
    private String familyName;

    /**
     * 家族长ID
     */
    private Long userId;

    /**
     * C_FAMILY=PGC
     * P_FAMILY=UGC
     */
    private String familyType;

    /**
     * 家族简介
     */
    private String familyNote;

    /**
     * 家族头像
     */
    private String familyIconUrl;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 用户类型
     * USER(0, "普通用户"),
     * ADMIN(1, "管理员"),
     * PLAYER(2, "成员"),
     * FAMILY(3,"家族长");
     */
    private Integer userType;

}
