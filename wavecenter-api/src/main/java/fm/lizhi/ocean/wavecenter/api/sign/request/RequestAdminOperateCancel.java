package fm.lizhi.ocean.wavecenter.api.sign.request;

import fm.lizhi.ocean.wavecenter.api.sign.constant.OperateTypeEnum;
import lombok.Builder;
import lombok.Getter;
import lombok.NonNull;

/**
 * <AUTHOR>
 * @date 2024/10/14 14:54
 */
@Getter
@Builder
public class RequestAdminOperateCancel {

    @NonNull
    private Integer appId;

    /**
     * 管理员用户id
     */
    @NonNull
    private Long curUserId;

    /**
     * 签约记录ID
     */
    @NonNull
    private Long playerSignId;

    @NonNull
    private OperateTypeEnum operateType;

}
