package fm.lizhi.ocean.wavecenter.api.sign.request;

import fm.lizhi.ocean.wavecenter.api.common.util.ApiAssert;
import lombok.Builder;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/10/16 17:14
 */
@Getter
@Builder
public class RequestAdminTodoList {

    private Integer appId;

    private Long njId;

    public static class RequestAdminTodoListBuilder {
        public RequestAdminTodoList build() {
            ApiAssert.notNull(appId, "appId is null");
            ApiAssert.notNull(njId, "njId is null");
            return new RequestAdminTodoList(appId, njId);
        }
    }

}
