package fm.lizhi.ocean.wavecenter.api.user.bean;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2024/4/10 17:55
 */
@Data
@Accessors(chain = true)
public class UserInFamilyDetailBean {
    /**
     * 是否为家族长
     */
    private boolean isFamily;

    /**
     * 是否为厅主
     */
    private boolean isRoom;

    /**
     * 是否为主播
     */
    private boolean isPlayer;

    /**
     * 家族ID
     */
    private Long familyId;

    /**
     * 如果是厅主就是厅主ID
     */
    private Long njId;

    /**
     * 家族名称
     */
    private String familyName;
}
