package fm.lizhi.ocean.wavecenter.api.sign.request;

import fm.lizhi.ocean.wavecenter.api.common.util.ApiAssert;
import lombok.Builder;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/10/14 16:48
 */
@Getter
@Builder
public class RequestAdminApplyCancelPlayer {

    private Integer appId;

    private Long curUserId;

    /**
     * 签约记录ID
     */
    private Long playerSignId;

    protected RequestAdminApplyCancelPlayer(RequestAdminApplyCancelPlayerBuilder builder) {
        this.appId = builder.appId;
        this.curUserId = builder.curUserId;
        this.playerSignId = builder.playerSignId;
    }

    public static class RequestAdminApplyCancelPlayerBuilder{
        public RequestAdminApplyCancelPlayer build() {
            ApiAssert.notNull(appId, "appId can not be null");
            ApiAssert.notNull(curUserId, "curUserId can not be null");
            ApiAssert.notNull(playerSignId, "playerSignId can not be null");
            return new RequestAdminApplyCancelPlayer(this);
        }
    }

}
