package fm.lizhi.ocean.wavecenter.api.sign.constant;

/**
 * 签约关系枚举
 * <AUTHOR>
 * @date 2024/10/9 12:00
 */
public enum SignRelationEnum {

    /**
     * 未知状态
     */
    UNKNOWN("UNKNOWN")

    /**
     * 待平台审核
     */
    , WAIT_AUDIT("WAIT_AUDIT")

    /**
     * 平台审核不通过
     */
    , AUDIT_FAIL("AUDIT_FAIL")

    /**
     * 待签署
     */
    , WAIT_SIGN("WAIT_SIGN")

    /**
     * 签署中
     */
    , SIGNING("SIGNING")

    /**
     * 待对方签署
     */
    , WAIT_PARTNER_SIGN("WAIT_PARTNER_SIGN")

    /**
     * 签约完成
     */
    , SIGN_SUCCESS("SIGN_SUCCESS")

    /**
     * 签约失败
     */
    , SIGN_FAILED("SIGN_FAILED")

    /**
     * 已逾期
     */
    , OVERDUE("OVERDUE")

    /**
     * 对方逾期
     */
    , PARTNER_OVERDUE("PARTNER_OVERDUE")

    /**
     * 已解约
     */
    , STOP_CONTRACT("STOP_CONTRACT")

    /**
     * 撤销解约
     */
    , CANCEL_CONTRACT("CANCEL_CONTRACT")

    /**
     * 审核驳回
     */
    , REVIEW_REJECTED("REVIEW_REJECTED")

    /**
     * 签署确认
     */
    , SIGN_CONFIRM("SIGN_CONFIRM")

    /**
     * 已拒绝
     */
    , REJECT("REJECT")

    /**
     * 对方拒签
     */
    , PARTNER_REJECT("PARTNER_REJECT")

    /**
     * 待合同生效
     */
    , WAIT_CONTRACT_SYNC("WAIT_CONTRACT_SYNC")

    /**
     * 合同到期
     */
    , OVERDUE_CONTRACT("OVERDUE_CONTRACT")

    ;

    private String code;

    SignRelationEnum(String code) {
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public static SignRelationEnum fromCode(String code) {
        for (SignRelationEnum item : SignRelationEnum.values()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }
        return UNKNOWN;
    }
}
