package fm.lizhi.ocean.wavecenter.api.sign.response;

import fm.lizhi.ocean.wavecenter.api.sign.constant.IdentifyStatusEnum;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2024/10/23 10:19
 */
@Data
@Accessors(chain = true)
public class ResponseUserInfoStatus {

    /**
     * 用户信息状态
     * @see fm.lizhi.ocean.wavecenter.api.sign.constant.IdentifyStatusEnum
     */
    private String infoStatus = IdentifyStatusEnum.UNFINISHED.getCode();

    /**
     * 主播中心认证
     * @see fm.lizhi.ocean.wavecenter.api.sign.constant.IdentifyStatusEnum
     */
    private String playerCenterStatus = IdentifyStatusEnum.UNFINISHED.getCode();

}
