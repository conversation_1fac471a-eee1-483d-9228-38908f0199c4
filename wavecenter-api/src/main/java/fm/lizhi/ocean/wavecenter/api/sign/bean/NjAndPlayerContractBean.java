package fm.lizhi.ocean.wavecenter.api.sign.bean;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 管理员和主播签约信息
 * <AUTHOR>
 * @date 2024/10/9 20:29
 */
@Data
@Accessors(chain = true)
public class NjAndPlayerContractBean {

    /**
     * 合同ID
     */
    private Long contractId;

    /**
     * 解约记录的前合同ID
     */
    private Long oldContractId;

    /**
     * 状态
     * @see fm.lizhi.ocean.wavecenter.api.sign.constant.SignRelationEnum
     */
    private String status;

    /**
     * 类型 SIGN签约 CANCEL解约
     */
    private String type;

    /**
     * 发起人角色
     * ROOM=管理员发起, PLAYER=主播发起, FAMILY=家族发起
     */
    private String createUser;

    private Long njUserId;

    private Long playerUserId;

    /**
     * 签署截止时间
     */
    private Date signDeadline;

    /**
     * 签约生效开始时间
     */
    private Date startTime;

    /**
     * 签约生效结束时间
     */
    private Date endTime;

    /**
     * 解约时间
     */
    private Date stopTime;

    /**
     * 签约申请发起时间
     */
    private Date createTime;

}
