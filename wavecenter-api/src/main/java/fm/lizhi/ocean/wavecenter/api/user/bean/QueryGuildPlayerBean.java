package fm.lizhi.ocean.wavecenter.api.user.bean;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;

import java.util.List;

@AllArgsConstructor
@Getter
@Builder
public class QueryGuildPlayerBean {

    /**
     * 工会id
     */
    private long familyId;

    /**
     * appID
     */
    private int appId;

    /**
     * 签约状态
     * 1=已签约
     * 0=已解约
     * -1: 查询全部
     */
    private int status;

    /**
     * 厅范围
     * 如果有传，则只查询该厅列表下的主播
     */
    private List<Long> roomIds;

    public QueryGuildPlayerBean() {
        this.status = -1;
    }

}
