package fm.lizhi.ocean.wavecenter.api.live.bean;


import fm.lizhi.ocean.wavecenter.api.live.constants.CheckInStatus;
import fm.lizhi.ocean.wavecenter.api.live.constants.HostStatus;
import lombok.Data;

import java.util.Date;

@Data
public class RoomHourDetail implements IDetailEle{

    /**
     * 日的值 yyyy-MM-dd HH:mm-yyyy-MM-dd HH:mm
     */
    private Date time;

    private Date startDate;

    private Date endDate;


    private String income;

    private Long charm;

    /**
     * 主持档
     */
    private Integer hostCnt;

    /**
     * 打卡
     */
    private Integer checkStatus;

    private String remark;

    @Override
    public Date foundTime() {
        return this.time;
    }

    @Override
    public String foundIncome() {
        return this.income;
    }

    @Override
    public Integer foundCharm() {
        if (this.charm == null) {
            return 0;
        }
        return Math.toIntExact(this.charm);
    }

    @Override
    public Integer foundSeatOrder() {
        if (checkStatus == null) {
            return 0;
        }
        return checkStatus.equals(CheckInStatus.CHECKED.getValue()) ? 1 : 0;
    }

    @Override
    public Integer foundHostCnt() {
        if (hostCnt == null) {
            return 0;
        }
        return hostCnt.equals(HostStatus.HOST.getValue()) ? 1 : 0;
    }

    @Override
    public Integer foundCheckPlayerNumber() {
        if (checkStatus == null) {
            return 0;
        }
        return checkStatus.equals(CheckInStatus.CHECKED.getValue()) ? 1 : 0;
    }

}
