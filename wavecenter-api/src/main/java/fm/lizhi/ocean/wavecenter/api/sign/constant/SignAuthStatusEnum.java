package fm.lizhi.ocean.wavecenter.api.sign.constant;

/**
 * <AUTHOR>
 * @date 2024/10/24 17:35
 */
public enum SignAuthStatusEnum {

    UNKNOWN("UNKNOWN"),

    /**
     * 未认证
     */
    NOT_AUTH("NOT_AUTH"),
    /**
     * 审核中
     */
    IN_AUDIT("IN_AUDIT"),
    /**
     * 已认证
     */
    AUTO_AUTH_PASS("AUTO_AUTH_PASS"),
    /**
     * 认证不通过
     */
    AUTH_REJECT("AUTH_REJECT"),
    /**
     * 意愿性认证中
     */
    IN_WILL_AUTH("IN_WILL_AUTH"),
    /**
     * 认证中
     */
    IN_AUTH("IN_AUTH"),
    ;

    private final String code;

    SignAuthStatusEnum(String code) {
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public static SignAuthStatusEnum getByCode(String code){
        for (SignAuthStatusEnum value : SignAuthStatusEnum.values()) {
            if (value.getCode().equals(code)){
                return value;
            }
        }
        return UNKNOWN;
    }

}
