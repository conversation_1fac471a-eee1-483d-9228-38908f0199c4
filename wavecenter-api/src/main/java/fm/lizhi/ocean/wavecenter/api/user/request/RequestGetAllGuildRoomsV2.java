package fm.lizhi.ocean.wavecenter.api.user.request;

import fm.lizhi.ocean.wavecenter.api.common.annotation.AppEnumId;
import fm.lizhi.ocean.wavecenter.api.common.bean.IContextRequest;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/6 15:56
 */
@Data
@Accessors(chain = true)
public class RequestGetAllGuildRoomsV2 implements IContextRequest {

    @AppEnumId
    @NotNull(message = "appId is null")
    private Integer appId;

    @NotNull(message = "familyId is null")
    private Long familyId;

    private List<Long> roomIds;

    private Integer pageNo = 1;

    private Integer pageSize = 20;

    @Override
    public Integer foundIdAppId() {
        return this.appId;
    }
}
