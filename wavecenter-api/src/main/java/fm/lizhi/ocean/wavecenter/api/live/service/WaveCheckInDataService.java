package fm.lizhi.ocean.wavecenter.api.live.service;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.live.request.RequestGetCheckInPlayerStatistic;
import fm.lizhi.ocean.wavecenter.api.live.request.RequestGetCheckInPlayerSum;
import fm.lizhi.ocean.wavecenter.api.live.request.RequestGetCheckInRoomStatistic;
import fm.lizhi.ocean.wavecenter.api.live.request.RequestGetCheckInRoomSum;
import fm.lizhi.ocean.wavecenter.api.live.response.ResponseGetCheckInPlayerStatistic;
import fm.lizhi.ocean.wavecenter.api.live.response.ResponseGetCheckInPlayerSum;
import fm.lizhi.ocean.wavecenter.api.live.response.ResponseGetCheckInRoomStatistic;
import fm.lizhi.ocean.wavecenter.api.live.response.ResponseGetCheckInRoomSum;

import javax.validation.Valid;

/**
 * 麦序福利规则数据服务
 * <p>
 * 错误码以226开头, 详细规范参考
 * <a href="https://lizhi2021.feishu.cn/wiki/Ld0Rw5uvGiUimnkgez5cSVJ9n1c">错误码规范</a>
 * <p>
 * 通用错误码见{@link fm.lizhi.ocean.wavecenter.api.common.service.CommonService}
 */
public interface WaveCheckInDataService {

    /**
     * 获取麦序福利厅汇总
     *
     * @param req 获取麦序福利厅汇总请求
     * @return 麦序福利厅汇总
     */
    Result<ResponseGetCheckInRoomSum> getCheckInRoomSum(@Valid RequestGetCheckInRoomSum req);

    /**
     * 获取麦序福利厅明细统计
     *
     * @param req 获取麦序福利厅明细统计请求
     * @return 麦序福利厅明细统计
     */
    Result<ResponseGetCheckInRoomStatistic> getCheckInRoomStatistic(@Valid RequestGetCheckInRoomStatistic req);

    /**
     * 获取麦序福利主播汇总数据汇总
     *
     * @param req 获取麦序福利主播汇总数据的请求
     * @return 麦序福利主播汇总数据
     */
    Result<ResponseGetCheckInPlayerSum> getCheckInPlayerSum(@Valid RequestGetCheckInPlayerSum req);

    /**
     * 获取麦序福利主播明细统计
     *
     * @param req 获取麦序福利主播明细统计请求
     * @return 麦序福利主播明细统计
     */
    Result<ResponseGetCheckInPlayerStatistic> getCheckInPlayerStatistic(@Valid RequestGetCheckInPlayerStatistic req);

    // ------------------ getCheckInRoomSum ------------------
    // 见通用错误码

    // ------------------ getCheckInRoomStatistic ------------------
    // 见通用错误码

    // ------------------ getCheckInPlayerSum ------------------
    // 见通用错误码

    // ------------------ getCheckInPlayerStatistic ------------------
    // 见通用错误码
}
