package fm.lizhi.ocean.wavecenter.api.home.service;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.home.request.RequestGetRoomKeyDataSummary;
import fm.lizhi.ocean.wavecenter.api.home.request.RequestGetRoomKeyDataTrendChart;
import fm.lizhi.ocean.wavecenter.api.home.request.RequestGetRoomMsgAnalysisPerformance;
import fm.lizhi.ocean.wavecenter.api.home.response.ResponseRoomKeyDataSummary;
import fm.lizhi.ocean.wavecenter.api.home.response.ResponseRoomKeyDataTrendChart;
import fm.lizhi.ocean.wavecenter.api.home.response.ResponseRoomMsgAnalysisPerformance;

import javax.validation.Valid;
import java.util.List;

/**
 * 厅管理首页
 * <AUTHOR>
 */
public interface RoomHomeService {


    /**
     * 获取房间关键数据
     */
    Result<ResponseRoomKeyDataSummary> getRoomKeyDataSummary(@Valid RequestGetRoomKeyDataSummary request);

    /**
     * 获取房间关键数据趋势图
     */
    Result<List<ResponseRoomKeyDataTrendChart>> getRoomKeyDataTrendChart(@Valid RequestGetRoomKeyDataTrendChart request);

    /**
     * 获取私信拓客漏斗表现
     */
    Result<ResponseRoomMsgAnalysisPerformance> getRoomMsgAnalysisPerformance(@Valid RequestGetRoomMsgAnalysisPerformance request);

}
