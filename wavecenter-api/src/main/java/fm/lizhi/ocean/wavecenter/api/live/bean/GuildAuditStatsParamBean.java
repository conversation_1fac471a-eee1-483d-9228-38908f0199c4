package fm.lizhi.ocean.wavecenter.api.live.bean;


import fm.lizhi.ocean.wavecenter.api.common.constants.AuditMetricsEnum;
import fm.lizhi.ocean.wavecenter.api.common.constants.OrderType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class GuildAuditStatsParamBean {

    private int appId;

    private Long roomId;

    /**
     * 工会ID
     */
    private long familyId;

    private Date startTime;

    private Date endTime;

    private OrderType orderType;

    private AuditMetricsEnum orderMetrics;

    /**
     * 厅数据范围
     */
    private List<Long> roomIds;

}
