package fm.lizhi.ocean.wavecenter.base;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;

/**
 * <AUTHOR>
 * @date 2024/1/4 14:43
 */
public class RpcResult {

    private RpcResult() {
    }

    public static <T> Result<T> success(T data) {
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, data);
    }

    public static <T> Result<T> success() {
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, null);
    }

    public static <T> Result<T> fail(int rCode) {
        return new Result<>(rCode, null);
    }

    public static <T> Result<T> fail(int rCode, String message) {
        Result<T> result = new Result<>(rCode, null);
        result.setMessage(message);
        return result;
    }

    public static <T> boolean isFail(Result<T> result) {
        return result.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS;
    }

    public static <T> boolean noTarget(Result<T> result) {
        return result.target() == null;
    }

    public static <T> boolean noBusinessData(Result<T> result) {
        return isFail(result) || noTarget(result);
    }

    public static <T> boolean isSuccess(Result<T> result) {
        return result.rCode() == GeneralRCode.GENERAL_RCODE_SUCCESS;
    }

}
