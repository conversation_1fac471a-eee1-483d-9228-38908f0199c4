package fm.lizhi.ocean.wavecenter.domain.grow.entity;

/**
 * 成长等级
 * <AUTHOR>
 * @date 2025/3/18 17:37
 */
public interface Level extends Comparable<Level>{

    /**
     * 等级ID
     * @return
     */
    Long getId();

    /**
     * 业务线
     * @return
     */
    Integer getAppId();

    /**
     * 等级名称
     * @return
     */
    String getName();

    /**
     * 等级值
     * @return
     */
    Integer getLevelValue();

    /**
     * 最小经验值
     * 达到此等级所需要的最小经验值
     * @return
     */
    Integer getMinExp();

}
