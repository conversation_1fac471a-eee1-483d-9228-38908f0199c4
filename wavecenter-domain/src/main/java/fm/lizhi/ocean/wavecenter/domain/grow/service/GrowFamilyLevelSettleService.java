package fm.lizhi.ocean.wavecenter.domain.grow.service;

import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.ocean.wavecenter.domain.grow.entity.FamilyLevel;
import fm.lizhi.ocean.wavecenter.domain.grow.entity.Period;
import fm.lizhi.ocean.wavecenter.domain.grow.entity.organization.GrowFamily;
import fm.lizhi.ocean.wavecenter.domain.grow.repository.FamilyIncomeRepository;
import fm.lizhi.ocean.wavecenter.domain.grow.repository.FamilyLevelRepository;
import fm.lizhi.ocean.wavecenter.domain.grow.repository.GrowFamilyRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * 公会等级结算领域服务
 * <AUTHOR>
 * @date 2025/3/22 11:32
 */
@Slf4j
@Component
public class GrowFamilyLevelSettleService {

    private FamilyIncomeRepository familyIncomeRepository;

    private FamilyLevelRepository familyLevelRepository;

    private GrowFamilyRepository growFamilyRepository;

    @Autowired
    public GrowFamilyLevelSettleService(FamilyIncomeRepository familyIncomeRepository, FamilyLevelRepository familyLevelRepository, GrowFamilyRepository growFamilyRepository) {
        this.familyIncomeRepository = familyIncomeRepository;
        this.familyLevelRepository = familyLevelRepository;
        this.growFamilyRepository = growFamilyRepository;
    }

    /**
     * 结算周等级
     */
    public void settleWeekLevel(Integer appId, List<Long> familyIds, Date monDay, Date sunDay){
        List<FamilyLevel> levels = familyLevelRepository.getAppFamilyLevels(appId);
        if (CollectionUtils.isEmpty(levels)) {
            return;
        }

        log.info("settleWeekLevel levels={}", JsonUtil.dumps(levels));

        for (Long familyId : familyIds) {
            try {
                GrowFamily family = new GrowFamily(familyId, appId);
                Period period = new Period(monDay, sunDay);

                // 经验获取 周收入
                Integer familyWeekIncome = familyIncomeRepository.getFamilyWeekIncome(appId, familyId, monDay, sunDay);
                log.info("settleWeekLevel weekIncome. familyId={},monDay={},sunDay={},familyWeekIncome={}", familyId, monDay.getTime(), sunDay.getTime(), familyWeekIncome);
                family.addExp(familyWeekIncome, period);

                // 升级
                family.updateLevel(period, levels);
                growFamilyRepository.saveFamily(family, period);
            } catch (Exception e) {
                log.error("settleWeekLevel appId={},familyId={},monDay={},sunDay={} error:", appId, familyId, monDay, sunDay, e);
            }
        }
    }

    /**
     * 结算为指定等级
     * @param appId
     * @param familyId
     * @param monDay
     * @param sunDay
     * @param levelId
     */
    public void settleInLevel(Integer appId, Long familyId, Date monDay, Date sunDay, Long levelId){
        Optional<FamilyLevel> levelOp = familyLevelRepository.getLevel(appId, levelId);
        if (!levelOp.isPresent()) {
            return;
        }
        List<FamilyLevel> levels = familyLevelRepository.getAppFamilyLevels(appId);
        if (CollectionUtils.isEmpty(levels)) {
            return;
        }

        FamilyLevel level = levelOp.get();
        Period period = new Period(monDay, sunDay);
        GrowFamily growFamily = new GrowFamily(familyId, appId);

        growFamily.addExp(level.getMinExp(), period);
        growFamily.updateLevel(period, levels);

        growFamilyRepository.saveFamily(growFamily, period);
    }

}
