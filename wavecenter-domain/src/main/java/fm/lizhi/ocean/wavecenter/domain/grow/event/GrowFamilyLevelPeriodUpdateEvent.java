package fm.lizhi.ocean.wavecenter.domain.grow.event;

import fm.lizhi.ocean.wavecenter.domain.grow.entity.Level;
import fm.lizhi.ocean.wavecenter.domain.grow.entity.Period;
import fm.lizhi.ocean.wavecenter.domain.grow.entity.organization.Organization;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

import java.util.Date;

/**
 * 公会等级周结算事件
 * <AUTHOR>
 * @date 2025/3/18 20:26
 */
@Getter
public class GrowFamilyLevelPeriodUpdateEvent extends ApplicationEvent {

    /**
     * 公会
     */
    private Organization family;

    /**
     * 等级
     */
    private Level level;

    /**
     * 周期
     */
    private Period period;

    public GrowFamilyLevelPeriodUpdateEvent(Organization family, Level level, Period period) {
        super(new Date());
        this.family = family;
        this.level = level;
        this.period = period;
    }
}
