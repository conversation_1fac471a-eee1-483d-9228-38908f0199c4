package fm.lizhi.ocean.wavecenter.domain.grow.entity;

import lombok.Getter;
import org.jetbrains.annotations.NotNull;

/**
 * <AUTHOR>
 * @date 2025/3/19 19:20
 */
public abstract class AbstractLevel implements Level{

    @Getter
    protected Long id;

    @Getter
    protected Integer appId;

    @Getter
    protected String name;

    @Getter
    protected Integer levelValue;

    @Getter
    protected Integer minExp;

    public AbstractLevel(Long id, Integer appId, String name, Integer levelValue, Integer minExp) {
        this.id = id;
        this.appId = appId;
        this.name = name;
        this.levelValue = levelValue;
        this.minExp = minExp;
    }

    @Override
    public int compareTo(@NotNull Level o) {
        if (this.levelValue > o.getLevelValue()) {
            return 1;
        } else if (this.levelValue < o.getLevelValue()) {
            return -1;
        }
        return 0;
    }

}
