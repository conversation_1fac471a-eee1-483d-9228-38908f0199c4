package fm.lizhi.ocean.wavecenter.domain.grow.entity;

import lombok.Getter;
import lombok.Setter;

/**
 * 公会等级实体
 * <AUTHOR>
 * @date 2025/3/18 17:27
 */
public class FamilyLevel extends AbstractLevel{

    /**
     * 周最小流水
     */
    @Getter
    private Integer weekMinIncome;

    /**
     * 媒体资源
     */
    @Getter
    @Setter
    private FamilyLevelMedia familyLevelMedia;

    public FamilyLevel(Long id, Integer appId, String name, Integer weekMinIncome, FamilyLevelMedia familyLevelMedia) {
        // 使用经验值最小值作为等级值 当前直接使用周收入作为经验值
        super(id, appId, name, weekMinIncome, weekMinIncome);
        this.weekMinIncome = weekMinIncome;
        this.familyLevelMedia = familyLevelMedia;
    }

    public FamilyLevel(Long id, Integer appId, String name, Integer weekMinIncome) {
        // 使用经验值最小值作为等级值
        super(id, appId, name, weekMinIncome, weekMinIncome);
        this.weekMinIncome = weekMinIncome;
        // 媒体资源可以为空
        this.familyLevelMedia = FamilyLevelMedia.builder().build();
    }

}
