package fm.lizhi.ocean.wavecenter.domain.grow.entity.organization;

import fm.lizhi.ocean.wavecenter.domain.eventBus.EventBusHolder;
import fm.lizhi.ocean.wavecenter.domain.grow.entity.FamilyLevel;
import fm.lizhi.ocean.wavecenter.domain.grow.entity.Period;
import fm.lizhi.ocean.wavecenter.domain.grow.event.GrowFamilyLevelPeriodUpdateEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.comparator.Comparators;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 成长体系的公会
 * <AUTHOR>
 * @date 2025/3/19 18:53
 */
@Slf4j
public class GrowFamily extends AbstractOrganization<FamilyLevel>{

    /**
     * 公会的等级和经验存在周期性
     */
    private Map<Period, Integer> periodExpMap = new HashMap<>();
    private Map<Period, FamilyLevel> periodLevelMap = new HashMap<>();

    public GrowFamily(Long id, Integer appId, Map<Period, Integer> periodExpMap, Map<Period, FamilyLevel> periodLevelMap) {
        super(id, appId);
        this.periodExpMap = periodExpMap;
        this.periodLevelMap = periodLevelMap;
    }

    public GrowFamily(Long id, Integer appId) {
        super(id, appId);
    }

    @Override
    public FamilyLevel getLevel() {
        log.warn("The level needs to specify the period");
        return null;
    }

    public Optional<FamilyLevel> getLevel(Period period) {
        return Optional.ofNullable(periodLevelMap.get(period));
    }

    @Override
    public Integer getExp() {
        log.warn("The level needs to specify the period");
        return 0;
    }

    public Integer getExp(Period period) {
        return periodExpMap.getOrDefault(period, 0);
    }

    @Override
    public void addExp(Integer exp) {
        throw new IllegalArgumentException("The exp needs to specify the period");
    }

    public void addExp(Integer exp, Period period){
        Integer oldExp = periodExpMap.getOrDefault(period, 0);
        periodExpMap.put(period, oldExp + exp);
    }

    public void updateLevel(FamilyLevel level, Period period){
        periodLevelMap.put(period, level);
    }

    /**
     * 结算等级
     * @param period
     * @param levels
     */
    public void updateLevel(Period period, List<FamilyLevel> levels){
        levels.sort(Comparators.comparable().reversed());

        Integer exp = periodExpMap.getOrDefault(period, 0);
        for (FamilyLevel level : levels) {
            if (exp >= level.getMinExp()) {
                updateLevel(level, period);
                // 发送事件
                EventBusHolder.get().publishEvent(new GrowFamilyLevelPeriodUpdateEvent(this, level, period));
                return;
            }
        }
    }

    @Override
    public void updateLevel(List<FamilyLevel> levels) {
        throw new IllegalArgumentException("The level needs to specify the period");
    }
}
