package fm.lizhi.ocean.wavecenter.domain.grow.entity.organization;

import fm.lizhi.ocean.wavecenter.domain.grow.entity.Level;
import lombok.Getter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/19 18:56
 */
@Getter
public abstract class AbstractOrganization<L extends Level> implements Organization<L>{

    protected Long id;

    protected Integer appId;

    protected L level;

    protected Integer exp;

    protected AbstractOrganization(Long id, Integer appId, L level, Integer exp) {
        this.id = id;
        this.appId = appId;
        this.level = level;
        this.exp = exp;
    }

    protected AbstractOrganization(Long id, Integer appId) {
        this.id = id;
        this.appId = appId;
        this.exp = 0;
    }

    /**
     * 增加经验
     * @param exp
     */
    public void addExp(Integer exp) {
        this.exp += exp;
    }

    /**
     * 更新等级
     * @param levels
     */
    public abstract void updateLevel(List<L> levels);

}
