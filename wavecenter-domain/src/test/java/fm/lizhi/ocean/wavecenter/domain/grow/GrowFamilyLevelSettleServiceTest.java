package fm.lizhi.ocean.wavecenter.domain.grow;

import com.google.common.collect.Lists;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.common.utils.MyDateUtil;
import fm.lizhi.ocean.wavecenter.domain.eventBus.EventBus;
import fm.lizhi.ocean.wavecenter.domain.eventBus.EventBusHolder;
import fm.lizhi.ocean.wavecenter.domain.grow.entity.FamilyLevel;
import fm.lizhi.ocean.wavecenter.domain.grow.entity.Level;
import fm.lizhi.ocean.wavecenter.domain.grow.entity.Period;
import fm.lizhi.ocean.wavecenter.domain.grow.entity.organization.GrowFamily;
import fm.lizhi.ocean.wavecenter.domain.grow.event.GrowFamilyLevelPeriodUpdateEvent;
import fm.lizhi.ocean.wavecenter.domain.grow.repository.FamilyIncomeRepository;
import fm.lizhi.ocean.wavecenter.domain.grow.repository.FamilyLevelRepository;
import fm.lizhi.ocean.wavecenter.domain.grow.repository.GrowFamilyRepository;
import fm.lizhi.ocean.wavecenter.domain.grow.service.GrowFamilyLevelSettleService;
import fm.lizhi.ocean.wavecenter.domain.system.Visitor;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.context.ApplicationEvent;

import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * 测试公会等级领域服务
 * <AUTHOR>
 * @date 2025/3/22 11:45
 */
public class GrowFamilyLevelSettleServiceTest {

    FamilyLevel level = new FamilyLevel(1L, BusinessEvnEnum.PP.getAppId(), "一星", 1);
    FamilyLevel level2 = new FamilyLevel(2L, BusinessEvnEnum.PP.getAppId(), "二星", 101);
    FamilyLevel level3 = new FamilyLevel(2L, BusinessEvnEnum.PP.getAppId(), "三星", 201);

    List<FamilyLevel> levels = Lists.newArrayList(level, level2, level3);

    Long FamilyId1 = 1L;

    /**
     * 测试周结算
     * familyId=1 周收入150 结算等级为二星
     */
    @Test
    public void testSettleWeek(){
        Date monDay = MyDateUtil.getDayValueDate(20250310);
        Date sunDay = MyDateUtil.getDayValueDate(20250316);

        new EventBusHolder(new EventBus() {
            @Override
            public void publishEvent(ApplicationEvent event) {
                Assert.assertTrue(event instanceof GrowFamilyLevelPeriodUpdateEvent);
                GrowFamilyLevelPeriodUpdateEvent updateEvent = (GrowFamilyLevelPeriodUpdateEvent) event;
                Level eventLevel = updateEvent.getLevel();
                Assert.assertEquals(eventLevel.getId(), level2.getId());
            }
        });

        FamilyLevelRepository levelRepository = new FamilyLevelRepository() {
            @Override
            public void save(FamilyLevel familyLevel, Visitor visitor) {

            }

            @Override
            public List<FamilyLevel> getAppFamilyLevels(Integer appId) {
                return levels;
            }

            @Override
            public Optional<FamilyLevel> getLevel(Integer appId, Long levelId) {
                return Optional.empty();
            }
        };

        FamilyIncomeRepository familyIncomeRepository = new FamilyIncomeRepository() {
            @Override
            public Integer getFamilyWeekIncome(Integer appId, Long familyId, Date monDay, Date sunDay) {
                if (familyId == FamilyId1) {
                    return 150;
                }
                return 0;
            }
        };

        GrowFamilyRepository growFamilyRepository = new GrowFamilyRepository() {
            @Override
            public void saveFamily(GrowFamily family, Period period) {
                Optional<FamilyLevel> levelOp = family.getLevel(period);
                Assert.assertTrue(levelOp.isPresent());
                Assert.assertEquals(levelOp.get().getId(), level2.getId());
            }
        };

        GrowFamilyLevelSettleService service = new GrowFamilyLevelSettleService(familyIncomeRepository, levelRepository, growFamilyRepository);
        service.settleWeekLevel(BusinessEvnEnum.PP.getAppId(), Lists.newArrayList(FamilyId1), monDay, sunDay);
    }

    /**
     * 测试指定等级
     * familyId=1 结算等级为二星
     */
    @Test
    public void testInLevel(){
        Date monDay = MyDateUtil.getDayValueDate(20250310);
        Date sunDay = MyDateUtil.getDayValueDate(20250316);

        new EventBusHolder(new EventBus() {
            @Override
            public void publishEvent(ApplicationEvent event) {
                Assert.assertTrue(event instanceof GrowFamilyLevelPeriodUpdateEvent);
                GrowFamilyLevelPeriodUpdateEvent updateEvent = (GrowFamilyLevelPeriodUpdateEvent) event;
                Level eventLevel = updateEvent.getLevel();
                Assert.assertEquals(eventLevel.getId(), level2.getId());
            }
        });

        FamilyLevelRepository levelRepository = new FamilyLevelRepository() {
            @Override
            public void save(FamilyLevel familyLevel, Visitor visitor) {

            }

            @Override
            public List<FamilyLevel> getAppFamilyLevels(Integer appId) {
                return levels;
            }

            @Override
            public Optional<FamilyLevel> getLevel(Integer appId, Long levelId) {
                return Optional.empty();
            }
        };

        FamilyIncomeRepository familyIncomeRepository = new FamilyIncomeRepository() {
            @Override
            public Integer getFamilyWeekIncome(Integer appId, Long familyId, Date monDay, Date sunDay) {
                return 0;
            }
        };

        GrowFamilyRepository growFamilyRepository = new GrowFamilyRepository() {
            @Override
            public void saveFamily(GrowFamily family, Period period) {
                Optional<FamilyLevel> levelOp = family.getLevel(period);
                Assert.assertTrue(levelOp.isPresent());
                Assert.assertEquals(levelOp.get().getId(), level2.getId());
            }
        };

        GrowFamilyLevelSettleService service = new GrowFamilyLevelSettleService(familyIncomeRepository, levelRepository, growFamilyRepository);
        service.settleInLevel(BusinessEvnEnum.PP.getAppId(), FamilyId1, monDay, sunDay, level2.getId());
    }

}
