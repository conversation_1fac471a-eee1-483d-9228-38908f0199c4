package fm.lizhi.ocean.wavecenter.domain.grow;

import com.google.common.collect.Lists;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.common.utils.MyDateUtil;
import fm.lizhi.ocean.wavecenter.domain.grow.entity.FamilyLevel;
import fm.lizhi.ocean.wavecenter.domain.grow.entity.Period;
import fm.lizhi.ocean.wavecenter.domain.grow.entity.organization.GrowFamily;
import org.junit.Assert;
import org.junit.Test;

import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * 测试公会等级逻辑
 * <AUTHOR>
 * @date 2025/3/20 17:53
 */
public class TestGrowFamily {

    FamilyLevel level1 = new FamilyLevel(1L, BusinessEvnEnum.PP.getAppId(), "1级", 0);
    FamilyLevel level2 = new FamilyLevel(2L, BusinessEvnEnum.PP.getAppId(), "2级", 100);
    FamilyLevel level3 = new FamilyLevel(3L, BusinessEvnEnum.PP.getAppId(), "3级", 200);
    FamilyLevel level4 = new FamilyLevel(4L, BusinessEvnEnum.PP.getAppId(), "4级", 300);

    List<FamilyLevel> levels = Lists.newArrayList(
            level1
            , level2
            , level3
            , level4
    );

    @Test
    public void testUpdateLevel() {
        Date start = MyDateUtil.getDayValueDate(20250310);
        Date end = MyDateUtil.getDayValueDate(20250316);
        Period period = new Period(start, end);

        GrowFamily growFamily = new GrowFamily(1L, BusinessEvnEnum.PP.getAppId());
        growFamily.addExp(100, period);
        growFamily.updateLevel(period, levels);

        Optional<FamilyLevel> level = growFamily.getLevel(period);
        Assert.assertTrue(level.isPresent());

        // 周期经验 100 命中2级
        Assert.assertEquals(level2.getId(), level.get().getId());

        growFamily.addExp(150, period);
        growFamily.updateLevel(period, levels);

        level = growFamily.getLevel(period);
        Assert.assertTrue(level.isPresent());
        // 总经验 250 命中3级
        Assert.assertEquals(level3.getId(), level.get().getId());
    }

    /**
     * 测试多个周期
     */
    @Test
    public void testSamePeriod(){
        Period period1 = buildPeriod(20250310, 20250316);
        Period period2 = buildPeriod(20250310, 20250316);

        GrowFamily growFamily = new GrowFamily(1L, BusinessEvnEnum.PP.getAppId());
        growFamily.addExp(100, period1);
        growFamily.updateLevel(period1, levels);

        Optional<FamilyLevel> period1Level = growFamily.getLevel(period1);
        Assert.assertTrue(period1Level.isPresent());

        Optional<FamilyLevel> period2Level = growFamily.getLevel(period2);
        Assert.assertTrue(period2Level.isPresent());

        // period1和period2是同个周期 获取的等级应该相同
        Assert.assertEquals(period1Level.get().getId(), period2Level.get().getId());

        Period period3 = buildPeriod(20250303, 20250309);
        growFamily.addExp(350, period3);
        growFamily.updateLevel(period3, levels);

        Optional<FamilyLevel> period3Level = growFamily.getLevel(period3);
        Assert.assertTrue(period3Level.isPresent());
        // period3 命中等级4
        Assert.assertEquals(level4.getId(), period3Level.get().getId());

        Optional<FamilyLevel> period2Level2 = growFamily.getLevel(period2);
        Assert.assertTrue(period2Level2.isPresent());
        // period2 命中等级2 说明不同周期可以获取到不同等级
        Assert.assertEquals(level2.getId(), period2Level2.get().getId());
    }

    private Period buildPeriod(Integer start, Integer end) {
        Date startDate = MyDateUtil.getDayValueDate(start);
        Date endDate = MyDateUtil.getDayValueDate(end);
        return new Period(startDate, endDate);
    }

}
