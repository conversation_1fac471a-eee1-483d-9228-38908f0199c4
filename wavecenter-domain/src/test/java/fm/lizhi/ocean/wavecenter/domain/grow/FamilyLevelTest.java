package fm.lizhi.ocean.wavecenter.domain.grow;

import com.google.common.collect.Lists;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.domain.grow.entity.FamilyLevel;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.util.comparator.Comparators;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/22 10:34
 */
public class FamilyLevelTest {

    /**
     * 测试收入和经验的映射关系
     */
    @Test
    public void testIncomeAndExp(){
        FamilyLevel level = new FamilyLevel(1L, BusinessEvnEnum.PP.getAppId(), "一星", 1);

        Assert.assertEquals(level.getMinExp(), level.getWeekMinIncome());
        Assert.assertEquals(level.getLevelValue(), level.getWeekMinIncome());
    }

    /**
     * 测试排序
     */
    @Test
    public void testSort(){
        FamilyLevel level = new FamilyLevel(1L, BusinessEvnEnum.PP.getAppId(), "一星", 1);
        FamilyLevel level2 = new FamilyLevel(2L, BusinessEvnEnum.PP.getAppId(), "二星", 101);
        FamilyLevel level3 = new FamilyLevel(2L, BusinessEvnEnum.PP.getAppId(), "三星", 201);

        List<FamilyLevel> levels = Lists.newArrayList(level2, level, level3);
        levels.sort(Comparators.comparable());

        Assert.assertEquals(levels.get(0).getId(), level.getId());
        Assert.assertEquals(levels.get(1).getId(), level2.getId());
        Assert.assertEquals(levels.get(2).getId(), level3.getId());
    }

}
