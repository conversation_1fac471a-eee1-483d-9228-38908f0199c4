package fm.lizhi.ocean.wavecenter.config;

import fm.lizhi.ocean.lamp.common.generic.context.BusinessContext;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2024/3/26 19:33
 */
@Configuration
public class GenericConfig {

    @Bean
    public BusinessContext businessContext(){
        return new BusinessContext() {
            @Override
            public String getRegion() {
                return ContextUtils.getBusinessEvnEnum().getRegion();
            }

            @Override
            public String getBusinessEnv() {
                return ContextUtils.getBusinessEvnEnum().getBusinessEnv();
            }
        };
    }

}
