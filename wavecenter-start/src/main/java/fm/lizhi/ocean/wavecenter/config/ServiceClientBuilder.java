package fm.lizhi.ocean.wavecenter.config;

import fm.lizhi.common.dubbo.DubboClientBuilder;
import fm.lizhi.content.review.api.CheckDataService;
import fm.lizhi.pay.settle.api.creativecenter.CreatorDataQueryService;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * rpc服务消费者配置
 */
@Configuration
public class ServiceClientBuilder {

    private final int timeout = 5000;
    private final int connections = 100;

    @Bean
    public CreatorDataQueryService creatorDataQueryService(){
        return new DubboClientBuilder<>(CreatorDataQueryService.class)
                .timeoutInMillis(timeout)
                .connections(connections)
                .build();
    }

    @Bean
    public CheckDataService checkDataService(){
        return new DubboClientBuilder<>(CheckDataService.class)
                .timeoutInMillis(timeout)
                .connections(connections)
                .build();
    }


}
