package fm.lizhi.ocean.wavecenter;

import fm.lizhi.common.datastore.mysql.spring.boot.annotation.EnableDatastoreMysqlConfig;
import fm.lizhi.common.dubbo.adapter.springboot.EnableServiceProvider;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;

/**
 * 微服务应用启动类
 *
 * <AUTHOR> 由脚手架生成
 */
@EnableServiceProvider(basePackages = "fm.lizhi.ocean.wavecenter")
@SpringBootApplication(exclude={DataSourceAutoConfiguration.class})
@EnableDatastoreMysqlConfig(basePackages = "fm.lizhi.ocean.wavecenter")
public class MicroservicesApplication {

    // ⚠️⚠️⚠️本地DEBUG启动，请记得先配置启动参数，见READNE.MD
    public static void main(String[] args) {
        SpringApplication.run(MicroservicesApplication.class, args);
    }
}
