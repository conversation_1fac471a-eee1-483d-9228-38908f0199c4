<?xml version="1.0"?>
<assembly>
    <id>deploy</id>
    <baseDirectory/>
    <formats>
        <format>tar.gz</format>
    </formats>
    <dependencySets>
        <dependencySet>
            <useProjectArtifact>true</useProjectArtifact>
            <outputDirectory>lib</outputDirectory>
        </dependencySet>
    </dependencySets>
    <fileSets>
        <fileSet>
            <directory>bin</directory>
            <outputDirectory>/</outputDirectory>
            <includes>
                <include>startup.sh</include>
            </includes>
        </fileSet>
        <fileSet>
            <directory>src/main/resources/${lz.env}</directory>
            <outputDirectory>/conf</outputDirectory>
            <includes>
                <include>logback.xml</include>
            </includes>
        </fileSet>
    </fileSets>
</assembly>
