<?xml version="1.0"?>
<configuration>
    <appender name="infoAppender" class="ch.qos.logback.core.rolling.RollingFileAppender">
	    <file>${LOG_PATH}/info.log</file>
	    <append>true</append>
	    <encoder>
	            <Pattern>%d{yyyy-MM-dd HH:mm:ss.SSS}`CID=%X{cid:-N/A}`MID=%X{mid:-N/A}`TID=%X{tid:-N/A}`SID=%X{sid:-N/A}`TRACE_ID=%X{traceId}`%m%n</Pattern>
	    </encoder>
	    <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
	            <fileNamePattern>${LOG_PATH}/info.%d{yyyy-MM-dd_HH}.log</fileNamePattern>
	    </rollingPolicy>
    </appender>
    <appender name="asyncInfoAppender" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="infoAppender"/>
        <queueSize>10000</queueSize>
    </appender>

    <appender name="accessAppender" class="ch.qos.logback.core.rolling.RollingFileAppender">
	    <file>${LOG_PATH}/access.log</file>
	    <append>true</append>
	    <encoder>
	            <Pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - `CID=%X{cid:-N/A}`MID=%X{mid:-N/A}`TID=%X{tid:-N/A}`SID=%X{sid:-N/A}`TRACE_ID=%X{traceId}`%msg%n</Pattern>
	    </encoder>
	    <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
	            <fileNamePattern>${LOG_PATH}/access.%d{yyyy-MM-dd_HH}.log</fileNamePattern>
	    </rollingPolicy>
    </appender>
    <appender name="asyncAccessAppender" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="accessAppender"/>
        <queueSize>10000</queueSize>
    </appender>

    <appender name="serverAppender" class="ch.qos.logback.core.rolling.RollingFileAppender">
	    <file>${LOG_PATH}/server.log</file>
	    <append>true</append>
	    <encoder>
	            <Pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - `CID=%X{cid:-N/A}`MID=%X{mid:-N/A}`TID=%X{tid:-N/A}`SID=%X{sid:-N/A}`TRACE_ID=%X{traceId}`%msg%n</Pattern>
	    </encoder>
	    <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
	            <fileNamePattern>${LOG_PATH}/server.%d{yyyy-MM-dd_HH}.log</fileNamePattern>
	    </rollingPolicy>
    </appender>
    <appender name="asyncServerAppender" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="serverAppender"/>
        <queueSize>10000</queueSize>
    </appender>

    <appender name="stdout" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} [%X{traceId}] - %msg%n</pattern>
        </encoder>
    </appender>

    <appender name="catAppender" class="com.dianping.cat.logback.CatLogbackAppender"/>

    <logger name="access_log" level="info" additivity="false">
        <appender-ref ref="asyncAccessAppender"/>
    </logger>
    <logger name="info" level="info" additivity="false">
        <appender-ref ref="asyncInfoAppender"/>
    </logger>
    <root level="info">
        <appender-ref ref="asyncServerAppender"/>
        <appender-ref ref="catAppender"/>
    </root>
</configuration>
