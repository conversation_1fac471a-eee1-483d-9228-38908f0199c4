import java.io.*;
import java.nio.file.*;
import java.util.*;
import java.util.regex.*;

/**
 * 简化版日志分析工具
 * 快速提取URL并排序
 */
public class SimpleLogAnalyzer {
    
    public static void main(String[] args) {
        // 要分析的日志文件
        String[] files = {
            "fm/lizhi/ocean/wavecenter/test/data (22).txt",
            "fm/lizhi/ocean/wavecenter/test/data (23).txt"
        };
        
        Set<String> urls = new TreeSet<>(); // 自动排序和去重
        Pattern urlPattern = Pattern.compile("url=([^\\s,]+)");
        
        // 读取并处理文件
        for (String file : files) {
            try {
                List<String> lines = Files.readAllLines(Paths.get(file));
                for (String line : lines) {
                    Matcher matcher = urlPattern.matcher(line);
                    if (matcher.find()) {
                        urls.add(matcher.group(1));
                    }
                }
                System.out.println("已处理文件: " + file);
            } catch (IOException e) {
                System.err.println("读取文件失败: " + file + " - " + e.getMessage());
            }
        }
        
        // 输出结果
        System.out.println("\n找到 " + urls.size() + " 个不重复的URL:");
        System.out.println("=".repeat(60));
        
        int index = 1;
        for (String url : urls) {
            System.out.printf("%2d. %s%n", index++, url);
        }
        
        System.out.println("=".repeat(60));
        
        // 保存到文件
        try {
            Files.write(Paths.get("sorted_urls.txt"), urls);
            System.out.println("结果已保存到: sorted_urls.txt");
        } catch (IOException e) {
            System.err.println("保存文件失败: " + e.getMessage());
        }
    }
}
