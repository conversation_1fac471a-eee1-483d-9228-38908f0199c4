日志分析结果
================================================================================
分析时间: Tue Jun 03 15:39:39 CST 2025

分析的文件:
  - fm/lizhi/ocean/wavecenter/test/data (22).txt
  - fm/lizhi/ocean/wavecenter/test/data (23).txt

总共找到 28 个不重复的URL

排序后的URL列表:
--------------------------------------------------------------------------------
  1. http://wave-bak1.lzpscn1.com/amusement/info
  2. http://wave-bak1.lzpscn1.com/amusement/reportAudioSource
  3. http://wave-bak1.lzpscn1.com/live/getUserLiveStatus
  4. http://wave-bak1.lzpscn1.com/live/homeList
  5. http://wave-bak1.lzpscn1.com/rank/rankInfo
  6. http://wave-bak1.lzpscn1.com/user/heartbeat
  7. http://wave.lzpscn1.com/amusement/info
  8. http://wave.lzpscn1.com/amusement/reportAudioSource
  9. http://wave.lzpscn1.com/amusement/waitingUserList
 10. http://wave.lzpscn1.com/checkIn/allMic/notAllocation
 11. http://wave.lzpscn1.com/checkIn/config
 12. http://wave.lzpscn1.com/checkIn/detail
 13. http://wave.lzpscn1.com/checkIn/getSpecialConfig
 14. http://wave.lzpscn1.com/comment/getLatestComment
 15. http://wave.lzpscn1.com/comment/styleList
 16. http://wave.lzpscn1.com/giftEffect/getChangeEffectPackage
 17. http://wave.lzpscn1.com/im/appkey
 18. http://wave.lzpscn1.com/im/recent/visitor
 19. http://wave.lzpscn1.com/live/getUserLiveStatus
 20. http://wave.lzpscn1.com/live/homeList
 21. http://wave.lzpscn1.com/polish/permission
 22. http://wave.lzpscn1.com/rank/rankInfo
 23. http://wave.lzpscn1.com/room/income
 24. http://wave.lzpscn1.com/room/onlineCommonUsers
 25. http://wave.lzpscn1.com/room/onlineUsers
 26. http://wave.lzpscn1.com/user/login/out
 27. http://wave.lzpscn1.com/user/relatedUserList
 28. http://wave.lzpscn1.com/user/simple
--------------------------------------------------------------------------------
