package fm.lizhi.ocean.wavecenter.test.background;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSONObject;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityToolBean;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.bean.ActivityTemplateFlowResourceBean;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.bean.ActivityTemplateFlowResourceImageBean;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.bean.ActivityTemplateFlowResourceImageExtraBean;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.bean.ActivityTemplateProcessBean;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestCreateActivityTemplate;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestDeleteActivityTemplate;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.response.ResponseCreateActivityTemplate;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.service.ActivityTemplateConfigService;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.datastore.mapper.ext.ActivityTemplateInfoExtMapper;
import fm.lizhi.ocean.wavecenter.service.activitycenter.manager.ActivityToolsManager;
import fm.lizhi.ocean.wavecenter.test.AbstractDataCenterTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
public class ActivityTemplateTest extends AbstractDataCenterTest {

    @Autowired
    private ActivityTemplateConfigService activityTemplateConfigService;

    @Autowired
    private ActivityToolsManager activityToolManager;

    @Autowired
    private ActivityTemplateInfoExtMapper activityTemplateInfoExtMapper;

    private static final Long classId = 5409828172692718719L;

    private static final int appId = BusinessEvnEnum.PP.getAppId();

    @Test
    public void test(){
        while (true) {
            activityTemplateInfoExtMapper.updateTimeLimit(5415610487148731519L, 30, new Date(), new Date());
            activityTemplateInfoExtMapper.updateTimeLimit(5415610487148731519L, null, null, null);
            activityTemplateInfoExtMapper.updateUpTimeLimit(5415610487148731519L, new Date(), new Date());
            activityTemplateInfoExtMapper.updateUpTimeLimit(5415610487148731519L, null, null);
        }
    }

    @Test
    public void createTemplate(){


        RequestCreateActivityTemplate template = new RequestCreateActivityTemplate();
        template.setAppId(appId);
        template.setName(getRandomString("测试活动模板", 2));
        template.setClassId(classId);
        template.setGoal(getRandomChinese("模板目标: "));
        template.setIntroduction(getRandomChinese("模板介绍"));
        template.setProcesses(buildProcesses(5));
        template.setAuxiliaryPropUrls(CollUtil.newArrayList("/sociality/2024/07/24/3089282513531193404.png", "/studio/2024/07/17/3088047624533417526.jpg"));
        template.setPosterUrl("/studio/2024/07/17/3088047624533417526.jpg");
        template.setActivityTools(CollUtil.newArrayList(activityToolManager.getAllActivityTool(appId)).stream().map(ActivityToolBean::getType).collect(Collectors.toList()));
        template.setRoomAnnouncement(getRandomChinese("模板房间公告:"));
        template.setRoomAnnouncementImages(CollUtil.newArrayList("/sociality/2024/07/24/3089282513531193404.png", "/studio/2024/07/17/3088047624533417526.jpg"));
        template.setRoomBackgroundIds(CollUtil.newArrayList(5404084925234309759L, 5404084925234309247L));
        template.setAvatarWidgetIds(CollUtil.newArrayList(5405755914483434623L, 5405755914483430015L, 5404439511285367935L));
        template.setFlowResources(buildFlowResources());
        template.setOperator(getRandomString("测试", 3));

        Result<ResponseCreateActivityTemplate> result = activityTemplateConfigService.createTemplate(template);
        log.info(JSONObject.toJSONString(result));
    }


    @Test
    public void deleteTemplate(){
        RequestDeleteActivityTemplate template = new RequestDeleteActivityTemplate();
        template.setId(5412211654037341311L);
        template.setOperator(getRandomString("测试", 3));

        Result<Void> result = activityTemplateConfigService.deleteTemplate(template);
        log.info(JSONObject.toJSONString(result));

    }



    private List<ActivityTemplateFlowResourceBean> buildFlowResources() {
        ActivityTemplateFlowResourceBean bean1 = new ActivityTemplateFlowResourceBean();
        bean1.setResourceConfigId(5409976948212368511L);
        bean1.setImages(buildActivityTemplateFlowResourceImageBean());


        ActivityTemplateFlowResourceBean bean2 = new ActivityTemplateFlowResourceBean();
        bean2.setResourceConfigId(5409982445770507391L);
        bean2.setImages(buildActivityTemplateFlowResourceImageBean());

        ActivityTemplateFlowResourceBean bean3 = new ActivityTemplateFlowResourceBean();
        bean3.setResourceConfigId(5409982445770507482L);
        bean3.setImages(buildActivityTemplateFlowResourceImageBean());

        return CollUtil.newArrayList(bean1, bean2, bean3);
    }


    private List<ActivityTemplateFlowResourceImageBean> buildActivityTemplateFlowResourceImageBean() {
        ActivityTemplateFlowResourceImageBean bean = new ActivityTemplateFlowResourceImageBean();
        bean.setImageUrl("/public/%E4%BA%A4%E5%8F%8B%E7%AC%94%E8%AE%B0-1.png");
        bean.setExtra(new ActivityTemplateFlowResourceImageExtraBean().setColor("#FFFFFF").setScale("1:1"));

        ActivityTemplateFlowResourceImageBean bean2 = new ActivityTemplateFlowResourceImageBean();
        bean2.setImageUrl("/public/%E5%BC%80%E6%92%AD%E5%8A%A9%E6%89%8B%E5%85%AC%E5%91%8A.png");
        bean2.setExtra(new ActivityTemplateFlowResourceImageExtraBean().setColor("#FFF000").setScale("1:1"));

        return CollUtil.newArrayList(bean2, bean);
    }


    private List<ActivityTemplateProcessBean> buildProcesses(int count) {

        List<ActivityTemplateProcessBean> processes = new ArrayList<>();

        for (int i = 1; i <= count; i++) {
            ActivityTemplateProcessBean bean = new ActivityTemplateProcessBean();
            bean.setName(getRandomChinese("流程:"));
            bean.setDuration((i * 10) +"分钟");
            bean.setExplanation(getRandomChinese("流程说明:"));
            processes.add(bean);
        }
        return processes;
    }

    private String getRandomString(String prefix, int length){
        return prefix + RandomUtil.randomStringUpper(length);
    }

    private String getRandomChinese(String prefix){
        return prefix + RandomUtil.randomChinese();
    }

}
