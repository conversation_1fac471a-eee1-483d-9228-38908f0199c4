package fm.lizhi.ocean.wavecenter.test.activitycenter;

import com.alibaba.fastjson.JSONObject;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.activitycenter.request.RequestActivityOptionOfficialTime;
import fm.lizhi.ocean.wavecenter.api.activitycenter.response.ResponseActivityOptionOfficialTime;
import fm.lizhi.ocean.wavecenter.api.activitycenter.service.ActivityOfficialSeatTimeService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.test.AbstractDataCenterTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
public class OfficialSeatTimeTest extends AbstractDataCenterTest {

    @Autowired
    private ActivityOfficialSeatTimeService activityOfficialSeatTimeService;

    @Test
    public void testGetOfficialSeatTimeList() {
        RequestActivityOptionOfficialTime seatTimeBean = new RequestActivityOptionOfficialTime();
        seatTimeBean.setAppId(10919088);
        seatTimeBean.setActivityEndTime(1747497600000L);
        seatTimeBean.setActivityStartTime(1747488600000L);
        seatTimeBean.setTemplateId(5423754275593419391L);

        Result<ResponseActivityOptionOfficialTime> result = activityOfficialSeatTimeService.getOptionalOfficialTime(seatTimeBean);
        if (RpcResult.isFail(result)) {
            log.error("获取官频位失败:{}", result.getMessage());
            return;
        }

        log.info("获取官频位成功:{}", JSONObject.toJSONString(result.target().getOfficialOptionalTime()));
    }
}
