package fm.lizhi.ocean.wavecenter.test.demo;

import fm.lizhi.commons.util.DateUtil;
import fm.lizhi.ocean.wavecenter.common.utils.MyDateUtil;
import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.job.ActivityResourceTransferJob;
import fm.lizhi.ocean.wavecenter.test.AbstractDataCenterTest;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/10 18:16
 */
public class JobTest extends AbstractDataCenterTest {

    @Autowired
    private ActivityResourceTransferJob activityResourceTransferJob;

    @Test
    public void testJob() throws Exception {
        Date yesterday = DateUtil.getDayBefore(1);
        List<Integer> dates = new ArrayList<>(30);
        dates.add(MyDateUtil.getDateDayValue(yesterday));
        for (int i = 1; i < 30; i++) {
            Date dayBefore = DateUtil.getDayBefore(yesterday, i);
            dates.add(MyDateUtil.getDateDayValue(dayBefore));
        }
        System.out.println("dates = " + dates);
    }


    @Test
    public void testTransferJob() throws Exception {
        activityResourceTransferJob.execute(null);
    }

}
