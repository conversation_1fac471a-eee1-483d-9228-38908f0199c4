package fm.lizhi.ocean.wavecenter.test.demo;

import fm.lizhi.ocean.wavecenter.api.common.bean.PageConfigBean;
import fm.lizhi.ocean.wavecenter.api.common.bean.SaveConfigReqBean;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.service.common.manager.CommonConfigManager;
import fm.lizhi.ocean.wavecenter.test.AbstractDataCenterTest;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/20 14:54
 */
public class UserConfigTest extends AbstractDataCenterTest {

    @Autowired
    private CommonConfigManager commonConfigManager;

    @Test
    public void save(){
        SaveConfigReqBean.SaveConfigReqBeanBuilder builder = SaveConfigReqBean.builder();
        builder.appId(BusinessEvnEnum.PP.appId());
        builder.userId(2677455881410503212L);
        builder.pageCode("roomData");
        builder.config("{\"keyData\":{\"select\":[\"charm\"]}}");
        commonConfigManager.savePageConfig(builder.build());

        List<PageConfigBean> list = commonConfigManager.getPageCode(BusinessEvnEnum.PP.appId(), 2677455881410503212L, "");
        System.out.println("list = " + list);

        List<PageConfigBean> list1 = commonConfigManager.getPageCode(BusinessEvnEnum.PP.appId(), 2677455881410503212L, "roomData");
        System.out.println("list1 = " + list1);
    }

}
