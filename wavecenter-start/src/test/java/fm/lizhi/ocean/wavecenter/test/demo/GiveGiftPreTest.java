package fm.lizhi.ocean.wavecenter.test.demo;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.commons.util.DateUtil;
import fm.lizhi.commons.util.GuidGenerator;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.common.utils.DateTimeUtils;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.entity.LiveGiveGiftActionPo;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.entity.XmLiveGiveGiftActionPo;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.mapper.PpLiveGiveGiftActionMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.mapper.XmLiveGiveGiftActionMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.income.datastore.entity.WcPlayerSignCharmStat;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.remote.xm.XmUserFamilyRemote;
import fm.lizhi.ocean.wavecenter.test.AbstractDataCenterTest;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/26 20:29
 */
public class GiveGiftPreTest extends AbstractDataCenterTest {

    @Autowired
    private PpLiveGiveGiftActionMapper giveGiftActionMapper;


    @Autowired
    private XmLiveGiveGiftActionMapper xmLiveGiveGiftActionMapper;

    @Autowired
    private GuidGenerator guidGenerator;

    @Autowired
    private XmUserFamilyRemote xmUserFamilyRemote;


    Date startDate = DateUtil.formatStrToNormalDate("2024-04-01 00:00:00");
    Date endDate = DateUtil.formatStrToNormalDate("2024-04-30 23:59:59");
    Long roomId = 1377323822017411842L;
    Long recUserId = 1377323143412576002L;


    @Test
    public void xmGiftTest() {
        int pageSize = 10;
        long id = 0L;
        int resultNumber = 0;
        while (true) {
            List<XmLiveGiveGiftActionPo> xmLiveGiveGiftActionPos = xmLiveGiveGiftActionMapper.selectList(id, startDate, endDate,  pageSize);
            if (CollectionUtils.isEmpty(xmLiveGiveGiftActionPos)) {
                break;
            }
            resultNumber += xmLiveGiveGiftActionPos.size();
            id = xmLiveGiveGiftActionPos.get(xmLiveGiveGiftActionPos.size() - 1).getId();

            for (XmLiveGiveGiftActionPo xmLiveGiveGiftActionPo : xmLiveGiveGiftActionPos) {
                WcPlayerSignCharmStat wcPlayerSignCharmStat = new WcPlayerSignCharmStat();
                wcPlayerSignCharmStat.setAppId((long) BusinessEvnEnum.XIMI.getAppId());
                Date statDate = DateTimeUtils.formatTimeToDate(xmLiveGiveGiftActionPo.getCreateTime(), DateUtil.date_2);
                wcPlayerSignCharmStat.setStatDay(statDate);
                wcPlayerSignCharmStat.setId(guidGenerator.genId());
                wcPlayerSignCharmStat.setValue(xmLiveGiveGiftActionPo.getValue());
                wcPlayerSignCharmStat.setUserId(xmLiveGiveGiftActionPo.getUserId());

//                UserInFamilyBean userInFamily = xmUserFamilyRemote.getUserInFamily(xmLiveGiveGiftActionPo.getUserId());
//                wcPlayerSignCharmStat.setFamilyId(userInFamily.getFamilyId()); // 待补充
//                wcPlayerSignCharmStat.setRoomId(userInFamily.getNjId());//待补充
                wcPlayerSignCharmStat.setCreateTime(xmLiveGiveGiftActionPo.getCreateTime());
                wcPlayerSignCharmStat.setModifyTime(xmLiveGiveGiftActionPo.getModifyTime());
            }

            if (xmLiveGiveGiftActionPos.size() < pageSize) {
                break;
            }
        }
        System.out.println(resultNumber);
    }


    @Test
    public void getRoomRecFlow() {
//        PageList<LiveGiveGiftActionPo> list = giveGiftActionMapper.getRoomRecFlow(roomId, startDate, endDate, 1, 10);
//        System.out.println("list = " + list);
    }

    @Test
    public void getRoomRecFlowSum() {
//        RoomRecFlowSumDto roomRecFlowSum = giveGiftActionMapper.getRoomRecFlowSum(roomId, startDate, endDate);
//        System.out.println("roomRecFlowSum = " + roomRecFlowSum);
    }

    @Test
    public void getPersonalGiftflow() {
        PageList<LiveGiveGiftActionPo> list = giveGiftActionMapper.getPersonalGiftflow(recUserId, startDate, endDate, null, null, 1, 10);
        System.out.println("list = " + list);
    }

    @Test
    public void getPersonalGiftflowSum() {
        LiveGiveGiftActionPo list = giveGiftActionMapper.getPersonalGiftflowSum(recUserId, startDate, endDate, null, null);
        System.out.println("list = " + list);
    }

    @Test
    public void getRoomGiftflow() {
        PageList<LiveGiveGiftActionPo> roomGiftflow = giveGiftActionMapper.getRoomGiftflow(roomId, startDate, endDate, null, null, 1, 10);
        System.out.println("roomGiftflow = " + roomGiftflow);
    }

    @Test
    public void getRoomGiftflowSum() {
        LiveGiveGiftActionPo po = giveGiftActionMapper.getRoomGiftflowSum(roomId, startDate, endDate, null, null);
        System.out.println("po = " + po);
    }

}
