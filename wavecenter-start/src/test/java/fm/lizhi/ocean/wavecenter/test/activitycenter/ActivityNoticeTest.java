package fm.lizhi.ocean.wavecenter.test.activitycenter;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.response.ResponseGetActivityNoticeConfig;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestUpdateActivityNoticeConfig;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.service.ActivityNoticeConfigService;
import fm.lizhi.ocean.wavecenter.test.AbstractDataCenterTest;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Slf4j
public class ActivityNoticeTest extends AbstractDataCenterTest {

    @Autowired
    private ActivityNoticeConfigService activityNoticeConfigService;


    /**
     * 保存活动通知配置
     */
    @Test
    public void testSaveActivityNoticeConfig() {
        RequestUpdateActivityNoticeConfig request = new RequestUpdateActivityNoticeConfig();
        request.setAppId(10919088);
        request.setId(5424118514589892735L);
        request.setContent("就这");
        request.setOperator("zengjiaying");
        Result<Long> result = activityNoticeConfigService.updateNoticeConfig(request);
    }

    /**
     * 查询活动通知配置
     */
    @Test
    public void testQueryActivityNoticeConfig() {
        Result<List<ResponseGetActivityNoticeConfig>> noticeConfig = activityNoticeConfigService.getNoticeConfig(10919088);
        System.out.println(noticeConfig.target());
    }
}
