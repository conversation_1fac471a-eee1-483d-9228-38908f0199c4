package fm.lizhi.ocean.wavecenter.test.activitycenter;

import cn.hutool.core.date.DateUtil;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.RequestGetResourceTimeBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ResponseGetResourceTimeBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.service.ActivityResourceTimeService;
import fm.lizhi.ocean.wavecenter.base.util.JsonUtils;
import fm.lizhi.ocean.wavecenter.common.utils.ResultHandler;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.ActivityInfoDTO;
import fm.lizhi.ocean.wavecenter.service.activitycenter.manager.ActivityApplyManager;
import fm.lizhi.ocean.wavecenter.service.activitycenter.manager.ActivityChatManager;
import fm.lizhi.ocean.wavecenter.test.AbstractDataCenterTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;

@Slf4j
public class ActivityResourceTimeTest extends AbstractDataCenterTest {

    @Autowired
    private ActivityResourceTimeService activityResourceTimeService;


    /**
     * 用户取消活动
     */
    @Test
    public void getResourceTimeList() {

        RequestGetResourceTimeBean request = new RequestGetResourceTimeBean();
        request.setAppId(BusinessEvnEnum.PP.getAppId());
        request.setStartDate(new Date(1747584000000L));
        request.setEndDate(new Date(1748188800000L));
//        request.setSeat(1);
        request.setTemplateId(5438278306647553663L);

        Result<ResponseGetResourceTimeBean> result = activityResourceTimeService.getResourceTimeList(request);
        log.info("result: {}", JsonUtils.toJsonString(result.target()));
    }

}
