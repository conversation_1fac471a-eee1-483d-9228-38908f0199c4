package fm.lizhi.ocean.wavecenter.test.levelconfig;

import com.google.common.collect.Lists;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.api.grow.level.request.RequestSaveFamilyLevelConfig;
import fm.lizhi.ocean.wavecenter.service.grow.impl.FamilyLevelConfigServiceImpl;
import fm.lizhi.ocean.wavecenter.test.AbstractDataCenterTest;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @date 2025/4/1 10:53
 */
public class FamilyLevelConfigTest extends AbstractDataCenterTest {

    @Autowired
    private FamilyLevelConfigServiceImpl familyLevelConfigService;

    @Test
    public void test() {
        // ID覆盖问题
        RequestSaveFamilyLevelConfig request2 = new RequestSaveFamilyLevelConfig();
        request2.setAppId(BusinessEvnEnum.HEI_YE.getAppId());
        request2.setLevelName("TEST");
        request2.setMinIncome(900);
        request2.setLevelIcon("http://romefs.yfxn.lizhi.fm/public/wave/a3932770d5dd4b9b39cabceac2b140bf.png");
        request2.setLevelMedal("http://romefs.yfxn.lizhi.fm/public/wave/b7cad35baacc7d9514535219de2ca33b.png");
        request2.setAwardImgs(Lists.newArrayList("http://romefs.yfxn.lizhi.fm/public/wave/84dc2bb7259f1d9118e5a4a6e83e0b4e.png"));
        request2.setThemColor("#b26eff");
        request2.setBackgroundColor("#f9f9f9");
        request2.setOperator("admin");
        Result<Void> idRes = familyLevelConfigService.save(request2);
        System.out.println("idRes.rCode() = " + idRes.rCode());

    }

}
