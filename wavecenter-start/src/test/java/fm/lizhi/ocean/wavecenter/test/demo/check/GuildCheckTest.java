package fm.lizhi.ocean.wavecenter.test.demo.check;

import fm.lizhi.commons.util.DateUtil;
import fm.lizhi.ocean.wavecenter.api.live.bean.GuildRoomDayCheckStatsReq;
import fm.lizhi.ocean.wavecenter.api.live.bean.GuildRoomHourCheckStatsReq;
import fm.lizhi.ocean.wavecenter.service.live.impl.LiveGuildCheckInServiceImpl;
import fm.lizhi.ocean.wavecenter.test.AbstractDataCenterTest;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;

public class GuildCheckTest extends AbstractDataCenterTest {


    @Autowired
    private LiveGuildCheckInServiceImpl guildCheckInService;

    @Test
    public void roomStats(){

        Date endDate = new Date();
        Date startDate = new Date(endDate.getTime()-1000000000);

        GuildRoomDayCheckStatsReq statsReq = GuildRoomDayCheckStatsReq.builder()
                .appId(9637128)
                .startDate(startDate)
                .endDate(endDate)
                .familyId(5249323885502923903L)
                .build();
//        Result<PageBean<GuildRoomDayStatsRes>> result = guildCheckInService.roomDayStats(statsReq);
//        PageBean<GuildRoomDayStatsRes> target = result.target();
//        System.out.println(JSONObject.toJSONString(target));
    }

    @Test
    public void roomHourStats(){

        Date endDate = DateUtil.formatStrToDate("2024-06-25 17:00:00", DateUtil.datetime_2);
        Date startDate = DateUtil.formatStrToDate("2024-06-24 00:00:00", DateUtil.datetime_2);

        GuildRoomHourCheckStatsReq statsReq = GuildRoomHourCheckStatsReq.builder()
                .appId(10919088)
                .startDate(startDate)
                .endDate(endDate)
                .familyId(5310214712732943487L)
                .build();
//        Result<List<GuildRoomHourStatsRes>> result = guildCheckInService.roomHourStats(statsReq);
//        List<GuildRoomHourStatsRes> target = result.target();
//        System.out.println(JSONObject.toJSONString(target));
    }

}
