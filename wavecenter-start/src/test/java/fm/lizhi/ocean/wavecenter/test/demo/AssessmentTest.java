package fm.lizhi.ocean.wavecenter.test.demo;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.GuildAssessmentInfoBean;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.PlayerAssessmentInfoBean;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.RoomAssessmentInfoBean;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.service.datacenter.impl.GuildDataServiceImpl;
import fm.lizhi.ocean.wavecenter.service.datacenter.impl.PlayerDataServiceImpl;
import fm.lizhi.ocean.wavecenter.service.datacenter.impl.RoomDataServiceImpl;
import fm.lizhi.ocean.wavecenter.test.AbstractDataCenterTest;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @date 2024/5/23 15:15
 */
public class AssessmentTest extends AbstractDataCenterTest {

    @Autowired
    private GuildDataServiceImpl guildDataService;
    @Autowired
    private RoomDataServiceImpl roomDataService;
    @Autowired
    private PlayerDataServiceImpl playerDataService;

    private Long hyFamilyId = 5318540755940148863L;
    private Long hyRoomId = 1368303752892663170L;
    private Long hyPlayerId = 1368304803012168450L;

    private Long xmFamilyId = 5246373908659044991L;
    private Long xmRoomId = 1291847566882786178L;

    @Test
    public void testHy(){
        Result<GuildAssessmentInfoBean> result = guildDataService.getAssessmentInfo(BusinessEvnEnum.HEI_YE.appId(), hyFamilyId);
        GuildAssessmentInfoBean target = result.target();
        System.out.println("target = " + target);
    }

    @Test
    public void testXm(){
        long familyId = 5246373908659044991L;
        Result<GuildAssessmentInfoBean> result = guildDataService.getAssessmentInfo(BusinessEvnEnum.XIMI.appId(), familyId);
        GuildAssessmentInfoBean target = result.target();
        System.out.println("target = " + target);
    }

    @Test
    public void testRoom(){
        Result<RoomAssessmentInfoBean> hyRes = roomDataService.getAssessmentInfo(BusinessEvnEnum.HEI_YE.appId(), hyRoomId, hyFamilyId);
        RoomAssessmentInfoBean target = hyRes.target();
        System.out.println("target = " + target);

        Result<RoomAssessmentInfoBean> xmRes = roomDataService.getAssessmentInfo(BusinessEvnEnum.XIMI.appId(), xmRoomId, xmFamilyId);
        RoomAssessmentInfoBean target2 = xmRes.target();
        System.out.println("target2 = " + target2);

    }

    @Test
    public void testPlayer(){
        Result<PlayerAssessmentInfoBean> result = playerDataService.getAssessmentInfo(BusinessEvnEnum.HEI_YE.appId(), hyPlayerId, hyFamilyId, hyRoomId);
        PlayerAssessmentInfoBean target = result.target();
        System.out.println("target = " + target);
    }

}
