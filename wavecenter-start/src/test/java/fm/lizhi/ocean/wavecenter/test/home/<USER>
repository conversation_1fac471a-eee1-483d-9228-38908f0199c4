package fm.lizhi.ocean.wavecenter.test.home;

import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSONObject;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.util.DateUtil;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityBigClassBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityClassConfigBean;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.*;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.service.ActivityClassificationConfigService;
import fm.lizhi.ocean.wavecenter.api.common.constants.DateType;
import fm.lizhi.ocean.wavecenter.api.home.constants.MarketMonitorTrendChartType;
import fm.lizhi.ocean.wavecenter.api.home.request.*;
import fm.lizhi.ocean.wavecenter.api.home.response.*;
import fm.lizhi.ocean.wavecenter.api.home.service.GuildHomeService;
import fm.lizhi.ocean.wavecenter.api.home.service.RoomHomeService;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.base.util.CalculateUtil;
import fm.lizhi.ocean.wavecenter.test.AbstractDataCenterTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

@Slf4j
public class HomeServiceTest extends AbstractDataCenterTest {

    @Autowired
    private GuildHomeService guildHomeService;

    @Autowired
    private RoomHomeService roomHomeService;

    @Test
    public void getKeyDataSummary() {

        RequestGetGuildKeyDataSummary request = new RequestGetGuildKeyDataSummary()
                .setAppId(BusinessEvnEnum.XIMI.getAppId())
                .setFamilyId(5271758460393805951L)
                .setUserId(5265102936121599615L)
                .setStartDate("2024-04-22")
                .setEndDate("2024-04-28")
                ;


        Result<ResponseGuildKeyDataSummary> result = guildHomeService.getKeyDataSummary(request);
        log.info("result: {}", JSONObject.toJSONString(result));
    }


    @Test
    public void getKeyDataTrendChart() {

        RequestGetGuildKeyDataTrendChart request = new RequestGetGuildKeyDataTrendChart()
                .setAppId(BusinessEvnEnum.PP.getAppId())
                .setFamilyId(5346066226491555455L)
                .setUserId(5265102936121599615L)
                .setStartDate("2024-04-22")
                .setEndDate("2024-04-28")
                ;


        Result<List<ResponseGuildKeyDataTrendChart>> result = guildHomeService.getKeyDataTrendChart(request);
        log.info("result: {}", JSONObject.toJSONString(result));
    }


    @Test
    public void getMarketMonitorTrendChart() {

        RequestGetGuildMarketMonitorTrendChart request = new RequestGetGuildMarketMonitorTrendChart()
                .setAppId(BusinessEvnEnum.PP.getAppId())
                .setFamilyId(5346066226491555455L)
                .setUserId(5265102936121599615L)
                .setStartDate("2024-04-22")
                .setEndDate("2024-04-28")
                .setType(MarketMonitorTrendChartType.ROOM_INCOME.getType())
                ;


        Result<List<ResponseGuildMarketMonitorTrendChart>> result = guildHomeService.getMarketMonitorTrendChart(request);
        log.info("result: {}", JSONObject.toJSONString(result));
    }

    @Test
    public void getRoomKeyDataSummary() {

        RequestGetRoomKeyDataSummary request = new RequestGetRoomKeyDataSummary()
                .setAppId(BusinessEvnEnum.PP.getAppId())
                .setFamilyId(5344814249880951423L)
                .setUserId(0L)
                .setStartDate("2024-11-11")
                .setEndDate("2024-11-17")
                .setRoomId(1371951030530871426L)
                ;
        Result<ResponseRoomKeyDataSummary> result = roomHomeService.getRoomKeyDataSummary(request);
        log.info("result: {}", JSONObject.toJSONString(result));
    }


    @Test
    public void getRoomKeyDataTrendChart() {

        RequestGetRoomKeyDataTrendChart request = new RequestGetRoomKeyDataTrendChart()
                .setAppId(BusinessEvnEnum.XIMI.getAppId())
                .setFamilyId(5344814249880951423L)
                .setUserId(0L)
                .setStartDate("2024-11-11")
                .setEndDate("2024-11-17")
                .setRoomId(1371951030530871426L)
                ;
        Result<List<ResponseRoomKeyDataTrendChart>> result = roomHomeService.getRoomKeyDataTrendChart(request);
        log.info("result: {}", JSONObject.toJSONString(result));
    }



    @Test
    public void getRoomMsgAnalysisPerformanceDay() {

        RequestGetRoomMsgAnalysisPerformance request = new RequestGetRoomMsgAnalysisPerformance()
                .setAppId(BusinessEvnEnum.XIMI.getAppId())
                .setFamilyId(5259196336071416959L)
                .setUserId(0L)
                .setStartDate("2024-11-17")
                .setEndDate("2024-11-17")
                .setRoomId(1406660170930836994L)
                .setDateType(DateType.DAY.getValue())
                ;
        Result<ResponseRoomMsgAnalysisPerformance> result = roomHomeService.getRoomMsgAnalysisPerformance(request);
        log.info("result: {}", JSONObject.toJSONString(result));
    }

    @Test
    public void getRoomMsgAnalysisPerformanceWeek() {

        RequestGetRoomMsgAnalysisPerformance request = new RequestGetRoomMsgAnalysisPerformance()
                .setAppId(BusinessEvnEnum.XIMI.getAppId())
                .setFamilyId(5258061328598940287L)
                .setUserId(0L)
                .setStartDate("2024-11-11")
                .setEndDate("2024-11-17")
                .setRoomId(1319987000752872194L)
                .setDateType(DateType.WEEK.getValue())
                ;
        Result<ResponseRoomMsgAnalysisPerformance> result = roomHomeService.getRoomMsgAnalysisPerformance(request);
        log.info("result: {}", JSONObject.toJSONString(result));
    }
}
