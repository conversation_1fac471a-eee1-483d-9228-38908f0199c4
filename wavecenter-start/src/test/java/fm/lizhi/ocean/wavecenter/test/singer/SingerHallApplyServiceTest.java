package fm.lizhi.ocean.wavecenter.test.singer;

import cn.hutool.core.collection.CollUtil;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerHallApplyStatusEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.request.RequestGetSingerVerifyRecord;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.request.RequestImportHallApply;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.request.RequestOperateHallApply;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.request.RequestPageHallApply;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.response.ResponsePageHallApply;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.service.SingerHallApplyService;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.service.SingerVerifyAdminService;
import fm.lizhi.ocean.wavecenter.api.common.constants.OrderType;
import fm.lizhi.ocean.wavecenter.test.AbstractDataCenterTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

@Slf4j
public class SingerHallApplyServiceTest extends AbstractDataCenterTest {

    @Autowired
    private SingerHallApplyService singerHallApplyService;

    @Autowired
    private SingerVerifyAdminService singerVerifyAdminService;



    @Test
    public void testSingerHallApply() {

        RequestImportHallApply request = new RequestImportHallApply();
        request.setAppId(BusinessEvnEnum.PP.getAppId());
        request.setNjIds(CollUtil.newArrayList(5416153068240634412L, 1397216343153004290L, 1244364897391768962L));
        request.setStatus(SingerHallApplyStatusEnum.APPLYING);
        request.setOperator("admin");

        Result<List<Long>> result = singerHallApplyService.importHallApply(request);
        log.info("result: {}", JsonUtil.dumps(result));
    }


    @Test
    public void testPageHallApplyList() {

        RequestPageHallApply request = new RequestPageHallApply();
        request.setAppId(BusinessEvnEnum.PP.getAppId());
//        request.setNjId();
        request.setAuditStatus(SingerHallApplyStatusEnum.APPLYED.getStatus());
//        request.setStartTime();
//        request.setEndTime();
        request.setPageNo(1);
        request.setPageSize(20);

        Result<ResponsePageHallApply> result = singerHallApplyService.pageHallApplyList(request);
        log.info("result: {}", JsonUtil.dumps(result));
    }

    @Test
    public void operateHallApply() {

        RequestOperateHallApply request = new RequestOperateHallApply();
        request.setAppId(BusinessEvnEnum.PP.getAppId());
        request.setStatus(SingerHallApplyStatusEnum.REJECTED);
        request.setOperator("admin11");
        request.setId(5441011540092257407L);

        Result<Void> result = singerHallApplyService.operateHallApply(request);
        log.info("result: {}", JsonUtil.dumps(result));
    }

    @Test
    public void getVerifyRecord() {
        RequestGetSingerVerifyRecord requestGetSingerVerifyRecord = new RequestGetSingerVerifyRecord();
        requestGetSingerVerifyRecord.setAppId(BusinessEvnEnum.PP.getAppId()).setPageNo(1).setPageSize(10);
        singerVerifyAdminService.getSingerVerifyRecord(requestGetSingerVerifyRecord);
    }

    @Test
    public void testPageHallApplyListWithSorting() {
        // 测试按认证歌手数降序排序
        RequestPageHallApply request = new RequestPageHallApply();
        request.setAppId(BusinessEvnEnum.XIMI.getAppId());
//        request.setAuditStatus(SingerHallApplyStatusEnum.APPLYED.getStatus());
        request.setPageNo(1);
        request.setPageSize(20);
        request.setOrderMetrics("singerAuthCnt");
        request.setOrderType(OrderType.DESC);

        Result<ResponsePageHallApply> result = singerHallApplyService.pageHallApplyList(request);
        log.info("按认证歌手数降序排序结果: {}", JsonUtil.dumps(result));

        // 测试按优质歌手数升序排序
        request.setOrderMetrics("seniorSingerAuthCnt");
        request.setOrderType(OrderType.DESC);

        result = singerHallApplyService.pageHallApplyList(request);
        log.info("按优质歌手数升序排序结果: {}", JsonUtil.dumps(result));

        // 测试按创建时间降序排序
        request.setOrderMetrics("createTime");
        request.setOrderType(OrderType.ASC);

        result = singerHallApplyService.pageHallApplyList(request);
        log.info("按创建时间降序排序结果: {}", JsonUtil.dumps(result));
    }

}
