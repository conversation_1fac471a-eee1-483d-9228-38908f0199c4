package fm.lizhi.ocean.wavecenter.test.background;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityResourceDeployTypeConstants;
import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.AutoConfigResourceEnum;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.*;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.response.ResponseActivityResource;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.service.ActivityResourceConfigService;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.test.AbstractDataCenterTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

@Slf4j
public class ActivityResourceTest extends AbstractDataCenterTest {

    @Autowired
    private ActivityResourceConfigService activityResourceService;


    @Test
    public void saveActivityResource(){
        // 创建模拟参数
        RequestSaveActivityResource param = RequestSaveActivityResource.builder()
                .name("节目单" + RandomUtil.randomStringUpper(2))
                .relationLevelIds(ListUtil.of(5412209671909934719L))
                .deployType(ActivityResourceDeployTypeConstants.AUTO_CONFIG)
                .imageUrl("/test" + RandomUtil.randomStringUpper(5) + ".png")
                .introduction("测试介绍" + RandomUtil.randomStringUpper(5))
                .status(1)
                .resourceCode(AutoConfigResourceEnum.OFFICIAL_SEAT.getResourceCode())
                .required(false)
                .appId(BusinessEvnEnum.HEI_YE.getAppId())
                .operator("测试用户" + RandomUtil.randomStringUpper(2))
                .build();

        Result<Void> result = activityResourceService.saveActivityResource(param);
        log.info("result: {}", JSONObject.toJSONString(result));
    }

    @Test
    public void updateActivityResource(){
        // 创建模拟参数
        RequestUpdateActivityResource param = RequestUpdateActivityResource.builder()
                .id(5413021431474685055L)
                .name("测试自动资源" + RandomUtil.randomStringUpper(2))
                .deployType(ActivityResourceDeployTypeConstants.AUTO_CONFIG)
                .status(1)
                .resourceCode(AutoConfigResourceEnum.OFFICIAL_SEAT.getResourceCode())
                .required(true)
                .appId(BusinessEvnEnum.XIMI.getAppId())
                .operator("测试用户" + RandomUtil.randomStringUpper(2))
                .relationLevelIds(Lists.newArrayList(5412635436312956031L))
                .build();

        Result<Void> result = activityResourceService.updateActivityResource(param);
        log.info("result: {}", JSONObject.toJSONString(result));
    }

    @Test
    public void deleteActivityResource(){
        Result<Void> result = activityResourceService.deleteActivityResource(5413021431474685055L, BusinessEvnEnum.XIMI.getAppId(), "测试用户" + RandomUtil.randomStringUpper(2));
        log.info("result: {}", JSONObject.toJSONString(result));
    }


    @Test
    public void listActivityResource(){
        RequestPageActivityResources param = RequestPageActivityResources.builder().pageNo(1).pageSize(20).appId(BusinessEvnEnum.HEI_YE.getAppId()).build();
        Result<PageBean<ResponseActivityResource>> result = activityResourceService.listActivityResource(param);
        log.info("result: {}", JSONObject.toJSONString(result));
    }

    @Test
    public void listActivityResourceByLevelId(){
        Result<List<ResponseActivityResource>> result = activityResourceService.listActivityResourceByLevelId(5409795320489968767L, BusinessEvnEnum.PP.getAppId());
        log.info("result: {}", JSONObject.toJSONString(result));
    }

}
