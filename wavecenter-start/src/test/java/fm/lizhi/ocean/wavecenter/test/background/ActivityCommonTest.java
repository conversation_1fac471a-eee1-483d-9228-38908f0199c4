package fm.lizhi.ocean.wavecenter.test.background;

import com.alibaba.fastjson.JSONObject;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.activitycenter.service.ActivityCommonService;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.response.ResponseActivityConfigBean;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.test.AbstractDataCenterTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
public class ActivityCommonTest extends AbstractDataCenterTest {

    @Autowired
    private ActivityCommonService activityCommonService;


    @Test
    public void getBaseEnumConfig(){
        Result<ResponseActivityConfigBean> result = activityCommonService.getBaseEnumConfig(BusinessEvnEnum.PP.getAppId());
        log.info("result: {}", JSONObject.toJSONString(result));
    }


}
