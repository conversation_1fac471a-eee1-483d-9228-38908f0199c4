package fm.lizhi.ocean.wavecenter.test.demo;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.user.service.UserLoginService;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wavecenter.service.user.manager.UserManager;
import fm.lizhi.ocean.wavecenter.test.AbstractDataCenterTest;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @date 2024/4/26 20:29
 */
public class UserTest extends AbstractDataCenterTest {

    @Autowired
    private UserLoginService userLoginService;

    @Autowired
    private UserManager userManager;


    @Test
    public void getUserIdByAccessToken(){
        Result<Long> accessToken = userLoginService.getUserIdByAccessToken("53bd7e7b5edbfd89e984b4496f6d88b3");
        System.out.println(accessToken.target());
    }

    @Test
    public void getBand(){
        ContextUtils.setBusinessEvnEnum(BusinessEvnEnum.PP);
        Long userIdByBand = userManager.getUserIdByBand("1000985");
        System.out.println("userIdByBand = " + userIdByBand);
    }

}
