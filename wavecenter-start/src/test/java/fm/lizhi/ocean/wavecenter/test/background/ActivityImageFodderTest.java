package fm.lizhi.ocean.wavecenter.test.background;

import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSONObject;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityImageFodderBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityFodderClassificationEnum;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.*;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.response.ResponseSaveActivityImageFodder;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.service.ActivityImageFodderConfigService;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.test.AbstractDataCenterTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
public class ActivityImageFodderTest extends AbstractDataCenterTest {

    @Autowired
    private ActivityImageFodderConfigService activityImageFodderConfigService;


    @Test
    public void saveImageFodder(){
        ActivityFodderClassificationEnum fodderClassificationEnum = ActivityFodderClassificationEnum.OFFICIAL_CHANNEL_COVER;
        // 创建模拟参数
        RequestSaveActivityImageFodder param = RequestSaveActivityImageFodder.builder()
                .type(fodderClassificationEnum.getType())
                .color("#FF00000")
                .imageUrl("/test" + RandomUtil.randomStringUpper(5) + ".png")
                .name(fodderClassificationEnum.getName() + RandomUtil.randomStringUpper(2))
                .operator("测试用户" + RandomUtil.randomStringUpper(2))
                .scale("1:1")
                .build();

        Result<ResponseSaveActivityImageFodder> result = activityImageFodderConfigService.saveImageFodder(param);
        log.info("result: {}", JSONObject.toJSONString(result));
    }


    @Test
    public void updateActivityRule(){
        ActivityFodderClassificationEnum fodderClassificationEnum = ActivityFodderClassificationEnum.PROGRAM_COVER;
        // 创建模拟参数
        RequestUpdateActivityImageFodder param = RequestUpdateActivityImageFodder.builder()
                .id(5410381373072475263L)
                .type(fodderClassificationEnum.getType())
                .color("#FF00000")
                .imageUrl("/test" + RandomUtil.randomStringUpper(5) + ".png")
                .name(fodderClassificationEnum.getName() + RandomUtil.randomStringUpper(2))
                .operator("测试用户" + RandomUtil.randomStringUpper(2))
                .build();

        Result<Void> result = activityImageFodderConfigService.updateImageFodder(param);
        log.info("result: {}", JSONObject.toJSONString(result));
    }

    @Test
    public void pageImageFodder(){
        RequestPageActivityImageFodder param = RequestPageActivityImageFodder.builder().pageNo(1).pageSize(20).build();
        Result<PageBean<ActivityImageFodderBean>> result = activityImageFodderConfigService.pageImageFodder(param);
        log.info("result: {}", JSONObject.toJSONString(result.target()));
    }

    @Test
    public void deleteImageFodder(){
        Result<Void> result = activityImageFodderConfigService.deleteImageFodder(5410032228736435327L, "测试用户" + RandomUtil.randomStringUpper(2));
        log.info("result: {}", JSONObject.toJSONString(result));
    }

}
