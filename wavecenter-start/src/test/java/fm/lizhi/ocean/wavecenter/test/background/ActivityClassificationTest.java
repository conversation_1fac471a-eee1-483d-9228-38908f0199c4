package fm.lizhi.ocean.wavecenter.test.background;

import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSONObject;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityBigClassBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityClassConfigBean;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.*;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.service.ActivityClassificationConfigService;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.test.AbstractDataCenterTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

@Slf4j
public class ActivityClassificationTest extends AbstractDataCenterTest {

    @Autowired
    private ActivityClassificationConfigService activityClassificationService;

    @Test
    public void testSaveBigClassification() {
        // 创建模拟参数
        RequestSaveActivityBigClass param = RequestSaveActivityBigClass.builder()
                .appId(BusinessEvnEnum.PP.getAppId())
                .name("测试活动大类" + RandomUtil.randomStringUpper(2))
                .operator("测试用户"+ RandomUtil.randomStringUpper(2))
                .build();

        Result<Void> result = activityClassificationService.saveBigClassification(param);
        log.info("result: {}", JSONObject.toJSONString(result));
    }

    @Test
    public void testSaveClassification() {
        // 创建模拟参数
        RequestSaveActivityClassification param = RequestSaveActivityClassification.builder()
                .bigClassId(5409827146197632127L)
                .levelId(5409794953268167807L)
                .name("测试活动分类" + RandomUtil.randomStringUpper(2))
//                .name("测试等级AC")
                .operator("测试用户"+ RandomUtil.randomStringUpper(2))
                .build();

        Result<Void> result = activityClassificationService.saveClassification(param);
        log.info("result: {}", JSONObject.toJSONString(result));
    }

    @Test
    public void testUpdateBigClassification() {
        // 创建模拟参数
        RequestUpdateActivityBigClass param = RequestUpdateActivityBigClass.builder()
                .id(5409827146197632127L)
                .name("测试活动大类" + RandomUtil.randomStringUpper(2))
                .operator("测试用户" + RandomUtil.randomStringUpper(2))
                .build();

        Result<Void> result = activityClassificationService.updateBigClassification(param);
        log.info("result: {}", JSONObject.toJSONString(result));
    }

    @Test
    public void testUpdateClassification() {
        // 创建模拟参数
        RequestUpdateActivityClassification param = RequestUpdateActivityClassification.builder()
                .id(5409827616496551039L)
                .levelId(5409794953268167807L)
                .name("测试活动分类AD")
                .operator("测试用户" + RandomUtil.randomStringUpper(2))
                .build();

        Result<Void> result = activityClassificationService.updateClassification(param);
        log.info("result: {}", JSONObject.toJSONString(result));
    }



    @Test
    public void testDeleteBigClassification() {
        RequestDeleteActivityBigClass req = new RequestDeleteActivityBigClass();
        req.setId(5409827146197632127L);
        req.setOperator("测试用户");
        Result<Void> result = activityClassificationService.deleteBigClassification(req);
        log.info("result: {}", JSONObject.toJSONString(result));
    }

    @Test
    public void testDeleteClassification() {
        RequestDeleteActivityClassification req = new RequestDeleteActivityClassification();
        req.setId(5409827616496551039L);
        req.setOperator("测试用户");
        Result<Void> result = activityClassificationService.deleteClassification(req);
        log.info("result: {}", JSONObject.toJSONString(result));
    }

    @Test
    public void testListBigClassByAppId() {
        Result<List<ActivityBigClassBean>> resultPp = activityClassificationService.listBigClassByAppId(BusinessEvnEnum.PP.getAppId());
        log.info("resultPp : {}", JSONObject.toJSONString(resultPp));

        Result<List<ActivityBigClassBean>> resultXm = activityClassificationService.listBigClassByAppId(BusinessEvnEnum.XIMI.getAppId());
        log.info("resultXm : {}", JSONObject.toJSONString(resultXm));
    }

    @Test
    public void testListClassificationByBigClassId() {
        Result<List<ActivityClassConfigBean>> resultPp = activityClassificationService.listClassificationByBigClassId(5409827146197632127L);
        log.info("resultPp : {}", JSONObject.toJSONString(resultPp));

        Result<List<ActivityClassConfigBean>> resultXm = activityClassificationService.listClassificationByBigClassId(5409827146197632127L);
        log.info("resultXm : {}", JSONObject.toJSONString(resultXm));
    }
}
