package fm.lizhi.ocean.wavecenter.test.activitycenter;

import com.alibaba.fastjson.JSONObject;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityReportDataDetailBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityReportDataGiftBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityReportDataPlayerBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityReportDataSummaryBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.service.ActivityReportDataService;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.test.AbstractDataCenterTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

@Slf4j
public class ActivityReportDataTest extends AbstractDataCenterTest {

    @Autowired
    private ActivityReportDataService activityReportDataService;

    private static final Long activityId = 5410347483935442339L;
    private static final int appId = BusinessEvnEnum.XIMI.getAppId();


    @Test
    public void getReportSummary(){
        Result<ActivityReportDataSummaryBean> result = activityReportDataService.getReportSummary(activityId, appId);
        log.info("result: {}", JSONObject.toJSONString(result));
    }

    @Test
    public void getReportDetail(){
        Result<List<ActivityReportDataDetailBean>> result = activityReportDataService.getReportDetail(activityId, appId);
        log.info("result: {}", JSONObject.toJSONString(result));
    }

    @Test
    public void pageReportGift(){
        Result<PageBean<ActivityReportDataGiftBean>> result = activityReportDataService.pageReportGift(activityId, appId, 1, 20);
        log.info("result: {}", JSONObject.toJSONString(result));
    }

    @Test
    public void pageReportPlayer(){
        Result<PageBean<ActivityReportDataPlayerBean>> result = activityReportDataService.pageReportPlayer(5410347483935442339L, 57333013, 1, 20);
        log.info("result: {}", JSONObject.toJSONString(result));
    }

}
