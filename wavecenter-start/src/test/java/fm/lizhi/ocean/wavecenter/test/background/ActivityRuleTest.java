package fm.lizhi.ocean.wavecenter.test.background;

import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSONObject;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityApplyRuleEnum;
import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityApplyRulePeriodEnum;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityRuleConfigBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.OfficialCountRuleBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ReportCountRuleBean;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestSaveActivityRule;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestUpdateActivityRule;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.service.ActivityRuleConfigService;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.manager.ActivityRuleManager;
import fm.lizhi.ocean.wavecenter.test.AbstractDataCenterTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

@Slf4j
public class ActivityRuleTest extends AbstractDataCenterTest {

    @Autowired
    private ActivityRuleConfigService activityRuleConfigService;

    @Autowired
    private ActivityRuleManager activityRuleManager;


    @Test
    public void saveRecommendCard(){
        // 创建模拟参数
        RequestSaveActivityRule param = RequestSaveActivityRule.builder()
                .appId(BusinessEvnEnum.PP.getAppId())
                .operator("测试用户" + RandomUtil.randomStringUpper(2))
                .ruleType(ActivityApplyRuleEnum.REPORTS_COUNT.getId())
//                .rule(ReportCountRuleBean.builder().count(10).period(ActivityApplyRulePeriodEnum.HALL_WEEK.name()).build())
                .build();

        Result<Void> result = activityRuleConfigService.saveActivityRule(param);
        log.info("result: {}", JSONObject.toJSONString(result));
    }


    @Test
    public void updateActivityRule(){
        // 创建模拟参数
        RequestUpdateActivityRule param = RequestUpdateActivityRule.builder()
                .id(5410032228736435327L)
                .appId(BusinessEvnEnum.PP.getAppId())
                .operator("测试用户" + RandomUtil.randomStringUpper(2))
                .ruleType(ActivityApplyRuleEnum.OFFICIAL_COUNT.getId())
//                .rule(OfficialCountRuleBean.builder().count(RandomUtil.randomInt(1, 10)).build())
                .build();

        Result<Void> result = activityRuleConfigService.updateActivityRule(param);
        log.info("result: {}", JSONObject.toJSONString(result));
    }

    @Test
    public void listActivityRule(){
        Result<List<ActivityRuleConfigBean>> result = activityRuleConfigService.listActivityRule(BusinessEvnEnum.PP.getAppId());
        log.info("result: {}", JSONObject.toJSONString(result.target()));
    }

    @Test
    public void deleteActivityRule(){
        Result<Void> result = activityRuleConfigService.deleteActivityRule(5410032228736435327L, BusinessEvnEnum.PP.getAppId(), "测试用户" + RandomUtil.randomStringUpper(2));
        log.info("result: {}", JSONObject.toJSONString(result));
    }

    @Test
    public void getRuleBean(){
        String ruleJson = "{\"hallCount\":10}";
        OfficialCountRuleBean bean = activityRuleManager.getRuleBean(ActivityApplyRuleEnum.OFFICIAL_COUNT, ruleJson);
        log.info("bean: {}", JSONObject.toJSONString(bean));
    }

}
