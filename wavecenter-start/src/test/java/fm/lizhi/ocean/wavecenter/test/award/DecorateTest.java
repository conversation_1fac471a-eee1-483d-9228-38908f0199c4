package fm.lizhi.ocean.wavecenter.test.award;

import com.alibaba.druid.support.json.JSONUtils;
import com.alibaba.fastjson.JSONObject;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wavecenter.api.resource.constants.PlatformDecorateTypeEnum;
import fm.lizhi.ocean.wavecenter.infrastructure.resource.decorate.remote.DecorateRemote;
import fm.lizhi.ocean.wavecenter.infrastructure.resource.decorate.remote.request.RequestRecoverDecorate;
import fm.lizhi.ocean.wavecenter.infrastructure.resource.decorate.remote.request.RequestSendDecorate;
import fm.lizhi.ocean.wavecenter.test.AbstractDataCenterTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
public class DecorateTest extends AbstractDataCenterTest {

    @Autowired
    private DecorateRemote decorateRemote;


    @Test
    public void testSendDecorate() {
        ContextUtils.setBusinessEvnEnum(BusinessEvnEnum.HEI_YE);

        RequestSendDecorate request = new RequestSendDecorate();
        request.setDecorateType(PlatformDecorateTypeEnum.USER_GLORY);
        request.setDecorateId(5443407764613404799L);
        request.setDecorateNumber(1);
        request.setDecorateExpireTime(1);
        request.setUserId(1403348231454351490L);

        Result<Void> result = decorateRemote.sendDecorate(request);
        log.info("result: {}", JSONObject.toJSONString(result));
    }


    @Test
    public void testRecoverDecorate() {
        ContextUtils.setBusinessEvnEnum(BusinessEvnEnum.HEI_YE);
        RequestRecoverDecorate request = new RequestRecoverDecorate();
        request.setDecorateType(PlatformDecorateTypeEnum.USER_GLORY);
        request.setDecorateId(5443407764613404799L);
        request.setUserId(1403348231454351490L);

        Result<Void> result = decorateRemote.recoverDecorate(request);
        log.info("result: {}", JSONObject.toJSONString(result));
    }
}
