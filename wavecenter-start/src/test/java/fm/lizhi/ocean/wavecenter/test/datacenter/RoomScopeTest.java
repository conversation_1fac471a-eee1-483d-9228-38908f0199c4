package fm.lizhi.ocean.wavecenter.test.datacenter;

import com.google.common.collect.Lists;
import fm.lizhi.ocean.wavecenter.api.common.constants.MetricsEnum;
import fm.lizhi.ocean.wavecenter.common.utils.MyDateUtil;
import fm.lizhi.ocean.wavecenter.service.datacenter.manager.RoomDataManager;
import fm.lizhi.ocean.wavecenter.test.AbstractDataCenterTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/12/12 15:08
 */
@Slf4j
public class RoomScopeTest extends AbstractDataCenterTest {

    @Autowired
    private RoomDataManager roomDataManager;

    @Test
    public void testDay(){

        Map<String, String> result = roomDataManager.getRoomDayKeyIndicatorsSum(9637128
                , 5403375778987420287L
                , Lists.newArrayList(1457706187956045698L, 1346204443200407554L)
                , MyDateUtil.getDayValueDate(20241117)
                , Lists.newArrayList(
                        MetricsEnum.ALL_INCOME.getValue()
                        , MetricsEnum.SIGN_HALL_INCOME.getValue()
                        , MetricsEnum.PERSONAL_HALL_INCOME.getValue()
                        , MetricsEnum.OFFICIAL_HALL_INCOME.getValue()
                        , MetricsEnum.CHARM.getValue()
                )
        );
        System.out.println("result = " + result);


    }

}
