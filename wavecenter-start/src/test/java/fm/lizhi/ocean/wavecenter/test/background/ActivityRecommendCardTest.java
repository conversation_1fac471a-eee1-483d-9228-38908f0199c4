package fm.lizhi.ocean.wavecenter.test.background;

import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSONObject;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.bean.ActivityRecommendCardConfigBean;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestSaveActivityRecommendCard;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestUpdateActivityRecommendCard;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.service.ActivityRecommendCardConfigService;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.test.AbstractDataCenterTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

@Slf4j
public class ActivityRecommendCardTest extends AbstractDataCenterTest {

    @Autowired
    private ActivityRecommendCardConfigService activityRecommendCardService;


    @Test
    public void saveRecommendCard(){
        // 创建模拟参数
        RequestSaveActivityRecommendCard param = RequestSaveActivityRecommendCard.builder()
                .levelId(5409795320489968767L)
                .validDay(RandomUtil.randomInt(1, 10))
                .count(RandomUtil.randomInt(1, 10))
                .appId(BusinessEvnEnum.PP.getAppId())
                .operator("测试用户" + RandomUtil.randomStringUpper(2))
                .build();

        Result<Void> result = activityRecommendCardService.saveRecommendCard(param);
        log.info("result: {}", JSONObject.toJSONString(result));
    }

    @Test
    public void updateRecommendCard(){
        // 创建模拟参数
        RequestUpdateActivityRecommendCard param = RequestUpdateActivityRecommendCard.builder()
                .id(5410011316542768255L)
                .levelId(5409794953268167807L)
                .validDay(RandomUtil.randomInt(1, 10))
                .count(RandomUtil.randomInt(1, 10))
                .appId(BusinessEvnEnum.PP.getAppId())
                .operator("测试用户" + RandomUtil.randomStringUpper(2))
                .build();

        Result<Void> result = activityRecommendCardService.updateRecommendCard(param);
        log.info("result: {}", JSONObject.toJSONString(result));
    }

   @Test
    public void deleteRecommendCard(){
        Result<Void> result = activityRecommendCardService.deleteRecommendCard(5410011316542768255L, BusinessEvnEnum.PP.getAppId(), "测试用户" + RandomUtil.randomStringUpper(2));
        log.info("result: {}", JSONObject.toJSONString(result));
    }


    @Test
    public void listByAppId(){
        Result<List<ActivityRecommendCardConfigBean>> result = activityRecommendCardService.listByAppId(BusinessEvnEnum.PP.getAppId());
        log.info("result: {}", JSONObject.toJSONString(result));
    }

}
