package fm.lizhi.ocean.wavecenter.test.award;

import com.google.common.collect.Lists;
import fm.lizhi.common.job.dispatcher.executor.handler.JobExecuteContext;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.infrastructure.award.family.job.FamilyLevelAwardDeliverJob;
import fm.lizhi.ocean.wavecenter.infrastructure.award.family.job.FamilyLevelAwardDeliverJobParam;
import fm.lizhi.ocean.wavecenter.test.AbstractDataCenterTest;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/3/28 15:51
 */
public class FamilyLevelAwardDeliverJobTest extends AbstractDataCenterTest {

    @Autowired
    private FamilyLevelAwardDeliverJob job;

    @Test
    public void test() throws Exception {
        while (true) {
            FamilyLevelAwardDeliverJobParam en = new FamilyLevelAwardDeliverJobParam();
            en.setAppIds(Lists.newArrayList(BusinessEvnEnum.XIMI.getAppId()));
            en.setAwardStartTime(new Date(1742140800000L));
            JobExecuteContext param = new JobExecuteContext();
            param.setParam(JsonUtil.dumps(en));
            job.execute(param);
        }
    }

}
