package fm.lizhi.ocean.wavecenter.test.demo;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.util.DateUtil;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.RoomIncomeDetailBean;
import fm.lizhi.ocean.wavecenter.api.income.bean.SignRoomIncomeDetailParamBean;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.service.income.impl.IncomeGuildServiceImpl;
import fm.lizhi.ocean.wavecenter.test.AbstractDataCenterTest;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @date 2024/5/27 17:28
 */
public class GuildIncomeTest extends AbstractDataCenterTest {

    @Autowired
    private IncomeGuildServiceImpl incomeGuildService;

    @Test
    public void testSignRoom(){
        SignRoomIncomeDetailParamBean paramBean = new SignRoomIncomeDetailParamBean();
        paramBean.setAppId(BusinessEvnEnum.HEI_YE.appId());
        paramBean.setFamilyId(5318540755940148863L);
        paramBean.setPage(1);
        paramBean.setPageSize(100);
        paramBean.setStartDate(DateUtil.formatStrToNormalDate("2024-05-01 00:00:00"));
        paramBean.setEndDate(DateUtil.formatStrToNormalDate("2024-05-31 00:00:00"));
        Result<PageBean<RoomIncomeDetailBean>> restul = incomeGuildService.signRoomIncomeDetail(paramBean);
        PageBean<RoomIncomeDetailBean> target = restul.target();
        System.out.println("target = " + target);
    }

}
