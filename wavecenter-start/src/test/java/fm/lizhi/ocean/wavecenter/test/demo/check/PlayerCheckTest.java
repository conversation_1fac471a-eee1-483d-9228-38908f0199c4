package fm.lizhi.ocean.wavecenter.test.demo.check;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.util.DateUtil;
import fm.lizhi.ocean.wavecenter.api.live.bean.*;
import fm.lizhi.ocean.wavecenter.api.user.constants.SingStatusEnum;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.base.util.JsonUtils;
import fm.lizhi.ocean.wavecenter.service.live.impl.LivePlayerCheckInServiceImpl;
import fm.lizhi.ocean.wavecenter.test.AbstractDataCenterTest;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/11 17:30
 */
public class PlayerCheckTest extends AbstractDataCenterTest {

    @Autowired
    private LivePlayerCheckInServiceImpl playerCheckInService;

    @Test
    public void dayTest(){
        PlayerCheckDayStatsReq.PlayerCheckDayStatsReqBuilder builder = PlayerCheckDayStatsReq.builder().playerId(1L)
                .appId(BusinessEvnEnum.HEI_YE.appId())
                .familyId(5368832626096353407L)
                .roomId(1246552526099063810L)
                .playerId(1246552526099063810L)
                .singStatus(SingStatusEnum.SING)
                .startDay(DateUtil.formatStrToNormalDate("2024-06-21 00:00:00"))
                .endDay(DateUtil.formatStrToNormalDate("2024-06-25 23:59:59"));
        Result<List<PlayerCheckDayStatsBean>> result = playerCheckInService.dayStats(builder.build());
        List<PlayerCheckDayStatsBean> target = result.target();
        System.out.println("target = " + JsonUtils.toJsonStringLegacy(target));
    }

    @Test
    public void hourTest(){
        PlayerCheckHourStatsReq.PlayerCheckHourStatsReqBuilder builder = PlayerCheckHourStatsReq.builder().playerId(1L)
                .appId(BusinessEvnEnum.HEI_YE.appId())
                .familyId(5368832626096353407L)
                .roomId(1246552526099063810L)
                .playerId(1246552526099063810L)
                .singStatus(SingStatusEnum.SING)
                .startDay(DateUtil.formatStrToNormalDate("2024-06-21 00:00:00"))
                .endDay(DateUtil.formatStrToNormalDate("2024-06-25 23:59:59"));
        Result<List<PlayerCheckHourStatsDayBean>> result = playerCheckInService.hourStats(builder.build());
        List<PlayerCheckHourStatsDayBean> target = result.target();
        System.out.println("target = " + JsonUtils.toJsonStringLegacy(target));
    }

    @Test
    public void sumTest(){
        PlayerCheckHourStatsReq.PlayerCheckHourStatsReqBuilder builder = PlayerCheckHourStatsReq.builder().playerId(1L)
                .appId(BusinessEvnEnum.HEI_YE.appId())
                .familyId(5368832626096353407L)
                .roomId(1246552526099063810L)
                .playerId(1246552526099063810L)
                .singStatus(SingStatusEnum.SING)
                .startDay(DateUtil.formatStrToNormalDate("2024-06-21 00:00:00"))
                .endDay(DateUtil.formatStrToNormalDate("2024-06-25 23:59:59"));
        Result<PlayerCheckStatsSumBean> result = playerCheckInService.sum(builder.build());
        PlayerCheckStatsSumBean target = result.target();
        System.out.println("target = " + JsonUtils.toJsonStringLegacy(target));
    }

}
