package fm.lizhi.ocean.wavecenter.test.background;

import com.alibaba.fastjson.JSONObject;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.DecorateBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.DecorateEnum;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestGetDecorate;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.service.ActivityDecorateService;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.test.AbstractDataCenterTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
public class ActivityDecorateTest extends AbstractDataCenterTest {

    @Autowired
    private ActivityDecorateService activityDecorateService;


    @Test
    public void getDecorateList(){
        // 创建模拟参数
        /*RequestGetDecorate request = RequestGetDecorate.builder().appId(BusinessEvnEnum.XIMI.getAppId())
                .type(DecorateEnum.AVATAR.getType())
                .pageNo(1)
                .pageSize(20)
                .build();

        Result<PageBean<DecorateBean>> result = activityDecorateService.getDecorateList(request);
        log.info("AVATAR result1: {}", JSONObject.toJSONString(result));*/

        // 创建模拟参数
        /*RequestGetDecorate request2 = RequestGetDecorate.builder().appId(BusinessEvnEnum.HEI_YE.getAppId())
                .type(DecorateEnum.AVATAR.getType())
                .pageNo(1)
                .pageSize(20)
                .build();

        Result<PageBean<DecorateBean>> result2 = activityDecorateService.getDecorateList(request2);
        log.info("AVATAR result2: {}", JSONObject.toJSONString(result2));
*/
        // 创建模拟参数
      /*  RequestGetDecorate request3 = RequestGetDecorate.builder().appId(BusinessEvnEnum.XIMI.getAppId())
                .type(DecorateEnum.BACKGROUND.getType())
                .pageNo(1)
                .pageSize(20)
                .build();

        Result<PageBean<DecorateBean>> result3 = activityDecorateService.getDecorateList(request3);
        log.info("BACKGROUND request3: {}", JSONObject.toJSONString(result3));*/

        // 创建模拟参数
       /* RequestGetDecorate request4 = RequestGetDecorate.builder().appId(BusinessEvnEnum.HEI_YE.getAppId())
                .type(DecorateEnum.BACKGROUND.getType())
                .pageNo(1)
                .pageSize(20)
                .build();

        Result<PageBean<DecorateBean>> result4 = activityDecorateService.getDecorateList(request4);
        log.info("BACKGROUND result4: {}", JSONObject.toJSONString(result4));*/

        // 创建模拟参数
       RequestGetDecorate request5 = RequestGetDecorate.builder().appId(BusinessEvnEnum.PP.getAppId())
                .type(DecorateEnum.AVATAR.getType())
                .pageNo(1)
                .pageSize(20)
                .build();

        Result<PageBean<DecorateBean>> result5 = activityDecorateService.getDecorateList(request5);
        log.info("AVATAR result5: {}", JSONObject.toJSONString(result5));
    }

}
