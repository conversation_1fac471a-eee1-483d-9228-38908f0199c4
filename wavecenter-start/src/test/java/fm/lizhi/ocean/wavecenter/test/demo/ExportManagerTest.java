//package fm.lizhi.ocean.wavecenter.test.demo;
//
//import com.alibaba.excel.EasyExcel;
//import com.alibaba.excel.ExcelWriter;
//import com.alibaba.excel.write.metadata.WriteSheet;
//import fm.lizhi.ocean.wavecenter.api.datacenter.bean.FileExportRecordBean;
//import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
//import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.entity.WcFileExportRecord;
//import fm.lizhi.ocean.wavecenter.test.AbstractDataCenterTest;
//import org.junit.Test;
//import org.springframework.beans.factory.annotation.Autowired;
//
//import java.util.ArrayList;
//import java.util.Date;
//import java.util.List;
//
//
//public class ExportManagerTest extends AbstractDataCenterTest {
//
//   /* @Autowired
//    private ExportManager exportManager;
//
//
//    @Test
//    public void createTask(){
//
//        FileExportRecordBean recordDto = exportManager.createTask(BusinessEvnEnum.PP.getAppId(), 123456L, "个人收益");
//        System.out.println(recordDto);
//    }
//
//
//    @Test
//    public void asyncExport(){
//        FileExportRecordBean recordDto = exportManager.createTask(BusinessEvnEnum.PP.getAppId(), 123456L,"个人收益1");
//
//        exportManager.asyncExport(recordDto.getId(), recordDto.getFileName(), WcFileExportRecord.class, excelWriterBuilder -> {
//            try(ExcelWriter excelWriter = excelWriterBuilder.build()){
//                for (int i = 0; i < 5; i++) {
//                    List<WcFileExportRecord> data = data();
//                    WriteSheet writeSheet = EasyExcel.writerSheet(i, recordDto.getFileName() + (i+1)).build();
//                    excelWriter.write(data, writeSheet);
//                }
//            }
//        });
//    }*/
//
//    private static List<WcFileExportRecord> data() {
//
//        List<WcFileExportRecord> data = new ArrayList<>();
//        for (int i = 0; i < 10000; i++) {
//            WcFileExportRecord record = new WcFileExportRecord();
//            record.setId((long) i);
//            record.setAppId(i + 1001);
//            record.setFileStatus(i % 3 - 1);
//            record.setFilePath("/path/to/file_" + (i + 1));
//            record.setFileName("file_" + (i + 1) + ".xlsx");
//            record.setCreateTime(new Date());
//            record.setModifyTime(new Date());
//            data.add(record);
//        }
//
//        return data;
//    }
//}
