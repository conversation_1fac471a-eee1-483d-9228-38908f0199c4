package fm.lizhi.ocean.wavecenter.test.background;

import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSONObject;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.bean.ActivityLevelConfigBean;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestSaveActivityLevel;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestUpdateActivityLevel;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.service.ActivityLevelConfigService;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.test.AbstractDataCenterTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

@Slf4j
public class ActivityLevelTest extends AbstractDataCenterTest {

    @Autowired
    private ActivityLevelConfigService activityLevelService;


    @Test
    public void saveLevel(){
        // 创建模拟参数
        RequestSaveActivityLevel param = RequestSaveActivityLevel.builder()
                .appId(BusinessEvnEnum.PP.getAppId())
                .level("测试等级" + RandomUtil.randomStringUpper(2))
//                .level("测试等级B")
                .operator("测试用户" + RandomUtil.randomNumbers(2))
                .build();

        // 调用 saveLevel 方法
        Result<Boolean> result = activityLevelService.saveLevel(param);
        log.info("result: {}", JSONObject.toJSONString(result));
    }

    @Test
    public void updateLevel(){
        // 创建模拟参数
        RequestUpdateActivityLevel param = RequestUpdateActivityLevel.builder()
                .id(5409794244598563967L)
                .appId(BusinessEvnEnum.PP.getAppId())
                .level("测试等级B")
                .operator("测试用户")
                .build();

        // 调用 updateLevel 方法
        Result<Boolean> result = activityLevelService.updateLevel(param);
        log.info("result: {}", JSONObject.toJSONString(result));
    }

    @Test
    public void deleteLevel(){
        // 调用 updateLevel 方法
        Result<Boolean> result = activityLevelService.deleteLevel(5409794244598563967L, BusinessEvnEnum.PP.getAppId(), "测试用户");
        log.info("result: {}", JSONObject.toJSONString(result));
    }

    @Test
    public void listByAppId(){
        // 调用 updateLevel 方法
        Result<List<ActivityLevelConfigBean>> resultPp = activityLevelService.listByAppId(BusinessEvnEnum.PP.getAppId());
        log.info("resultPp : {}", JSONObject.toJSONString(resultPp));

        Result<List<ActivityLevelConfigBean>> resultXm = activityLevelService.listByAppId(BusinessEvnEnum.XIMI.getAppId());
        log.info("resultXm : {}", JSONObject.toJSONString(resultXm));
    }

}
