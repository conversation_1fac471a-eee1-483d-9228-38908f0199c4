package fm.lizhi.ocean.wavecenter.test.activitycenter;

import com.google.common.collect.Lists;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestBatchGetDecorate;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.service.ActivityDecorateService;
import fm.lizhi.ocean.wavecenter.test.AbstractDataCenterTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
public class ActivityDecorateTest extends AbstractDataCenterTest {

    @Autowired
    private ActivityDecorateService activityDecorator;

    @Test
    public void testDecorate(){
        RequestBatchGetDecorate.RequestBatchGetDecorateBuilder builder = RequestBatchGetDecorate.builder().appId(9637128)
                .ids(Lists.newArrayList(5394234535842025087L,5394234535842024575L));
        activityDecorator.batchGetDecorateList(builder.build());
    }


}
