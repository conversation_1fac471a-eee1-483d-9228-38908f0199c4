package fm.lizhi.ocean.wavecenter.test.activitycenter;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import fm.lizhi.common.job.dispatcher.executor.handler.JobExecuteContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.*;
import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityApplyTypeEnum;
import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.AutoConfigResourceEnum;
import fm.lizhi.ocean.wavecenter.api.activitycenter.request.RequestGetInTimeRangeActivityApply;
import fm.lizhi.ocean.wavecenter.api.activitycenter.response.ResponseActivityInfoDetail;
import fm.lizhi.ocean.wavecenter.api.activitycenter.service.ActivityApplyService;
import fm.lizhi.ocean.wavecenter.api.activitycenter.service.ActivityLevelService;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestActivityModifyBean;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.ResponseActivityModifyBean;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.service.ActivityAdminOperateService;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageParamBean;
import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.job.ActivityResourceTransferJob;
import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.job.SendReportDataMessageJob;
import fm.lizhi.ocean.wavecenter.test.AbstractDataCenterTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Slf4j
public class ActivityApplyTest extends AbstractDataCenterTest {

    @Autowired
    private ActivityApplyService activityApplyService;

    @Autowired
    private ActivityResourceTransferJob activityResourceTransferJob;

    @Autowired
    private ActivityLevelService activityLevelService;

    @Autowired
    private ActivityAdminOperateService activityAdminOperateService;
    @Autowired
    private SendReportDataMessageJob sendReportDataMessageJob;

    private static final String cdn = "https://cdnoffice.lizhi.fm";

    private static final Integer appId = 10919088;

    private static final String coverA = "https://cdnoffice.lizhi.fm/studio/2024/07/17/3088047624533417526.jpg";

    private static final String coverB = "https://cdnoffice.lizhi.fm/sociality/2024/07/24/3089282513531193404.png";

    private static final String bannerUrl = "https://cdnoffice.lizhi.fm/studio/2024/07/16/3087828780917070390.png";

    private static final String officialSeatUrl = "https://cdnoffice.lizhi.fm/studio/2024/09/23/3100636238102160950.png";

    @Test
    public void testSendReportDataMessageJob() throws Exception {
        sendReportDataMessageJob.execute(null);
    }


    @Test
    public void testGetInTimeRangeActivityApply(){
        while (true) {
            Result<List<ActivityApplyToolBean>> result = activityApplyService.getInTimeRangeActivityApply(new RequestGetInTimeRangeActivityApply()
                    .setAppId(BusinessEvnEnum.HEI_YE.getAppId())
                    .setNjId(1368684667099714306L)
                    .setStartTimeBeforeMinute(60)
                    .setEndTimeAfterMinute(60)
            );
            System.out.println("result.target() = " + result.target());
        }
    }

    //@Test
    public void testActivityApply() {
        Date startTime = new Date(1729778400000L);
        Date endTime = new Date(1729782000000L);
        List<ActivityProcessBean> list = new ArrayList<>();
        list.add(new ActivityProcessBean().setName("第一步").setDuration("60分钟").setExplanation("进行开场典礼"));
        list.add(new ActivityProcessBean().setName("第二步").setDuration("10分钟").setExplanation("重要人员讲话"));

        List<ActivityFlowResourceBean> flowResources = new ArrayList<>();
        FlowResourceExtra flowResourceExtra = new FlowResourceExtra()
                .setSeat(2).setStartTime(startTime.getTime()).setEndTime(endTime.getTime()).setColor("#FF50e3c2");

        ActivityFlowResourceBean programmeBean = new ActivityFlowResourceBean()
                .setResourceCode(AutoConfigResourceEnum.BANNER.getResourceCode())
                .setImageUrl(bannerUrl)
                .setResourceConfigId(5409982445770507391L)
                .setExtra(new FlowResourceExtra().setScale("6.92"));

        ActivityFlowResourceBean officialSeatBean = new ActivityFlowResourceBean()
                .setResourceCode(AutoConfigResourceEnum.OFFICIAL_SEAT.getResourceCode())
                .setImageUrl(officialSeatUrl)
                .setResourceConfigId(5409976948212368511L)
                .setExtra(flowResourceExtra);

        ActivityFlowResourceBean recCard = new ActivityFlowResourceBean()
                .setResourceCode(AutoConfigResourceEnum.REC_CARD.getResourceCode())
                .setResourceConfigId(5409982445770507482L);
        flowResources.add(programmeBean);
        flowResources.add(officialSeatBean);
        flowResources.add(recCard);

        List<String> imageUrlList = Lists.newArrayList(coverA, coverB);
        RequestActivityApplyBean applyBean = RequestActivityApplyBean.builder()
                .appId(appId)
                .name("PP资源发放测试")
                .applyType(ActivityApplyTypeEnum.NJ_APPLY)
                .activityTool(Lists.newArrayList(1))
                .classId(5409828172692718719L)
                .contact("运营小助手")
                .applicantUid(1386654939115492226L)
//                .avatarWidgetId(5410745871918695039L)
                .auxiliaryPropUrl(imageUrlList)
                .contactNumber("<EMAIL>")
                .goal("赚他几个亿")
                .introduction("大家努力搞钱")
                .posterUrl(coverA)
                .hostId(1386653092279532674L)
                .njId(1386654939115492226L)
                .accompanyNjIds(Lists.newArrayList(1386653092279532674L, 1386657103779033986L))
                .roomAnnouncement("大家快来我直播间搞钱啊")
//                .roomBackgroundId(5404084925234309759L)
                .startTime(startTime.getTime())
                .endTime(endTime.getTime())
                .templateId(1L)
                .roomAnnouncementImgUrl(Lists.newArrayList(coverA, coverB))
                .processList(list)
                .flowResources(flowResources)
                .build();
        String jsonString = JSONObject.toJSONString(applyBean);
        log.info("applyBean={}", jsonString);
        Result<Void> result = activityApplyService.activityApply(applyBean);
        log.info("resultCode={}, message={}", result.rCode(), result.getMessage());
    }

    @Test
    public void xmTestActivityApply() {
        Date startTime = new Date(1729778400000L);
        Date endTime = new Date(1729782000000L);
        Integer appId = 10919088;
        String posterUrl = "https://cdnoffice.lizhi.fm/studio/2024/07/17/3088047624533417526.jpg";
        String coverB = "https://cdnoffice.lizhi.fm/sociality/2024/07/24/3089282513531193404.png";
        String bannerUrl = "https://cdnoffice.lizhi.fm/studio/2024/07/17/3088047624533417526.jpg";
        String officialSeatUrl = "https://cdnoffice.lizhi.fm/studio/2024/04/10/3069798984951185974.png";

        List<ActivityProcessBean> list = new ArrayList<>();
        list.add(new ActivityProcessBean().setName("第一步").setDuration("5分钟").setExplanation("进行开场典礼"));
        list.add(new ActivityProcessBean().setName("第二步").setDuration("40分钟").setExplanation("重要人员讲话"));
        list.add(new ActivityProcessBean().setName("第二步").setDuration("10分钟").setExplanation("致辞"));

        List<ActivityFlowResourceBean> flowResources = new ArrayList<>();
        FlowResourceExtra flowResourceExtra = new FlowResourceExtra()
                .setSeat(1).setStartTime(startTime.getTime()).setEndTime(endTime.getTime());

        ActivityFlowResourceBean programmeBean = new ActivityFlowResourceBean()
                .setResourceCode(AutoConfigResourceEnum.BANNER.getResourceCode())
                .setImageUrl(bannerUrl)
                .setResourceConfigId(5409982445770507391L)
                .setExtra(new FlowResourceExtra().setScale("6.92"));

        /*ActivityFlowResourceBean officialSeatBean = new ActivityFlowResourceBean()
                .setResourceCode(AutoConfigResourceEnum.OFFICIAL_SEAT.getResourceCode())
                .setImageUrl(officialSeatUrl)
                .setResourceConfigId(5409976948212368511L)
                .setExtra(flowResourceExtra);*/

        ActivityFlowResourceBean recCard = new ActivityFlowResourceBean()
                .setResourceCode(AutoConfigResourceEnum.REC_CARD.getResourceCode())
                .setResourceConfigId(5409982445770507482L);

        ActivityFlowResourceBean resourceBean = new ActivityFlowResourceBean()
                .setImageUrl(posterUrl)
                .setResourceCode(AutoConfigResourceEnum.PROGRAMME.getResourceCode())
                .setResourceConfigId(5409982445770507392L);
        flowResources.add(programmeBean);
        //flowResources.add(officialSeatBean);
        flowResources.add(resourceBean);
        flowResources.add(recCard);

        List<String> imageUrlList = Lists.newArrayList(posterUrl, coverB);
        RequestActivityApplyBean applyBean = RequestActivityApplyBean.builder()
                .appId(appId)
                .name("西米资源发放测试")
                .applyType(ActivityApplyTypeEnum.NJ_APPLY)
                .activityTool(Lists.newArrayList(1))
                .classId(5409828172692718719L)
                .contact("xm运营小助手")
                .applicantUid(1386654939115492226L)
//                .avatarWidgetId(5410745871918695039L)
                .auxiliaryPropUrl(imageUrlList)
                .contactNumber("<EMAIL>")
                .goal("赚他几个亿")
                .introduction("大家努力搞钱")
                .posterUrl(posterUrl)
                .hostId(1386653092279532674L)
                .njId(1386654939115492226L)
                .accompanyNjIds(Lists.newArrayList(1386653092279532674L, 1386657103779033986L))
                .roomAnnouncement("大家快来我直播间搞钱啊")
//                .roomBackgroundId(5404084925234309759L)
                .startTime(startTime.getTime())
                .endTime(endTime.getTime())
                .templateId(1L)
                .roomAnnouncementImgUrl(Lists.newArrayList(posterUrl, coverB))
                .processList(list)
                .flowResources(flowResources)
                .build();
        String jsonString = JSONObject.toJSONString(applyBean);
        log.info("applyBean={}", jsonString);
        Result<Void> result = activityApplyService.activityApply(applyBean);
        log.info("resultCode={}, message={}", result.rCode(), result.getMessage());
    }

    @Test
    public void testModify() {
        String modifyJsonStr = "{\"accompanyNjIds\":[],\"activityId\":5415908888614543487,\"activityTool\":[1,2,3],\"appId\":10919088,\"applicantUid\":1386653158851527810,\"applyType\":\"NJ_APPLY\",\"auxiliaryPropUrl\":[\"http://romefs.yfxn.lizhi.fm/public/wave/f6fbc1c1b9f80809cfaa1463976f472a.png\"],\"avatarWidgetId\":5414436328989277823,\"classId\":5415915569438340223,\"contact\":\"修改后\",\"contactNumber\":\"修改后\",\"endTime\":1731665857000,\"flowResources\":[{\"extra\":{\"color\":\"#4d2794\",\"scale\":\"1\"},\"imageUrl\":\"http://romefs.yfxn.lizhi.fm/public/wave/a6d76d9716d27926f18301c8c678a748.png\",\"resourceCode\":\"programme\",\"resourceConfigId\":5413537153963624063}],\"goal\":\"修改后\",\"introduction\":\"修改后\",\"name\":\"修改3333\",\"njId\":1386653158851527810,\"posterUrl\":\"http://romefs.yfxn.lizhi.fm/public/wave/0c068ac24b4cd564bcc7bfaa120cd0e2.png\",\"processList\":[{\"duration\":\"修改后\",\"explanation\":\"修改后\",\"name\":\"修改后\"},{\"duration\":\"10\",\"explanation\":\"环节 2\",\"name\":\"环节 2\"}],\"roomAnnouncement\":\"修改后\",\"roomBackgroundId\":5414436328989280383,\"startTime\":1731655057000,\"templateId\":5415915569438344319,\"version\":0}";
        RequestActivityApplyBean modifyBean = JSONObject.parseObject(modifyJsonStr, RequestActivityApplyBean.class);
        Result<Void> result = activityApplyService.activityModify(modifyBean);
        log.info("resultCode={}, message={}", result.rCode(), result.getMessage());
    }

    @Test
    public void testModify2() {
        String modifyJsonStr = "{\"njId\":\"1387000600230892802\",\"contact\":\"萨达\",\"contactNumber\":\"阿斯达\",\"accompanyNjIds\":[],\"name\":\"活动测试修改-2\",\"classId\":\"5415915569438399103\",\"goal\":\"官频位时间测试\",\"introduction\":\"官频位时间测试\",\"activityTool\":[],\"roomAnnouncement\":\"\",\"processList\":[{\"name\":\"开场\",\"duration\":\"1\",\"explanation\":\"官频位时间测试\"}],\"auxiliaryPropUrl\":[],\"roomAnnouncementImgUrl\":[],\"roomBackgroundId\":\"5399428570804295807\",\"avatarWidgetId\":\"5413345473869475455\",\"flowResources\":[{\"resourceConfigId\":\"5417433595562184831\",\"resourceCode\":\"official_seat\",\"imageUrl\":\"http://romefs.yfxn.lizhi.fm/public/wave/6554a2909f2ebc9a3eac820582e6f2ea.png\",\"extra\":{\"color\":\"#b33232\",\"scale\":\"0.8481675392670157\",\"seat\":2,\"startTime\":1739556000000,\"endTime\":1739566800000}},{\"resourceConfigId\":\"5417433595562186367\",\"resourceCode\":\"programme\",\"imageUrl\":\"http://romefs.yfxn.lizhi.fm/public/wave/6554a2909f2ebc9a3eac820582e6f2ea.png\",\"extra\":{\"color\":\"#b33232\",\"scale\":\"0.848167539267015\",\"seat\":2,\"startTime\":1739556000000,\"endTime\":1739566800000}}],\"templateId\":\"5420773147385312895\",\"startTime\":1739556000000,\"endTime\":1739566800000,\"activityId\":\"5433017759841663615\",\"version\":2,\"applyType\":1,\"applicantUid\":\"1387000600230892802\"}";
        RequestActivityModifyBean modifyBean = JSONObject.parseObject(modifyJsonStr, RequestActivityModifyBean.class);
        modifyBean.setAppId(9637128);
        modifyBean.setApplyType(ActivityApplyTypeEnum.OFFICIAL_APPLY);
        Result<ResponseActivityModifyBean> result = activityAdminOperateService.adminModifyActivity(modifyBean);
        log.info("resultCode={}, message={}", result.rCode(), result.getMessage());
    }


    @Test
    public void queryActivityInfoDetail() {
        Result<ResponseActivityInfoDetail> result = activityApplyService.queryActivityInfoDetail(5413169294309783679L, 10919088);
        log.info("result: {}", JSONObject.toJSONString(result));
    }

    @Test
    public void getFlowResourceAuditResult() {
        Result<List<ActivityFlowResourceDetailBean>> result = activityApplyService.getFlowResourceAuditResult(5411864055920985727L, 10919088);
        log.info("result: {}", JSONObject.toJSONString(result));
    }

    @Test
    public void getUserActivites() {
        PageParamBean pageParamBean = PageParamBean.builder().pageNo(1).pageSize(20).build();
        Result<ResponseQueryUserActivitiesBean> responseQueryUserActivitiesBeanResult =
                activityApplyService.queryUserActivities(new RequestQueryUserActivitiesBean()
                        .setAppId(10919088).setMaxStartTime(1731143244000L).setMinStartTime(1729933644000L).setPageParam(pageParamBean));
        if (responseQueryUserActivitiesBeanResult.rCode() != 0) {
            log.error("error: {}", responseQueryUserActivitiesBeanResult.getMessage());
            return;
        }
        log.info("result: {}", JSONObject.toJSONString(responseQueryUserActivitiesBeanResult.target()));
    }

    @Test
    public void agreeActivityApply() {
        //资源status： 0：待审核，1：不发放，2：可发放
        // 活动status：1 - 等待审核 2 - 审核通过 3 - 审核拒绝 fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityAuditStatusEnum
        ActivityFlowResourceAuditBean activityFlowResourceAuditBean =
                new ActivityFlowResourceAuditBean().setFlowResourceId(5413135097784203391L).setStatus(2);
        ActivityFlowResourceAuditBean activityFlowResourceAuditBean2 =
                new ActivityFlowResourceAuditBean().setFlowResourceId(5413135097784205951L).setStatus(2);
        ActivityFlowResourceAuditBean activityFlowResourceAuditBean3 =
                new ActivityFlowResourceAuditBean().setFlowResourceId(5413321145019564159L).setStatus(2);

        RequestActivityAuditAgreeBean requestActivityAuditAgreeBean = new RequestActivityAuditAgreeBean();
        requestActivityAuditAgreeBean.setAppId(9637128);
        requestActivityAuditAgreeBean.setActivityId(5413719653560091775L);
        requestActivityAuditAgreeBean.setFlowResourceList(Lists.newArrayList(activityFlowResourceAuditBean, activityFlowResourceAuditBean2));
        //Result<String> agreeResult = activityApplyService.agreeActivityApply(requestActivityAuditAgreeBean);
        //log.info("result: {}", JSONObject.toJSONString(agreeResult));
    }

    @Test
    public void queryActivities() {
        List<Integer> status = Lists.newArrayList(1,3);
        Result<ResponseQueryActivityListBean> result = activityApplyService.queryActivityList(RequestQueryActivityListBean.builder()
                .appId(10919088).minStartTime(1735056030000L).maxStartTime(1735488030000L).pageParam(PageParamBean.builder().pageNo(1).pageSize(20).build()).build());
        log.info("result: {}", JSONObject.toJSONString(result));
    }

    @Test
    public void testJob() throws Exception {
        JobExecuteContext jobExecuteContext = new JobExecuteContext();
        activityResourceTransferJob.execute(jobExecuteContext);
    }

    @Test
    public void testActivityLevel() {
        Result<List<ActivityLevelInfoBean>> result = activityLevelService.listByAppId(10919088);
        log.info("result: {}", JSONObject.toJSONString(result));
    }

    public static void main(String[] args) {
        Date startTime = new Date(1729778400000L);
        Date endTime = new Date(1729782000000L);
        Integer appId = 10919088;
        String posterUrl = "https://cdnoffice.lizhi.fm/studio/2024/07/17/3088047624533417526.jpg";
        String coverB = "https://cdnoffice.lizhi.fm/sociality/2024/07/24/3089282513531193404.png";
        String bannerUrl = "https://cdnoffice.lizhi.fm/studio/2024/07/17/3088047624533417526.jpg";
        String officialSeatUrl = "https://cdnoffice.lizhi.fm/studio/2024/04/10/3069798984951185974.png";
        List<ActivityProcessBean> list = new ArrayList<>();
        list.add(new ActivityProcessBean().setName("第一步").setDuration("5分钟").setExplanation("进行开场典礼"));
        list.add(new ActivityProcessBean().setName("第二步").setDuration("40分钟").setExplanation("重要人员讲话"));
        list.add(new ActivityProcessBean().setName("第二步").setDuration("10分钟").setExplanation("致辞"));

        List<ActivityFlowResourceBean> flowResources = new ArrayList<>();
        FlowResourceExtra flowResourceExtra = new FlowResourceExtra()
                .setSeat(1).setStartTime(startTime.getTime()).setEndTime(endTime.getTime());

        ActivityFlowResourceBean programmeBean = new ActivityFlowResourceBean()
                .setResourceCode(AutoConfigResourceEnum.BANNER.getResourceCode())
                .setImageUrl(bannerUrl)
                .setResourceConfigId(5409982445770507391L)
                .setExtra(new FlowResourceExtra().setScale("6.92"));

        ActivityFlowResourceBean officialSeatBean = new ActivityFlowResourceBean()
                .setResourceCode(AutoConfigResourceEnum.OFFICIAL_SEAT.getResourceCode())
                .setImageUrl(officialSeatUrl)
                .setResourceConfigId(5409976948212368511L)
                .setExtra(flowResourceExtra);

        ActivityFlowResourceBean recCard = new ActivityFlowResourceBean()
                .setResourceCode(AutoConfigResourceEnum.REC_CARD.getResourceCode())
                .setResourceConfigId(5409982445770507482L);

        ActivityFlowResourceBean resourceBean = new ActivityFlowResourceBean()
                .setImageUrl(posterUrl)
                .setResourceCode(AutoConfigResourceEnum.PROGRAMME.getResourceCode())
                .setResourceConfigId(5409982445770507392L);
        flowResources.add(programmeBean);
        flowResources.add(officialSeatBean);
        flowResources.add(resourceBean);
        flowResources.add(recCard);
        System.out.println(JSONObject.toJSONString(list));
        System.out.println(JSONObject.toJSONString(flowResources));
    }




}
