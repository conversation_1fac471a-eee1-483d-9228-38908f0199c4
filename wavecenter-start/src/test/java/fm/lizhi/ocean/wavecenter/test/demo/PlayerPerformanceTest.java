package fm.lizhi.ocean.wavecenter.test.demo;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.common.constants.MetricsEnum;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.*;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.service.datacenter.impl.GuildDataServiceImpl;
import fm.lizhi.ocean.wavecenter.service.datacenter.impl.PlayerDataServiceImpl;
import fm.lizhi.ocean.wavecenter.service.datacenter.impl.RoomDataServiceImpl;
import fm.lizhi.ocean.wavecenter.test.AbstractDataCenterTest;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @date 2024/5/23 15:15
 */
public class PlayerPerformanceTest extends AbstractDataCenterTest {

    @Autowired
    private RoomDataServiceImpl roomDataService;

    private Long hyFamilyId = 5318540755940148863L;
    private Long hyRoomId = 1368303752892663170L;
    private Long hyPlayerId = 1368304803012168450L;

    private Long xmFamilyId = 5246373908659044991L;
    private Long xmRoomId = 1291847566882786178L;



    @Test
    public void testHyPerformance(){
        GetRoomPlayerPerformanceBean paramBean = GetRoomPlayerPerformanceBean.builder()
                .familyId(5318540755940148863L).roomId(1368303752892663170L).appId(57333013)
                .orderMetricsStr(MetricsEnum.CURR_INCOME.getValue()).build();

        Result<RoomPlayerPerformanceResBean> heRes = roomDataService.getPlayerPerformance(paramBean);
        RoomPlayerPerformanceResBean target = heRes.target();
        System.out.println("target = " + target);
    }

    @Test
    public void testXmPerformance() {
        GetRoomPlayerPerformanceBean paramBean = GetRoomPlayerPerformanceBean.builder()
                .familyId(5246373908659044991L).roomId(1291847566882786178L).appId(9637128)
                .orderMetricsStr(MetricsEnum.CURR_INCOME.getValue()).build();

        Result<RoomPlayerPerformanceResBean> heRes = roomDataService.getPlayerPerformance(paramBean);
        RoomPlayerPerformanceResBean target = heRes.target();
        System.out.println("target = " + target);
    }



}
