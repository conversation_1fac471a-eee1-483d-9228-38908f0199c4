package fm.lizhi.ocean.wavecenter.test.datacenter;

import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.infrastructure.common.constants.PayTenantCodeEnum;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.kafka.consumer.AccountFlowMsg;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.kafka.consumer.PayAccountFlowConsumer;
import fm.lizhi.ocean.wavecenter.service.datacenter.constants.AccountOpEnum;
import fm.lizhi.ocean.wavecenter.test.AbstractDataCenterTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @date 2025/4/24 16:33
 */
@Slf4j
public class PayConsumerTest extends AbstractDataCenterTest {

    @Autowired
    private PayAccountFlowConsumer payAccountFlowConsumer;

    @Test
    public void testPp(){
        while (true) {
            try {
                roomConsumer();
                playerConsumer();
            }catch (Exception e) {
                log.error("error:", e);
            }
        }
    }

    private void roomConsumer() {
        // 测试正常插入
        AccountFlowMsg msg = new AccountFlowMsg();
        msg.setAccountCode("200_300_2202001001004001_LIZHI");
        msg.setAccountOpType(AccountOpEnum.PLUS.getValue());
        msg.setAmount("1000");
        msg.setBizId(2);
        msg.setAppId(String.valueOf(BusinessEvnEnum.PP.getAppId()));
        msg.setBookkeepingDate("2025-04-24");
        msg.setBusinessNo("111111");
        msg.setId(1L);
        msg.setIdentity("1386653158851527810");
        msg.setTenantCode(PayTenantCodeEnum.pp.getPayTenantCode());
        msg.setTradeTime("2025-04-24");
        payAccountFlowConsumer.handleAccountFlow(JsonUtil.dumps(msg));

        // 测试冲突
        payAccountFlowConsumer.handleAccountFlow(JsonUtil.dumps(msg));

        // 测试更新
        msg.setId(2L);
        payAccountFlowConsumer.handleAccountFlow(JsonUtil.dumps(msg));

        // 测试插入第二条
        msg.setIdentity("1386652950545609346");
        msg.setId(3L);
        payAccountFlowConsumer.handleAccountFlow(JsonUtil.dumps(msg));
    }

    private void playerConsumer() {
        // 测试正常插入
        AccountFlowMsg msg = new AccountFlowMsg();
        msg.setAccountCode("600_300_8001504001_DIAMOND");
        msg.setAccountOpType(AccountOpEnum.PLUS.getValue());
        msg.setAmount("1000");
        msg.setBizId(2);
        msg.setAppId(String.valueOf(BusinessEvnEnum.HEI_YE.getAppId()));
        msg.setBookkeepingDate("2025-04-24");
        msg.setBusinessNo("111111");
        msg.setId(4L);
        msg.setIdentity("1368304803012168450");
        msg.setTenantCode(PayTenantCodeEnum.heiye.getPayTenantCode());
        msg.setTradeTime("2025-04-24");
        payAccountFlowConsumer.handleAccountFlow(JsonUtil.dumps(msg));

        // 测试冲突
        payAccountFlowConsumer.handleAccountFlow(JsonUtil.dumps(msg));

        // 测试更新
        msg.setId(5L);
        payAccountFlowConsumer.handleAccountFlow(JsonUtil.dumps(msg));

        // 测试插入第二条
        msg.setIdentity("1368473270956880130");
        msg.setId(6L);
        payAccountFlowConsumer.handleAccountFlow(JsonUtil.dumps(msg));
    }

}
