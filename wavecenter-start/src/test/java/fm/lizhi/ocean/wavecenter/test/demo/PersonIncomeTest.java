package fm.lizhi.ocean.wavecenter.test.demo;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.commons.util.DateUtil;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.income.bean.*;
import fm.lizhi.ocean.wavecenter.infrastructure.income.datastore.mapper.XmFamilyNjSettleRatioMapper;
import fm.lizhi.ocean.wavecenter.service.income.impl.IncomePlayerServiceImpl;
import fm.lizhi.ocean.wavecenter.test.AbstractDataCenterTest;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;

public class PersonIncomeTest extends AbstractDataCenterTest {



    @Autowired
    private XmFamilyNjSettleRatioMapper xmFamilyNjSettleRatioMapper;

    @Autowired
    private IncomePlayerServiceImpl incomePlayerService;

    @Test
    public void ratioTest(){
        Long playerIncome = xmFamilyNjSettleRatioMapper.getPlayerIncome(1298846598343704834L);
        System.out.println(playerIncome);
    }

    @Test
    public void testGetPersonalIncomeDetail() {

        GetPersonalIncomeDetailParamBean paramBean = GetPersonalIncomeDetailParamBean.builder()
                .appId(57333013)
                .flushTime(System.currentTimeMillis())
                .endDate(new Date())
                .startDate(new Date(System.currentTimeMillis()-1000 * 60 * 60 * 24 * 60 ))
                .userId(1405572979416806274L)
                .pageNo(1)
                .pageSize(20)
                .build();

        Result<PageBean<PersonalIncomeDetailBean>> result = incomePlayerService.getPersonalIncomeDetail(paramBean);
        assert result.rCode() == GeneralRCode.GENERAL_RCODE_SUCCESS;
    }

    @Test
    public void testGetPersonalIncomeDetailOut() {
        GetPersonalIncomeDetailParamBean paramBean = GetPersonalIncomeDetailParamBean.builder()
                .appId(9637128)
                .flushTime(System.currentTimeMillis())
                .endDate(new Date())
                .startDate(DateUtil.getDayBefore(new Date(), 60))
                .userId(1405572979416806274L)
                .pageNo(1)
                .pageSize(20)
                .build();

        Result<PageBean<PersonalIncomeDetailBean>> result = incomePlayerService.getPersonalIncomeDetailOut(paramBean);
        assert result.rCode() == GeneralRCode.GENERAL_RCODE_SUCCESS;
    }

    @Test
    public void testGetPersonalIncomeDetailSum() {
        GetPersonalIncomeDetailSumParamBean paramBean = GetPersonalIncomeDetailSumParamBean.builder()
                .appId(9637128)
                .endDate(new Date())
                .startDate(DateUtil.getDayBefore(new Date(), 60))
                .userId(1389069964865963778L)
                .build();
        Result<PersonalIncomeDetailSumBean> result = incomePlayerService.getPersonalIncomeDetailSum(paramBean);
        assert result.rCode() == GeneralRCode.GENERAL_RCODE_SUCCESS;
    }


    @Test
    public void testPlayerSum() {
        int appId = 9637128;
        long userId = 1405572979416806274L;

        Result<PlayerSumResBean> result = incomePlayerService.playerSum(appId, userId);
        assert result.rCode() == GeneralRCode.GENERAL_RCODE_SUCCESS;
    }

    @Test
    public void testPlayerPersonalGiftflow() {
        PlayerPersonalGiftflowParamBean paramBean = PlayerPersonalGiftflowParamBean.builder()
                .appId(9637128)
                .endDate(new Date())
                .startDate(DateUtil.getDayBefore(new Date(), 60))
                .pageNo(1)
                .userId(1359741209454761346L)
                .pageSize(20)
                .build();

        Result<PageBean<PersonalGiftflowBean>> result = incomePlayerService.playerPersonalGiftflow(paramBean);
        assert result.rCode() == GeneralRCode.GENERAL_RCODE_SUCCESS;
    }

    @Test
    public void testPlayerPersonalGiftflowSum() {
        PlayerPersonalGiftflowParamBean paramBean = PlayerPersonalGiftflowParamBean.builder()
                .appId(9637128)
                .endDate(new Date())
                .startDate(DateUtil.getDayBefore(new Date(), 60))
                .pageNo(1)
                .pageSize(20)
                .build();

        Result<PersonalGiftflowBean> result = incomePlayerService.playerPersonalGiftflowSum(paramBean);
        assert result.rCode() == GeneralRCode.GENERAL_RCODE_SUCCESS;
    }

    @Test
    public void testPlayerRoomGiftflow() {
        // Set paramBean attributes as needed
        PlayerRoomGiftflowParamBean paramBean = PlayerRoomGiftflowParamBean.builder()
                .appId(9637128)
                .endDate(new Date())
                .startDate(DateUtil.getDayBefore(new Date(), 60))
                .pageNo(1)
                .pageSize(20)
                .userId(1359741209454761346L)
                .build();

        Result<PageBean<PlayerRoomGiftflowBean>> result = incomePlayerService.playerRoomGiftflow(paramBean);
        assert result.rCode() == GeneralRCode.GENERAL_RCODE_SUCCESS;
    }

    @Test
    public void testPlayerRoomGiftflowSum() {
        PlayerRoomGiftflowParamBean paramBean = PlayerRoomGiftflowParamBean.builder()
                .appId(9637128)
                .endDate(new Date())
                .startDate(DateUtil.getDayBefore(new Date(), 60))
                .pageNo(1)
                .pageSize(20)
                .userId(1359741209454761346L)
                .build();
        Result<PlayerRoomGiftflowBean> result = incomePlayerService.playerRoomGiftflowSum(paramBean);
        assert result.rCode() == GeneralRCode.GENERAL_RCODE_SUCCESS;
    }
//
//    @Test
//    public void testGetRevenueIncomeDetail() {
//        GetPersonalIncomeDetailParamBean paramBean = new GetPersonalIncomeDetailParamBean();
//        // Set paramBean attributes as needed
//
//        Result<PageBean<PersonalIncomeDetailBean>> result = incomePlayerService.getRevenueIncomeDetail(paramBean);
//        assert result.rCode() == GeneralRCode.GENERAL_RCODE_SUCCESS;
//    }
//
//    @Test
//    public void testGetRevenueIncomeDetailOut() {
//        GetPersonalIncomeDetailParamBean paramBean = new GetPersonalIncomeDetailParamBean();
//        // Set paramBean attributes as needed
//
//        Result<PageBean<PersonalIncomeDetailBean>> result = incomePlayerService.getRevenueIncomeDetailOut(paramBean);
//        assert result.rCode() == GeneralRCode.GENERAL_RCODE_SUCCESS;
//    }
//
//    @Test
//    public void testGetRevenueIncomeDetailSum() {
//        GetPersonalIncomeDetailSumParamBean paramBean = new GetPersonalIncomeDetailSumParamBean();
//        // Set paramBean attributes as needed
//
//        Result<PersonalIncomeDetailSumBean> result = incomePlayerService.getRevenueIncomeDetailSum(paramBean);
//        assert result.rCode() == GeneralRCode.GENERAL_RCODE_SUCCESS;
//    }


}
