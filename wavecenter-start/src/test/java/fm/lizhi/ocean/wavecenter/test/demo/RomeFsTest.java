package fm.lizhi.ocean.wavecenter.test.demo;

import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.common.config.CommonConfig;
import fm.lizhi.ocean.wavecenter.common.config.RomeConfig;
import fm.lizhi.ocean.wavecenter.infrastructure.common.convert.FileManagerConvert;
import fm.lizhi.ocean.wavecenter.service.common.manager.RomeFsManager;
import fm.lizhi.ocean.wavecenter.service.common.param.RomeFsTransferParam;
import fm.lizhi.ocean.wavecenter.service.common.result.RomeFsPutResult;
import fm.lizhi.ocean.wavecenter.test.AbstractDataCenterTest;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @date 2024/4/26 20:29
 */
public class RomeFsTest extends AbstractDataCenterTest {


    @Autowired
    private RomeFsManager romeFsManager;

    @Autowired
    private CommonConfig commonConfig;


    @Test
    public void uploadTransfer(){

        RomeConfig romeConfig = commonConfig.getBizConfig(BusinessEvnEnum.PP.getAppId()).getRomeConfig();

        RomeFsTransferParam param = new RomeFsTransferParam();
        param.setSourceUrl("http://liverecordin-cnhl.lizhilive.com/Xiaoximi/5401160129869285942/5402160127125076607/20241108/Xiaoximi_5401160129869285942_5402160127125076607_1823475765_199712_1_1731054164000.aac");
        param.setAccessModifier(romeConfig.getAccessModifier());
        param.setRomeFsConfig(FileManagerConvert.I.convertRomeFsConfig(romeConfig));


        RomeFsPutResult result = romeFsManager.uploadTransfer(param);
        System.out.println(result);
    }

}
