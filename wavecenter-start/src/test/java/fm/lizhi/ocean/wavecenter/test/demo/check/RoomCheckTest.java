package fm.lizhi.ocean.wavecenter.test.demo.check;

import com.alibaba.fastjson.JSONObject;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.util.DateUtil;
import fm.lizhi.ocean.wavecenter.api.live.bean.*;
import fm.lizhi.ocean.wavecenter.api.live.service.LiveRoomCheckInService;
import fm.lizhi.ocean.wavecenter.test.AbstractDataCenterTest;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;

public class RoomCheckTest extends AbstractDataCenterTest {


    @Autowired
    private LiveRoomCheckInService roomCheckInService;

    @Test
    public void roomDayStats() {

        Date endDate = new Date();
        Date startDate = new Date(endDate.getTime() - 10000000000L);

        RoomDayCheckStatsReq statsReq = RoomDayCheckStatsReq.builder()
                .appId(10919088)
                .startDate(startDate)
                .endDate(endDate)
                .roomId(1386652551113644802L)
                .build();
//        Result<PageBean<RoomDayStatsRes>> result = roomCheckInService.roomDayStats(statsReq);
//        PageBean<RoomDayStatsRes> target = result.target();
//        System.out.println(JSONObject.toJSONString(target));
    }


    @Test
    public void roomHourStats() {

        Date startDate = DateUtil.formatStrToDate("2024-06-25 00:00:00", DateUtil.datetime_2);
        Date endDate = DateUtil.formatStrToDate("2024-06-25 23:59:59", DateUtil.datetime_2);

        RoomHourCheckStatsReq statsReq = RoomHourCheckStatsReq.builder()
                .appId(10919088)
                .startDate(startDate)
                .endDate(endDate)
                .roomId(1432681790786810498L)
                .build();
//        Result<List<RoomHourStatsRes>> result = roomCheckInService.roomHourStats(statsReq);
//        List<RoomHourStatsRes> target = result.target();
//        System.out.println(JSONObject.toJSONString(target));
    }

    @Test
    public void roomCalendar() {

        Date endDate = new Date();
        Date startDate = new Date(endDate.getTime() - 1000000000);

        RoomDayCalendarReq statsReq = RoomDayCalendarReq.builder()
                .appId(9637128)
                .startDate(startDate)
                .endDate(endDate)
                .roomId(1293318642573913090L)
                .build();
        Result<RoomDayCalendarRes> result = roomCheckInService.roomCalendar(statsReq);
        RoomDayCalendarRes target = result.target();
        System.out.println(JSONObject.toJSONString(target));
    }


    @Test
    public void roomHourDetail() {

        Date endDate = DateUtil.formatStrToDate("2024-06-25 00:00:00", DateUtil.datetime_2);
        Date startDate = DateUtil.formatStrToDate("2024-06-25 17:00:00", DateUtil.datetime_2);

        RoomHourCheckDetailReq statsReq = RoomHourCheckDetailReq.builder()
                .appId(10919088)
                .startDate(startDate)
                .endDate(endDate)
                .roomId(1432681790786810498L)
                .build();
        Result<RoomHourCheckDetailRes> result = roomCheckInService.hourDetail(statsReq);
        RoomHourCheckDetailRes target = result.target();
        System.out.println(JSONObject.toJSONString(target));
    }


    @Test
    public void roomHourStatsSummary() {

        Date endDate = new Date();
        Date startDate = new Date(endDate.getTime() - 1000000000);

        RoomHourCheckStatsReq statsReq = RoomHourCheckStatsReq.builder()
                .appId(57333013)
                .startDate(startDate)
                .endDate(endDate)
                .roomId(1317073954216616578L)
                .build();
        Result<RoomHourStatsSummaryRes> result = roomCheckInService.roomHourStatsSummary(statsReq);
        RoomHourStatsSummaryRes target = result.target();
        System.out.println(JSONObject.toJSONString(target));
    }


    @Test
    public void roomDayStatsSummary() {

        Date endDate = new Date();
        Date startDate = new Date(endDate.getTime() - 1000000000);

        RoomDayCheckStatsReq statsReq = RoomDayCheckStatsReq.builder()
                .appId(9637128)
                .startDate(startDate)
                .endDate(endDate)
                .roomId(1293318642573913090L)
                .build();
        Result<RoomDayStatsSummaryRes> result = roomCheckInService.roomDayStatsSummary(statsReq);
        RoomDayStatsSummaryRes target = result.target();
        System.out.println(JSONObject.toJSONString(target));
    }


}
