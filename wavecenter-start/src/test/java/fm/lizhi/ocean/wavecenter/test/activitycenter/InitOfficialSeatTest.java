package fm.lizhi.ocean.wavecenter.test.activitycenter;


import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.util.DateUtil;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.RequestGetOfficialSeatTimeBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ResponseGetOfficialSeatTimeBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.service.ActivityOfficialSeatTimeService;
import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.job.InitOfficialSeatTimeJob;
import fm.lizhi.ocean.wavecenter.test.AbstractDataCenterTest;
import fm.lizhi.pp.util.json.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;

@Slf4j
public class InitOfficialSeatTest extends AbstractDataCenterTest {

    @Autowired
    private InitOfficialSeatTimeJob initOfficialSeatJob;

    @Autowired
    private ActivityOfficialSeatTimeService activityOfficialSeatTimeService;

    @Test
    public void init() {
        initOfficialSeatJob.initOfficialSeatTime(10919088);
    }

    @Test
    public void query() {
        Date startDate = DateUtil.getDayStart(new Date());
        Date endDate = DateUtil.getDayAfter(startDate, 7);
        RequestGetOfficialSeatTimeBean param = new RequestGetOfficialSeatTimeBean().setSeat(1).setAppId(9637128).setStartDate(startDate).setEndDate(endDate);
        Result<ResponseGetOfficialSeatTimeBean> result = activityOfficialSeatTimeService.getOfficialSeatTimeList(param);
        System.out.println(JsonUtil.toJson(result.target()));
    }
}
