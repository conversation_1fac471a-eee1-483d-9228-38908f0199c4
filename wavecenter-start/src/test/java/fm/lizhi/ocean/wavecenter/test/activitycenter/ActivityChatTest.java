package fm.lizhi.ocean.wavecenter.test.activitycenter;

import fm.lizhi.ocean.wavecenter.common.utils.ResultHandler;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.ActivityInfoDTO;
import fm.lizhi.ocean.wavecenter.service.activitycenter.manager.ActivityApplyManager;
import fm.lizhi.ocean.wavecenter.service.activitycenter.manager.ActivityChatManager;
import fm.lizhi.ocean.wavecenter.test.AbstractDataCenterTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
public class ActivityChatTest extends AbstractDataCenterTest {

    @Autowired
    private ActivityChatManager activityChatManager;

    @Autowired
    private ActivityApplyManager activityApplyManager;


    /**
     * 用户取消活动
     */
    @Test
    public void cancelActivity() {
        ResultHandler.handle(10919088, () -> {
            ActivityInfoDTO info = activityApplyManager.getActivityInfoById(5423754309944783487L);
            activityChatManager.userCancelActivityNotice(info, 1L);
            return null;
        });
    }

    @Test
    public void startActivity() {
        ResultHandler.handle(10919088, () -> {
            activityChatManager.startActivityNotice();
            return null;
        });
    }

    @Test
    public void endActivity() {
        ResultHandler.handle(10919088, () -> {
            activityChatManager.endActivityNotice();
            return null;
        });
    }

}
