package fm.lizhi.ocean.wavecenter.test.activitycenter;

import com.google.common.collect.Lists;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.common.utils.ResultHandler;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.ResourceGiveResultDTO;
import fm.lizhi.ocean.wavecenter.service.activitycenter.manager.ActivityMaterielGiveManager;
import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.manager.ActivityResourceGiveManagerImpl;
import fm.lizhi.ocean.wavecenter.test.AbstractDataCenterTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

@Slf4j
public class ResourceGiveTest extends AbstractDataCenterTest {

    @Autowired
    private ActivityResourceGiveManagerImpl activityResourceManager;

    @Autowired
    private ActivityMaterielGiveManager activityMaterielGiveManager;

    @Test
    public void giveProgramme() {
        Result<List<ResourceGiveResultDTO>> result = ResultHandler.handle(10919088,
                () -> activityResourceManager.realTimeGiveResource(10919088, 5412452146436120703L));
        System.out.println(result.rCode() + "--" + result.getMessage());
    }

    @Test
    public void initResourceRecord() {
        ArrayList<Long> longs = Lists.newArrayList(5413169294309790335L, 5413169294309790335L, 5413169294309791359L, 5413169294309792383L);
        for (Long aLong : longs){
            activityMaterielGiveManager.initActivityMateriel(aLong, null);
        }
    }

    @Test
    public void giveJobResource() {
        activityResourceManager.giveResourceJobExecute(10919088);
    }

}
