package fm.lizhi.ocean.wavecenter.test;

import com.alibaba.fastjson.serializer.JSONSerializer;
import com.alibaba.fastjson.serializer.PropertyPreFilter;
import fm.lizhi.commons.unit.annotation.VMOptions;
import fm.lizhi.commons.unit.datacenter.DataCenterRunner;
import fm.lizhi.commons.unit.datacenter.annotation.DataCenterBootTest;
import fm.lizhi.ocean.wavecenter.MicroservicesApplication;
import org.junit.runner.RunWith;

@RunWith(DataCenterRunner.class)
//@DataCenterProxyService(hosts = "***************:5200" )
@DataCenterBootTest(bootstrap= MicroservicesApplication.class)
@VMOptions("-Dlocal.ip=************ -Dvtag=v_ocean_adjust_notify_time -Dmetadata.service.name=lz_ocean_wavecenter -Dmetadata.business.env=public -Dmetadata.deploy.env=test -Dmetadata.region=cn")
public class AbstractDataCenterTest {

	static class PropertyFilter implements PropertyPreFilter {

		@Override
		public boolean apply(JSONSerializer serializer, Object object, String name) {
			if ("defaultInstanceForType".equals(name)) {
				return false;
			}
			if ("parserForType".equals(name)) {
				return false;
			}
			if ("initialized".equals(name)) {
				return false;
			}
			if ("serializedSize".equals(name)) {
				return false;
			}
			if (name.lastIndexOf("Bytes") > 0) {
				return false;
			}
			if (name.indexOf("OrBuilder") > 0) {
				return false;
			}
			return true;
		}
	}


}
