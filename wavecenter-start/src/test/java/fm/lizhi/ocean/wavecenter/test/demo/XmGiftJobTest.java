package fm.lizhi.ocean.wavecenter.test.demo;

import fm.lizhi.common.job.dispatcher.executor.handler.JobExecuteContext;
import fm.lizhi.ocean.wavecenter.infrastructure.file.job.XmLiveGiveGiftActionJob;
import fm.lizhi.ocean.wavecenter.test.AbstractDataCenterTest;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @date 2024/6/4 14:41
 */
public class XmGiftJobTest extends AbstractDataCenterTest {

    @Autowired
    private XmLiveGiveGiftActionJob xmLiveGiveGiftActionJob;

    @Test
    public void test() throws Exception {
        JobExecuteContext jobExecuteContext = new JobExecuteContext();
        jobExecuteContext.setParam("20240301,20240331,2000");
        xmLiveGiveGiftActionJob.execute(jobExecuteContext);
    }

}
