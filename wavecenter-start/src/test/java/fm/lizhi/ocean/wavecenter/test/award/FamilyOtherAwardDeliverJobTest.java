package fm.lizhi.ocean.wavecenter.test.award;

import fm.lizhi.common.job.dispatcher.executor.handler.JobExecuteContext;
import fm.lizhi.ocean.wavecenter.infrastructure.award.family.job.FamilyOtherAwardDeliverJob;
import fm.lizhi.ocean.wavecenter.test.AbstractDataCenterTest;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @date 2025/3/28 17:00
 */
public class FamilyOtherAwardDeliverJobTest extends AbstractDataCenterTest {

    @Autowired
    private FamilyOtherAwardDeliverJob familyOtherAwardDeliverJob;

    @Test
    public void test() throws Exception {
        while (true) {
            JobExecuteContext jobExecuteContext = new JobExecuteContext();
            familyOtherAwardDeliverJob.execute(jobExecuteContext);
        }
    }

}
