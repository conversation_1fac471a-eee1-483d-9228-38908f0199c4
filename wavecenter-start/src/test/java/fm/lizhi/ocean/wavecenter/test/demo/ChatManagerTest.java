package fm.lizhi.ocean.wavecenter.test.demo;

import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wavecenter.infrastructure.chat.manager.ChatManagerImpl;
import fm.lizhi.ocean.wavecenter.test.AbstractDataCenterTest;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @date 2024/5/23 15:15
 */
public class ChatManagerTest extends AbstractDataCenterTest {

    @Autowired
    private ChatManagerImpl chatManager;

    @Test
    public void test(){
        ContextUtils.setBusinessEvnEnum(BusinessEvnEnum.XIMI);
        chatManager.sendChatAsync(1457341942516994050L, "测试一下");
    }

}
