package fm.lizhi.ocean.wavecenter.test.demo;

import fm.lizhi.ocean.wavecenter.api.datacenter.bean.GuildAssessmentInfoBean;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.GuildRoomPerformanceResBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.RoomSignBean;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.manager.GuildDataManagerImpl;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.remote.hy.HyContractRemote;
import fm.lizhi.ocean.wavecenter.test.AbstractDataCenterTest;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/5/28 11:16
 */
public class GuildDataTest extends AbstractDataCenterTest {

    @Autowired
    private GuildDataManagerImpl guildDataManager;

    @Autowired
    private HyContractRemote hyContractRemote;

    @Test
    public void testAss(){
        ContextUtils.setBusinessEvnEnum(BusinessEvnEnum.HEI_YE);
        Optional<GuildAssessmentInfoBean> assessmentInfo = guildDataManager.getAssessmentInfo(BusinessEvnEnum.HEI_YE.appId(), 5318540755940148863L);
        GuildAssessmentInfoBean guildAssessmentInfoBean = assessmentInfo.get();
        System.out.println("guildAssessmentInfoBean = " + guildAssessmentInfoBean);
    }

    @Test
    public void test(){
        Optional<RoomSignBean> roomSign = hyContractRemote.getRoomSign(5318540755940148863L, 1368303752892663170L);
        System.out.println("roomSign.get() = " + roomSign.get());
    }

    @Test
    public void testPer(){
        GuildRoomPerformanceResBean list = guildDataManager.roomPerformance(5318540755940148863L, null);
        System.out.println("list = " + list);
    }

}
