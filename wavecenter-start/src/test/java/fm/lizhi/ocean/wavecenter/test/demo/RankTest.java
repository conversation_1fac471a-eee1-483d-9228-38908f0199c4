package fm.lizhi.ocean.wavecenter.test.demo;

import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.util.DateUtil;
import fm.lizhi.ocean.wavecenter.api.common.constants.OrderType;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.GuildIncomeSummaryBean;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.RankBean;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.RoomIncomeSummaryBean;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wavecenter.service.datacenter.handler.RoomDataHandler;
import fm.lizhi.ocean.wavecenter.service.datacenter.manager.RankDataManager;
import fm.lizhi.ocean.wavecenter.service.income.manager.IncomeManager;
import fm.lizhi.ocean.wavecenter.test.AbstractDataCenterTest;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/30 17:28
 */
public class RankTest extends AbstractDataCenterTest {

    @Autowired
    private RankDataManager rankDataManager;

    @Autowired
    private RoomDataHandler roomDataHandler;

    @Autowired
    private IncomeManager incomeManager;


    @Test
    public void playerRank() {
        ContextUtils.setBusinessEvnEnum(BusinessEvnEnum.HEI_YE);
        Date date = DateUtil.formatStrToDate("2024-05-29 11:11:11", DateUtil.NORMAL_DATE_FORMAT);
        List<RankBean> rankBeans = rankDataManager.guildPlayer(5318540755940148863L, null, date, OrderType.DESC);
        System.out.println("rankBeans = " + JsonUtil.dumps(rankBeans));
    }


    @Test
    public void getRoomSign() {
        ContextUtils.setBusinessEvnEnum(BusinessEvnEnum.XIMI);
        Date date = DateUtil.formatStrToDate("2024-05-29 11:11:11", DateUtil.NORMAL_DATE_FORMAT);
        List<RankBean> rankBeans = rankDataManager.roomPlayer(1291847566882786178L, 5246373908659044991L, date, OrderType.DESC);
        System.out.println("rankBeans = " + JsonUtil.dumps(rankBeans));
    }

    @Test
    public void guildIncomeSummary() {
        ContextUtils.setBusinessEvnEnum(BusinessEvnEnum.HEI_YE);
        long startTime = System.currentTimeMillis();
        GuildIncomeSummaryBean summaryBean = incomeManager.guildIncomeSummary(5318540755940148863L, null);
        long endTime = System.currentTimeMillis();
        System.out.println("time=" + (endTime - startTime) + ", guildIncomeSummary = " + JsonUtil.dumps(summaryBean));
    }

    @Test
    public void roomIncomeSummary() {
        ContextUtils.setBusinessEvnEnum(BusinessEvnEnum.HEI_YE);
        long startTime = System.currentTimeMillis();
        RoomIncomeSummaryBean roomIncomeSummaryBean = incomeManager.roomIncomeSummary(5318540755940148863L, 1378847547564528642L, BusinessEvnEnum.HEI_YE.getAppId());
        long endTime = System.currentTimeMillis();
        System.out.println("time=" + (endTime - startTime) + ", roomIncomeSummary = " + JsonUtil.dumps(roomIncomeSummaryBean));
    }
}
