package fm.lizhi.ocean.wavecenter.test.demo;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.*;
import fm.lizhi.ocean.wavecenter.api.user.service.UserCommonService;
import fm.lizhi.ocean.wavecenter.test.AbstractDataCenterTest;
import fm.lizhi.xm.user.account.user.api.XmNewUserService;
import fm.lizhi.xm.user.account.user.protocol.XmUserBaseProto;
import fm.lizhi.xm.user.account.user.protocol.XmUserProto;
import fm.lizhi.xm.user.account.user.protocol.XmUserRequestProto;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;

@Slf4j
public class UserCommonTest extends AbstractDataCenterTest {


    @Autowired
    private UserCommonService userCommonService;


    @Autowired
    private XmNewUserService xmNewUserService;

    @Test
    public void getUserTest(){
        XmUserRequestProto.GetUserRequest request = XmUserRequestProto.GetUserRequest.newBuilder().setUserId(1291847566882786178L).build();
        Result<XmUserProto.ResponseGetUser> result = xmNewUserService.getUser(request);
        assert result.rCode() == GeneralRCode.GENERAL_RCODE_SUCCESS;
        XmUserBaseProto.User user = result.target().getUser();
        System.out.println(user.getId());

        Result<XmUserProto.ResponseGetUsers> users = xmNewUserService.getUsers(Arrays.asList(1291847566882786178L), false);
        assert users.rCode() == GeneralRCode.GENERAL_RCODE_SUCCESS;
        XmUserProto.ResponseGetUsers target = users.target();
        System.out.println(target.getUserList());
    }

    @Test
    public void getUserRoleInfoTest(){
        Result<UserRoleInfoBean> result = userCommonService.getUserRoleInfo(9637128, 1291847566882786178L);
        assert result.rCode() == GeneralRCode.GENERAL_RCODE_SUCCESS;
    }


    @Test
    public void getAllGuildRoomsTest(){
        Result<PageBean<RoomSignBean>> result = userCommonService.getAllGuildRooms(9637128, 5248023335008207487L, 1, 20);
        assert result.rCode() == GeneralRCode.GENERAL_RCODE_SUCCESS;
    }


    @Test
    public void getAllRoomPlayersTest() {
        Result<PageBean<PlayerSignBean>> result = userCommonService.getAllRoomPlayers(9637128, 1291847566882786178L, 1, 10);
        assert result.rCode() == GeneralRCode.GENERAL_RCODE_SUCCESS;
    }

    @Test
    public void getAllGuildPlayerTest() {
        QueryGuildPlayerBean req = QueryGuildPlayerBean.builder().appId(9637128)
                .familyId(5248023335008207487L).build();
        Result<PageBean<PlayerSignBean>> result = userCommonService.getAllGuildPlayer(req, 1, 10);
        assert result.rCode() == GeneralRCode.GENERAL_RCODE_SUCCESS;
    }

    @Test
    public void getUserByIdTest() {
        Result<UserBean> result = userCommonService.getUserById(9637128, 1291847566882786178L);
        assert result.rCode() == GeneralRCode.GENERAL_RCODE_SUCCESS;
    }

    @Test
    public void getUserByBandTest() {
        Result<UserBean> result = userCommonService.getUserByBand(9637128, "180004171");
        assert result.rCode() == GeneralRCode.GENERAL_RCODE_SUCCESS;
    }

    @Test
    public void getPlayerSignInfoTest() {
        Result<PlayerSignBean> result = userCommonService.getPlayerSignInfo(9637128, 5248023335008207487L, 1291847566882786178L, 1291847566882786178L);
        assert result.rCode() == GeneralRCode.GENERAL_RCODE_SUCCESS;
    }

}
