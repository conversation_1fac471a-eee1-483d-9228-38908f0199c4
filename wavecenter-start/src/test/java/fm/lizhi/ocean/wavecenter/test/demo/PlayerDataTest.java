package fm.lizhi.ocean.wavecenter.test.demo;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.PlayerAssessmentInfoBean;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.service.datacenter.impl.PlayerDataServiceImpl;
import fm.lizhi.ocean.wavecenter.test.AbstractDataCenterTest;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @date 2024/5/29 14:47
 */
public class PlayerDataTest extends AbstractDataCenterTest {

    @Autowired
    private PlayerDataServiceImpl playerDataService;

    @Test
    public void testAss(){
        Result<PlayerAssessmentInfoBean> result = playerDataService.getAssessmentInfo(BusinessEvnEnum.XIMI.appId(), 1389071994238013698L, 5246373908659044991L, 1291847566882786178L);
        PlayerAssessmentInfoBean target = result.target();
        System.out.println("target = " + target);
    }

    @Test
    public void testAssXm(){
        Result<PlayerAssessmentInfoBean> result = playerDataService.getAssessmentInfo(BusinessEvnEnum.XIMI.appId(), 1389071994238013698L, 5246373908659044991L, null);
        PlayerAssessmentInfoBean target = result.target();
        System.out.println("target = " + target);
    }

}
