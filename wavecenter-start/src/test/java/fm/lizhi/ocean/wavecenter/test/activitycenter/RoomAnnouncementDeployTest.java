package fm.lizhi.ocean.wavecenter.test.activitycenter;

import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.datastore.dao.ActivityRoomAnnouncementDeployDao;
import fm.lizhi.ocean.wavecenter.test.AbstractDataCenterTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
public class RoomAnnouncementDeployTest extends AbstractDataCenterTest {



    @Autowired
    private ActivityRoomAnnouncementDeployDao activityRoomAnnouncementDeployDao;


    @Test
    public void updateAnnouncement() {
        boolean b = activityRoomAnnouncementDeployDao.updateOriginalAnnouncementByActivityId(5411864055920985727L, null, null);
        System.out.println(b);
    }

}
