package fm.lizhi.ocean.wavecenter.test.singer;

import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.DecorateEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerTypeEnum;
import fm.lizhi.ocean.wavecenter.api.award.singer.request.RequestPageSingerDecorateRule;
import fm.lizhi.ocean.wavecenter.api.award.singer.request.RequestSaveSingerDecorateRule;
import fm.lizhi.ocean.wavecenter.api.award.singer.response.ResponseSingerDecorateRule;
import fm.lizhi.ocean.wavecenter.api.award.singer.serivce.SingerDecorateRuleService;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.test.AbstractDataCenterTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 */
@Slf4j
public class SingerDecorateRuleServiceTest extends AbstractDataCenterTest {

    @Autowired
    private SingerDecorateRuleService singerDecorateRuleService;

    @Test
    public void testSaveSingerDecorateRule() {

        RequestSaveSingerDecorateRule request = new RequestSaveSingerDecorateRule();
        request.setAppId(BusinessEvnEnum.PP.getAppId());
        request.setSingerType(SingerTypeEnum.QUALITY.getType());
        request.setSongStyle("ALL");
        request.setDecorateType(DecorateEnum.AVATAR.getType());
        request.setDecorateId(5437802338385523327L);
        request.setEnabled(true);
        request.setOperator("admin");

        Result<Void> result = singerDecorateRuleService.saveSingerDecorateRule(request);
        log.info("result:{}", JsonUtil.dumps(result));
    }

    @Test
    public void testPageSingerDecorateRule() {
        RequestPageSingerDecorateRule request = new RequestPageSingerDecorateRule();
        request.setAppId(BusinessEvnEnum.PP.getAppId());
        request.setPageSize(20);
        request.setPageNo(1);

        Result<PageBean<ResponseSingerDecorateRule>> result = singerDecorateRuleService.pageSingerDecorateRule(request);
        log.info("result:{}", JsonUtil.dumps(result));
    }
}
