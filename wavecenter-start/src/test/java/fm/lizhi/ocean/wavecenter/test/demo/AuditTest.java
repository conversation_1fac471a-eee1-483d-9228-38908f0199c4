package fm.lizhi.ocean.wavecenter.test.demo;

import fm.lizhi.commons.util.DateUtil;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.common.constants.AuditMetricsEnum;
import fm.lizhi.ocean.wavecenter.api.common.constants.OrderType;
import fm.lizhi.ocean.wavecenter.api.live.bean.AddAuditRecordFullParamBean;
import fm.lizhi.ocean.wavecenter.api.live.bean.AuditRecordBean;
import fm.lizhi.ocean.wavecenter.api.live.bean.AuditRecordSearchParamBean;
import fm.lizhi.ocean.wavecenter.api.live.bean.GuildAuditStatsParamBean;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.service.live.dto.GuildAuditRecordDto;
import fm.lizhi.ocean.wavecenter.service.live.manager.LiveAuditManager;
import fm.lizhi.ocean.wavecenter.test.AbstractDataCenterTest;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/5/8 19:17
 */
public class AuditTest extends AbstractDataCenterTest {


    @Autowired
    private LiveAuditManager liveAuditManager;

    private Long familyA = 5120155821400925311L;
    private Long nj1 = 2677417875244898348L;//pplive01
    private Long nj3 = 1386652551113644802L;//不睡觉啦
    private Long u1 = 1172865348842065282L;//
    private Long u2 = 1172896326564708610L;//
    private Long u5 = 2677600692675342892L;//

    private Long familyB = 5154674007559110783L;
    private Long nj2 = 1412936864054273410L;
    private Long u3 = 1442617972140233090L;//
    private Long u4 = 1442616518293790466L;//

    private Long u = 1377315212755443586L;

    @Test
    public void testMock2(){
        //违规记录1
        liveAuditManager.addAuditRecordFull(AddAuditRecordFullParamBean.builder()
                .appId(BusinessEvnEnum.PP.appId())
                .signNjId(nj1)
                .signFamilyId(familyA)
                .bizId(1L)
                .bizNjId(nj1)
                .bizFamilyId(familyA)
                .userId(u2)
                .op(10).reason("违规记录1")
                .auditEndTime(DateUtil.formatStrToNormalDate("2024-08-27 23:46:51"))
                .auditStartTime(DateUtil.formatStrToNormalDate("2024-08-27 23:46:51"))
                .auditId("2").recordId("2").sceneType(1160018).sceneName("PP-自动处罚")
                .pushTime("1天").build());
        //违规记录2
        liveAuditManager.addAuditRecordFull(AddAuditRecordFullParamBean.builder()
                .appId(BusinessEvnEnum.PP.appId())
                .signNjId(nj2).signFamilyId(familyB)
                .bizId(1L)
                .bizNjId(nj1).bizFamilyId(familyA)
                .userId(u3)
                .op(10).reason("违规记录2")
                .auditEndTime(DateUtil.formatStrToNormalDate("2024-08-27 23:46:51"))
                .auditStartTime(DateUtil.formatStrToNormalDate("2024-08-27 23:46:51"))
                .auditId("3").recordId("3").sceneType(1160018).sceneName("PP-自动处罚")
                .pushTime("1天").build());
        //违规记录3
        liveAuditManager.addAuditRecordFull(AddAuditRecordFullParamBean.builder()
                .appId(BusinessEvnEnum.PP.appId())
                .bizId(1L).bizNjId(nj1).bizFamilyId(familyA)
                .userId(u)
                .op(10).reason("违规记录3")
                .auditEndTime(DateUtil.formatStrToNormalDate("2024-08-27 23:46:51"))
                .auditStartTime(DateUtil.formatStrToNormalDate("2024-08-27 23:46:51"))
                .auditId("4").recordId("4").sceneType(1160018).sceneName("PP-自动处罚")
                .pushTime("1天").build());
        //违规记录4
        liveAuditManager.addAuditRecordFull(AddAuditRecordFullParamBean.builder()
                .appId(BusinessEvnEnum.PP.appId())
                .signNjId(nj2).signFamilyId(familyB)
                .bizId(1L)
                .bizNjId(nj2).bizFamilyId(familyB)
                .userId(u4)
                .op(10).reason("违规记录4-空时限")
                .auditEndTime(DateUtil.formatStrToNormalDate("2024-08-27 23:46:51"))
                .auditStartTime(DateUtil.formatStrToNormalDate("2024-08-27 23:46:51"))
                .auditId("5").recordId("5").sceneType(1160018).sceneName("PP-自动处罚").build());
        //违规记录5
        liveAuditManager.addAuditRecordFull(AddAuditRecordFullParamBean.builder()
                .appId(BusinessEvnEnum.PP.appId())
                .signNjId(nj1).signFamilyId(familyA)
                .bizId(1L)
                .bizNjId(nj1).bizFamilyId(familyA)
                .userId(u2)
                .op(10).reason("违规记录5")
                .auditEndTime(DateUtil.formatStrToNormalDate("2024-08-27 23:46:51"))
                .auditStartTime(DateUtil.formatStrToNormalDate("2024-08-27 23:46:51"))
                .auditId("6").recordId("6").sceneType(1160018).sceneName("PP-自动处罚")
                .pushTime("1天").build());

        //违规记录6
        liveAuditManager.addAuditRecordFull(AddAuditRecordFullParamBean.builder()
                .appId(BusinessEvnEnum.PP.appId())
                .signNjId(nj2).signFamilyId(familyB)
                .bizId(1L)
                .bizNjId(nj1).bizFamilyId(familyA)
                .userId(u3)
                .op(10).reason("违规记录6")
                .auditEndTime(DateUtil.formatStrToNormalDate("2024-08-27 23:46:51"))
                .auditStartTime(DateUtil.formatStrToNormalDate("2024-08-27 23:46:51"))
                .auditId("7").recordId("7").sceneType(1160018).sceneName("PP-自动处罚")
                .pushTime("1天").build());

        //违规记录7
        liveAuditManager.addAuditRecordFull(AddAuditRecordFullParamBean.builder()
                .appId(BusinessEvnEnum.PP.appId())
                .signNjId(nj1).signFamilyId(familyA)
                .bizId(1L)
                .bizNjId(nj3).bizFamilyId(familyA)
                .userId(u1)
                .op(10).reason("违规记录7")
                .auditEndTime(DateUtil.formatStrToNormalDate("2024-08-27 23:46:51"))
                .auditStartTime(DateUtil.formatStrToNormalDate("2024-08-27 23:46:51"))
                .auditId("8").recordId("8").sceneType(1160018).sceneName("PP-自动处罚")
                .pushTime("1天").build());
    }

    @Test
    public void mockDataTest(){
        //违规记录1
        liveAuditManager.addAuditRecordFull(AddAuditRecordFullParamBean.builder()
                .appId(BusinessEvnEnum.PP.appId()).signNjId(nj1).signFamilyId(familyA)
                .bizId(1L).bizNjId(nj1).bizFamilyId(familyA)
                .userId(u2).op(10).reason("带货")
                .auditEndTime(DateUtil.formatStrToNormalDate("2024-08-27 23:46:51"))
                .auditStartTime(DateUtil.formatStrToNormalDate("2024-08-27 23:46:51"))
                .auditId("2").recordId("2").sceneType(1160018).sceneName("PP-自动处罚")
                .pushTime("1天").build());
        //违规记录2
        liveAuditManager.addAuditRecordFull(AddAuditRecordFullParamBean.builder()
                .appId(BusinessEvnEnum.PP.appId()).signNjId(nj2).signFamilyId(familyB)
                .bizId(1L).bizNjId(nj1).bizFamilyId(familyA)
                .userId(u3)
                .op(10).reason("带货")
                .auditEndTime(DateUtil.formatStrToNormalDate("2024-08-27 23:46:51"))
                .auditStartTime(DateUtil.formatStrToNormalDate("2024-08-27 23:46:51"))
                .auditId("3").recordId("3").sceneType(1160018).sceneName("PP-自动处罚")
                .pushTime("1天").build());
        //违规记录3
        liveAuditManager.addAuditRecordFull(AddAuditRecordFullParamBean.builder()
                .appId(BusinessEvnEnum.PP.appId())
                .bizId(1L).bizNjId(nj1).bizFamilyId(familyA)
                .userId(u)
                .op(10).reason("带货")
                .auditEndTime(DateUtil.formatStrToNormalDate("2024-08-27 23:46:51"))
                .auditStartTime(DateUtil.formatStrToNormalDate("2024-08-27 23:46:51"))
                .auditId("4").recordId("4").sceneType(1160018).sceneName("PP-自动处罚")
                .pushTime("1天").build());
        //违规记录5
        liveAuditManager.addAuditRecordFull(AddAuditRecordFullParamBean.builder()
                .appId(BusinessEvnEnum.PP.appId()).signNjId(nj1).signFamilyId(familyA)
                .bizId(1L).bizNjId(nj1).bizFamilyId(familyA)
                .userId(u2)
                .op(10).reason("带货2")
                .auditEndTime(DateUtil.formatStrToNormalDate("2024-08-27 23:46:51"))
                .auditStartTime(DateUtil.formatStrToNormalDate("2024-08-27 23:46:51"))
                .auditId("5").recordId("5").sceneType(1160018).sceneName("PP-自动处罚")
                .pushTime("1天").build());

        //违规记录6
        liveAuditManager.addAuditRecordFull(AddAuditRecordFullParamBean.builder()
                .appId(BusinessEvnEnum.PP.appId()).signNjId(nj2).signFamilyId(familyB)
                .bizId(1L).bizNjId(nj1).bizFamilyId(familyA)
                .userId(u3)
                .op(10).reason("带货2")
                .auditEndTime(DateUtil.formatStrToNormalDate("2024-08-27 23:46:51"))
                .auditStartTime(DateUtil.formatStrToNormalDate("2024-08-27 23:46:51"))
                .auditId("6").recordId("6").sceneType(1160018).sceneName("PP-自动处罚")
                .pushTime("1天").build());

    }

    @Test
    public void testSate(){
        GuildAuditStatsParamBean.GuildAuditStatsParamBeanBuilder builder = GuildAuditStatsParamBean.builder()
                .appId(BusinessEvnEnum.PP.appId())
                .endTime(DateUtil.formatStrToNormalDate("2024-08-30 23:46:51"))
                .startTime(DateUtil.formatStrToNormalDate("2024-08-01 23:46:51"))
                .familyId(5120155821400925311L)
                .roomId(1386652551113644802L);
        PageBean<GuildAuditRecordDto> result = liveAuditManager.guildAuditRecordStats(builder.build(), 1, 20);
        System.out.println("result = " + result);
    }

    @Test
    public void query(){
        AuditRecordSearchParamBean searchParamBean = AuditRecordSearchParamBean.builder()
                .appId(BusinessEvnEnum.PP.appId())
                .endTime(DateUtil.formatStrToNormalDate("2024-08-30 23:46:51"))
                .startTime(DateUtil.formatStrToNormalDate("2024-08-01 23:46:51"))
                .familyId(5120155821400925311L)
                .roomId(1386652551113644802L)
                .build();
        PageBean<AuditRecordBean> list = liveAuditManager.getAuditRecordDetail(searchParamBean, 1, 20);
        System.out.println("list = " + list);
    }

    @Test
    public void add(){
        //2677600692675342892在自己的签约厅违规
        AddAuditRecordFullParamBean.AddAuditRecordFullParamBeanBuilder builder = AddAuditRecordFullParamBean.builder()
                .appId(BusinessEvnEnum.PP.appId())
                .signNjId(1386652551113644802L)
                .signFamilyId(5120155821400925311L)
                .bizId(1L)
                .bizNjId(1386652551113644802L)
                .bizNjId(5120155821400925311L)
                .userId(2677600692675342892L)
                .op(10)
                .reason("带货")
                .auditEndTime(DateUtil.formatStrToNormalDate("2024-08-25 23:46:51"))
                .auditStartTime(DateUtil.formatStrToNormalDate("2024-08-25 23:46:51"))
                .auditId("1")
                .recordId("1")
                .sceneType(1160018)
                .sceneName("PP-自动处罚")
                .pushTime("1天");
        boolean result = liveAuditManager.addAuditRecordFull(builder.build());
        System.out.println("result = " + result);
    }

    @Test
    public void test(){
        Date start = DateUtil.formatStrToNormalDate("2024-04-01 23:46:51");
        Date end = DateUtil.formatStrToNormalDate("2024-04-31 23:46:51");
        GuildAuditStatsParamBean param = new GuildAuditStatsParamBean();
        param.setAppId(10919088);
        param.setFamilyId(5356210793170780799L);
        param.setStartTime(start);
        param.setEndTime(end);
        param.setOrderType(OrderType.DESC);
        param.setOrderMetrics(AuditMetricsEnum.PUSH_NUMBER);
//        Integer i = auditRecordMapper.guildAuditRecordStatsCount(param);
//        System.out.println("i = " + i);
        PageBean<GuildAuditRecordDto> guildAuditRecordPageBean = liveAuditManager.guildAuditRecordStats(param, 1, 20);
        System.out.println("list = " + guildAuditRecordPageBean);
    }

}
