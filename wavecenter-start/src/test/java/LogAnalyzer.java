import java.io.*;
import java.nio.file.*;
import java.util.*;
import java.util.regex.*;

/**
 * 日志分析工具
 * 读取指定的日志文件，提取URL并进行排序
 */
public class LogAnalyzer {
    
    // 正则表达式匹配 url= 后面的内容
    private static final Pattern URL_PATTERN = Pattern.compile("url=([^\\s,]+)");
    
    public static void main(String[] args) {
        LogAnalyzer analyzer = new LogAnalyzer();
        
        // 指定要分析的日志文件
        String[] logFiles = {
            "wavecenter-start/src/test/java/fm/lizhi/ocean/wavecenter/test/data (22).txt",
            "wavecenter-start/src/test/java/fm/lizhi/ocean/wavecenter/test/data (23).txt"
        };
        
        try {
            // 分析日志文件
            List<String> urls = analyzer.analyzeLogFiles(logFiles);
            
            // 输出结果
            analyzer.printResults(urls, logFiles);
            
        } catch (IOException e) {
            System.err.println("读取文件时发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 分析多个日志文件，提取并排序URL
     * 
     * @param logFiles 日志文件路径数组
     * @return 排序后的URL列表
     * @throws IOException 文件读取异常
     */
    public List<String> analyzeLogFiles(String[] logFiles) throws IOException {
        Set<String> urlSet = new TreeSet<>(); // 使用TreeSet自动排序并去重
        
        for (String logFile : logFiles) {
            System.out.println("正在分析文件: " + logFile);
            
            Path filePath = Paths.get(logFile);
            if (!Files.exists(filePath)) {
                System.err.println("警告: 文件不存在 - " + logFile);
                continue;
            }
            
            // 读取文件并提取URL
            List<String> urls = extractUrlsFromFile(filePath);
            urlSet.addAll(urls);
            
            System.out.println("从 " + logFile + " 中提取到 " + urls.size() + " 个URL");
        }
        
        return new ArrayList<>(urlSet);
    }
    
    /**
     * 从单个文件中提取URL
     * 
     * @param filePath 文件路径
     * @return URL列表
     * @throws IOException 文件读取异常
     */
    private List<String> extractUrlsFromFile(Path filePath) throws IOException {
        List<String> urls = new ArrayList<>();
        
        try (BufferedReader reader = Files.newBufferedReader(filePath)) {
            String line;
            int lineNumber = 0;
            
            while ((line = reader.readLine()) != null) {
                lineNumber++;
                
                // 提取URL
                String url = extractUrlFromLine(line);
                if (url != null) {
                    urls.add(url);
                }
            }
        }
        
        return urls;
    }
    
    /**
     * 从单行日志中提取URL
     * 
     * @param line 日志行
     * @return 提取的URL，如果没有找到则返回null
     */
    private String extractUrlFromLine(String line) {
        Matcher matcher = URL_PATTERN.matcher(line);
        if (matcher.find()) {
            return matcher.group(1);
        }
        return null;
    }
    
    /**
     * 打印分析结果
     * 
     * @param urls 排序后的URL列表
     * @param logFiles 分析的文件列表
     */
    private void printResults(List<String> urls, String[] logFiles) {
        System.out.println("\n" + "=".repeat(80));
        System.out.println("日志分析结果");
        System.out.println("=".repeat(80));
        
        System.out.println("分析的文件:");
        for (String file : logFiles) {
            System.out.println("  - " + file);
        }
        
        System.out.println("\n总共找到 " + urls.size() + " 个不重复的URL");
        System.out.println("\n排序后的URL列表:");
        System.out.println("-".repeat(80));
        
        // 打印排序后的URL，带编号
        for (int i = 0; i < urls.size(); i++) {
            System.out.printf("%3d. %s%n", i + 1, urls.get(i));
        }
        
        System.out.println("-".repeat(80));
        
        // 统计URL的域名分布
        printUrlStatistics(urls);
    }
    
    /**
     * 打印URL统计信息
     * 
     * @param urls URL列表
     */
    private void printUrlStatistics(List<String> urls) {
        System.out.println("\nURL统计信息:");
        
        // 统计不同路径的出现次数
        Map<String, Integer> pathCount = new HashMap<>();
        Map<String, Integer> domainCount = new HashMap<>();
        
        for (String url : urls) {
            try {
                // 提取域名
                String domain = extractDomain(url);
                domainCount.put(domain, domainCount.getOrDefault(domain, 0) + 1);
                
                // 提取路径
                String path = extractPath(url);
                pathCount.put(path, pathCount.getOrDefault(path, 0) + 1);
                
            } catch (Exception e) {
                System.err.println("解析URL时出错: " + url);
            }
        }
        
        // 打印域名统计
        System.out.println("\n域名分布:");
        domainCount.entrySet().stream()
                .sorted(Map.Entry.<String, Integer>comparingByValue().reversed())
                .forEach(entry -> System.out.printf("  %s: %d 个URL%n", entry.getKey(), entry.getValue()));
        
        // 打印最常见的路径
        System.out.println("\n最常见的API路径 (前10个):");
        pathCount.entrySet().stream()
                .sorted(Map.Entry.<String, Integer>comparingByValue().reversed())
                .limit(10)
                .forEach(entry -> System.out.printf("  %s: %d 次%n", entry.getKey(), entry.getValue()));
    }
    
    /**
     * 从URL中提取域名
     */
    private String extractDomain(String url) {
        if (url.startsWith("http://")) {
            url = url.substring(7);
        } else if (url.startsWith("https://")) {
            url = url.substring(8);
        }
        
        int slashIndex = url.indexOf('/');
        if (slashIndex != -1) {
            return url.substring(0, slashIndex);
        }
        return url;
    }
    
    /**
     * 从URL中提取路径
     */
    private String extractPath(String url) {
        if (url.startsWith("http://")) {
            url = url.substring(7);
        } else if (url.startsWith("https://")) {
            url = url.substring(8);
        }
        
        int slashIndex = url.indexOf('/');
        if (slashIndex != -1) {
            return url.substring(slashIndex);
        }
        return "/";
    }
}
