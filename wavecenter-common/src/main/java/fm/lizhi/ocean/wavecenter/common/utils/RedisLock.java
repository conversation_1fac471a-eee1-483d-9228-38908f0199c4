package fm.lizhi.ocean.wavecenter.common.utils;

import fm.lizhi.common.datastore.redis.client.RedisClient;
import lombok.extern.slf4j.Slf4j;
import redis.clients.jedis.params.SetParams;

import java.io.Closeable;
import java.util.UUID;

/**
 * Redis 锁
 *
 * <AUTHOR>
 * @date 2020/1/2
 */
@Slf4j
public class RedisLock implements Closeable {

    /**
     * redis 客户端
     */
    private final RedisClient redis;
    /**
     * 锁的 Key
     */
    private final String key;
    /**
     * 是否获取到锁
     */
    private volatile boolean locked = false;
    /**
     * 锁值里面的随机数
     */
    private final UUID uuid;

    /**
     * 获取锁超时的时间(毫秒)，超时后获取失败，默认读{@link #DEFAULT_ACQUIRE_RESOLUTION_MILLIS}
     */
    private final int timeoutMillis;

    /**
     * 锁过期的时间(毫秒)，默认读{@link #DEFAULT_EXPIRE_MILLIS}
     */
    private final int expireMillis;

    /**
     * 一次获取锁失败后，线程Sleep的时间(毫秒)，Sleep后会继续重新尝试获取
     */
    private static final int DEFAULT_ACQUIRE_RESOLUTION_MILLIS = 100;
    /**
     * 锁过期的默认时间(毫秒)
     */
    private static final int DEFAULT_EXPIRE_MILLIS = 5000;
    /**
     * 获取锁默认超时的时间(毫秒)，超时后获取失败
     */
    private static final int DEFAULT_ACQUIRE_TIMEOUT_MILLIS = 3000;

    /**
     * 删除 Key 的 Lua 脚本
     */
    private static final String LUA_DEL_SCRIPT = "if redis.call('GET',KEYS[1]) == ARGV[1] then return redis.call('DEL',KEYS[1]) else return 0 end";

    public RedisLock(RedisClient redis, String key) {
        this(redis, key, DEFAULT_ACQUIRE_TIMEOUT_MILLIS, DEFAULT_EXPIRE_MILLIS);
    }

    /**
     * @param redis
     * @param key
     * @param timeout 超时时间，单位毫秒
     */
    public RedisLock(RedisClient redis, String key, int timeout) {
        this(redis, key, timeout, DEFAULT_EXPIRE_MILLIS);
    }

    /**
     * @param redis
     * @param key
     * @param timeout 超时时间，单位毫秒
     * @param expire  过期时间，单位毫秒
     */
    public RedisLock(RedisClient redis, String key, int timeout, int expire) {
        this.redis = redis;
        this.key = key;
        this.timeoutMillis = timeout;
        this.expireMillis = expire;
        this.uuid = UUID.randomUUID();
    }

    /**
     * 锁，会循环尝试获取锁，
     * 每次获取锁失败后会 sleep 一段时间({@link #DEFAULT_ACQUIRE_RESOLUTION_MILLIS})
     * 然后重新循环获取，直到超时(@link #timeoutMillis)
     *
     * @return 获取锁超时会返回 false，获取成功返回 true
     */
    public boolean lock() {
        synchronized (this) {
            long timeout = this.timeoutMillis;
            while (timeout > 0) {
                if (tryLock()) {
                    return true;
                }
                timeout -= DEFAULT_ACQUIRE_RESOLUTION_MILLIS;
                try {
                    this.wait(DEFAULT_ACQUIRE_RESOLUTION_MILLIS);
                } catch (InterruptedException e) {
                    log.warn("lock sleep error. key={}, value={}", key, getLockValue(), e);
                    Thread.currentThread().interrupt();
                }
            }
            return false;
        }
    }

    /**
     * 只尝试一次获取锁
     *
     * @return 是否设置Key成功
     */
    public synchronized boolean tryLock() {
        String lockValue = getLockValue();
        if ("OK".equals(this.setNxPx(lockValue))) {
            infoLog("try lock");
            this.locked = true;
            return true;
        }
        return false;
    }

    /**
     * 写日志，默认把 key 和 value 带上
     *
     * @param msg
     */
    private void infoLog(String msg) {
        log.info(msg + "--- lockKey={}, lockValue={}", key, getLockValue());
    }

    /**
     * 解锁，用 Lua 脚本删除 key
     */
    public synchronized void unlock() {
        if (this.locked) {
            infoLog("unlock");
            String lockValue = getLockValue();
            long result = (long) this.redis.eval(LUA_DEL_SCRIPT, 1, this.key, lockValue);
            if (result > 0) {
                this.locked = false;
            }
        }
    }

    /**
     * 查看当前线程是否继续拥有锁
     * 解释：这个方法主要是用于判断业务操作的时间是否超过了锁的过期时间
     *
     * @return
     */
    public boolean checkTimeOut() {
        if (this.locked) {
            String value = this.redis.get(this.key);
            if (this.getLockValue().equals(value)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取锁的值，默认用线程Id
     *
     * @return
     */
    private String getLockValue() {
        return this.uuid.toString() + "_" + Thread.currentThread().getId();
    }

    /**
     * 如果不存在(nx)，设置 key，同时设置过期时间(px)
     *
     * @param value
     * @return OK 表示设置成功
     */
    private String setNxPx(final String value) {
        SetParams setParams = new SetParams();
        setParams.nx();
        setParams.px(this.expireMillis);
        return this.redis.set(this.key, value, setParams);
    }

    @Override
    public void close() {
        unlock();
    }
}