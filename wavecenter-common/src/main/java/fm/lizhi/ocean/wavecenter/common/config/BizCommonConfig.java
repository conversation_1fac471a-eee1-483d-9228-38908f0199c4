package fm.lizhi.ocean.wavecenter.common.config;

/**
 * <AUTHOR>
 * @date 2024/4/10 16:38
 */
public interface BizCommonConfig {


    String getCdnHost();

    /**
     * 风控APPId
     * @return
     */
    String getRiskAppId();

    /**
     * 违规场景展示名称转换
     * @return
     */
    String getAuditSceneName();

    /**
     * 获取发送私信用户 ID
     */
    long getSendChatUid();

    /**
     * 获取罗马配置
     */
    RomeConfig getRomeConfig();

    /**
     * 获取转存资源的前缀
     */
    String getResourceTransferUrlPrefix(String fileName);


}
