package fm.lizhi.ocean.wavecenter.common.utils;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;

import java.util.function.Supplier;

/**
 * <AUTHOR>
 * @date 2024/4/10 11:58
 */
public class ResultHandler {

    public static <T> Result<T> handle(int appId, Supplier<Result<T>> supplier){
        try {
            ContextUtils.setBusinessEvnEnum(BusinessEvnEnum.from(appId));
            return supplier.get();
        } finally {
            ContextUtils.clearContext();
        }
    }

}
