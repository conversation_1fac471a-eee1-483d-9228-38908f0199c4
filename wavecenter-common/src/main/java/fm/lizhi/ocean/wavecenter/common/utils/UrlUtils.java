package fm.lizhi.ocean.wavecenter.common.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.utils.URIBuilder;

import java.io.UnsupportedEncodingException;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.URLEncoder;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Description:
 *
 * <AUTHOR>
 * Created in  2023/5/29
 */
@Slf4j
public class UrlUtils {

    private static final String REGEX_URI = "^https?://[^/]+/(.*)$";

    /**
     * 传入url获取uri
     *
     * @param url URL地址
     * @return URI
     */
    public static String getUriFromUrl(String url) {
        if (StringUtils.isBlank(url)) return StringUtils.EMPTY;
        try {
            return url.replaceFirst(REGEX_URI, "$1");
        } catch (Exception e) {
            log.warn("getUriFromUrl - error, url:{}", url, e);
            return StringUtils.EMPTY;
        }
    }

    /**
     * 移除url的cdnHost，即去除url的scheme和host，只保留path、query和fragment的部分，返回值以斜杆"/"开头，如果有特殊字符会按UTF-8转义。
     * 该方法会校验输入url是否使用了统一cdnHost。
     *
     * @param url 原始url值
     * @return 移除cdnHost后的url路径，以斜杆"/"开头，如果有特殊字符会按UTF-8转义
     */
    public static String removeAnyHost(String url) {
        try {
            URIBuilder uriBuilder = new URIBuilder(url);
            // 移除scheme和host，移除host后构建URI时会同时忽略port
            uriBuilder.setScheme(null);
            uriBuilder.setHost(null);
            URI uri = uriBuilder.build();
            // 输出转义特殊字符后的URI
            return uri.toASCIIString();
        } catch (URISyntaxException e) {
            throw new IllegalArgumentException("输入的url不是合法的URI格式: " + url, e);
        }
    }

    /**
     * url添加前缀，如果url已http开头，则不添加
     *
     * @param cdnHost     域名
     * @param relativeUrl url地址
     * @return 结果
     */
    public static String addCdnHost(String cdnHost, String relativeUrl) {
        return addCdnHostAndPrefix(cdnHost, relativeUrl, null);
    }

    /**
     * 拼接用户头像地址
     *
     * @param cdnHost 域名
     * @param url     头像地址
     * @return 完整地址
     */
    public static String getAvatarUrl(String cdnHost, String url) {
        return addCdnHostAndPrefix(cdnHost, url, "user");
    }

    /**
     * 拼接图片域名地址
     *
     * @param cdnHost 域名
     * @param url     图片地址
     * @return 完整地址
     */
    public static String getImageUrl(String cdnHost, String url) {
        return addCdnHostAndPrefix(cdnHost, url, null);
    }

    /**
     * 设置url的cdnHost，即添加url的scheme、host和port，如果原始url已存在host则不做操作. 添加后的返回值如果有特殊字符会按UTF-8转义。
     *
     * @param url 原始url值
     * @return 添加cdnHost后的url值，添加后的返回值如果有特殊字符会按UTF-8转义
     */
    private static String addCdnHostAndPrefix(String cdnHost, String url, String prefix) {
        try {
            if (StringUtils.isBlank(url) || url.startsWith("http")) {
                return url;
            }
            //理论上高版本的URI会自动匹配"/"，保险期间做一份兼容
            cdnHost = cdnHost.endsWith("/") ? cdnHost.substring(0, cdnHost.length() - 1) : cdnHost;
            url = url.startsWith("/") ? url.substring(1) : url;

            //如果前缀不等于空，就拼接一个前缀
            if (StringUtils.isNotBlank(prefix)) {
                prefix = prefix.startsWith("/") ? prefix.substring(1) : prefix;
                url = prefix + "/" + url;
            }

            URIBuilder uriBuilderFromCdnHost = new URIBuilder(cdnHost);
            URIBuilder uriBuilderFromInputUrl = new URIBuilder(url);
            // 从cdnHost复制scheme、host和port，以便适配https、http等多种协议
            uriBuilderFromInputUrl.setScheme(uriBuilderFromCdnHost.getScheme());
            uriBuilderFromInputUrl.setHost(uriBuilderFromCdnHost.getHost());
            uriBuilderFromInputUrl.setPort(uriBuilderFromCdnHost.getPort());
            URI uri = uriBuilderFromInputUrl.build();
            // 输出转义特殊字符后的URI
            return uri.toASCIIString();
        } catch (URISyntaxException e) {
            throw new IllegalArgumentException("输入的url不是合法的URI格式: " + url, e);
        }
    }

    /**
     * 移除url的host，即移除scheme、host和port，只保留path、query和fragment，该方法适用于数据库可选的URL字段。<b>注意：</b>
     * <ul>
     *     <li>返回值以斜杆"/"开头。</li>
     *     <li>如果url已经包含host，则会覆盖原有host。</li>
     * </ul>
     *
     * @param url url地址
     * @return 移除host后的url路径
     */
    public static String removeHostOrEmpty(String url) {
        if (StringUtils.isBlank(url)) {
            return StringUtils.EMPTY;
        }
        return removeAnyHost(url);
    }

    /**
     * 移除url的host和开头的斜杆"/", 该方法适用于某些业务数据库存储的URL是不以斜杆"/"开头的路径. 如果输入的url为null或空白字符串, 则返回空字符串.
     *
     * @param url url地址
     * @return 移除host和开头的斜杆"/"后的url路径
     */
    public static String removeHostAndStartSlashOrEmpty(String url) {
        String path = removeHostOrEmpty(url);
        return path.startsWith("/") ? path.substring(1) : path;
    }

    /**
     * 添加host，即scheme、host和port的部分，该方法适用于数据库可选的URL字段. <b>注意：</b>
     * <ul>
     *     <li>如果路径已经包含host，则会覆盖原有host。</li>
     *     <li>如果路径为空，则返回空字符串。</li>
     * </ul>
     *
     * @param relativePath url相对路径
     * @param host         域名，包含scheme、host和port，比如{@code https://https://cdnoffice.lizhi.fm}
     * @return 结果
     */
    public static String addHostOrEmpty(String relativePath, String host) {
        if (StringUtils.isBlank(relativePath)) {
            return StringUtils.EMPTY;
        }
        return addCdnHost(host, relativePath);
    }

    /**
     * 判断url是否包含域名
     *
     * @param url url
     * @return 结果，true表示包含域名，false表示不包含域名
     */
    public static boolean containsDomain(String url) {
        // 正则表达式用于匹配域名
        String domainRegex = "((http|https)://)?([a-zA-Z0-9\\-]+\\.[a-zA-Z]{2,})(/[\\w-./?%&=]*)?";
        Pattern pattern = Pattern.compile(domainRegex);
        Matcher matcher = pattern.matcher(url);
        return matcher.find();
    }

    public static String encodeUrl(String url) {
        try {
            if (StringUtils.isBlank(url)) {
                return "";
            }
            return URLEncoder.encode(url, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            log.error("encodeUrl error. url={}", url, e);
            return "";
        }
    }
}
