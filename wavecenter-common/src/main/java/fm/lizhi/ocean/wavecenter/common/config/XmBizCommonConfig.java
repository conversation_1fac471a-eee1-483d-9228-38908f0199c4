package fm.lizhi.ocean.wavecenter.common.config;

import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.common.utils.PlaceholderUtils;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/4/10 16:39
 */
@Data
public class XmBizCommonConfig implements BizCommonConfig{

    private String cdnHost = "https://cdnoffice.lizhi.fm";

    private String riskAppId = "ximi";

    private String auditSceneName = "{\"1560005\":\"-\",\"1560004\":\"-\",\"1530003\":\"私信消息\",\"1560002\":\"私信消息\",\"1560018\":\"-\",\"1560008\":\"-\",\"1560017\":\"-\",\"1560006\":\"-\",\"1560016\":\"-\"}";

    private long sendChatUid = 1295741558179497474L;

    private RomeConfig romeConfig = new RomeConfig(String.valueOf(BusinessEvnEnum.XIMI.getAppId()), "http://romefs.yfxn.lizhi.fm/");
    /**
     * 资源转存路径
     */
    private String resourceTransferUrlPrefix = "/publicTransfer/${businessName}/${fileName}";

    @Override
    public String getResourceTransferUrlPrefix(String fileName) {
        return PlaceholderUtils.replace(
                resourceTransferUrlPrefix,
                "businessName", BusinessEvnEnum.XIMI.getName(),
                "fileName", fileName
        );
    }
}
