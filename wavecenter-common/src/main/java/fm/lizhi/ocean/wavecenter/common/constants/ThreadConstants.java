package fm.lizhi.ocean.wavecenter.common.constants;

import fm.lizhi.ocean.wavecenter.base.util.ThreadUtils;

import java.util.concurrent.ExecutorService;

/**
 * <AUTHOR>
 * @date 2024/5/29 08:23
 */
public class ThreadConstants {

    /**
     * 公会数据线程池
     */
    public static final ExecutorService guildDatePool = ThreadUtils.getTtlExecutors("guild-data-pool", 50, 50);

    /**
     * 厅数据线程池
     */
    public static final ExecutorService roomDatePool = ThreadUtils.getTtlExecutors("room-data-pool", 50, 50);


    /**
     * 龙虎榜线程池
     */
    public static final ExecutorService rankDatePool = ThreadUtils.getTtlExecutors("rank-data-pool", 100, 100);

    /**
     * 公会收益线程池
     */
    public static final ExecutorService guildIncomePool = ThreadUtils.getTtlExecutors("guild-income-pool", 50, 50);


    /**
     * 公会首页线程池
     */
    public static final ExecutorService guildHomePool = ThreadUtils.getTtlExecutors("guild-home-pool", 50, 50);

    /**
     * 厅首页线程池
     */
    public static final ExecutorService roomHomePool = ThreadUtils.getTtlExecutors("room-home-pool", 50, 50);

    /**
     * 推荐卡批量发放线程池
     */
    public static final ExecutorService batchSendRecommendCardPool = ThreadUtils.getTtlExecutors("batchSendRecommendCard", 2000);
}
