package fm.lizhi.ocean.wavecenter.common.dto;

import lombok.Data;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/10 14:31
 */
@Data
public class PageDto<T> {

    private int total = 0;

    private List<T> list;

    public PageDto() {
    }

    public PageDto(int total, List<T> list) {
        this.total = total;
        this.list = list;
    }

    public static <T> PageDto<T> of(int total, List<T> list) {
        return new PageDto<>(total, list);
    }

    public static <T> PageDto<T> empty() {
        return new PageDto<>(0, Collections.emptyList());
    }
}
