package fm.lizhi.ocean.wavecenter.common.utils;

import com.ctrip.framework.apollo.core.enums.Env;
import com.lizhi.commons.config.core.util.ConfigUtils;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
public class KafkaMsgUtils {

    /**
     * 去掉消息头 这种消息拼接了一段非正常格式数据
     * 422794.168.16867232996306958|17|422794|422794|4896|#/live/livePolling|#/live/livePolling|422794.168.16867232996306959!%#{"appId":57333013,"userId":1368304803012168450}
     * @param sourceMsg
     * @return
     */
    public static String removeKafkaMsgCICDHead(String sourceMsg) {
        // CICD 的头
        Env env = ConfigUtils.getEnv();
        if (env == Env.DEV || env == Env.TEST || env == Env.LOCAL) {
            if (!sourceMsg.startsWith("{")) {
                String[] msgArr = sourceMsg.split("!%#");
                String msg = msgArr[msgArr.length - 1];
                return msg;
            }
        }
        return sourceMsg;
    }
}
