<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration
        PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">
<generatorConfiguration>
    <context id="mysql" targetRuntime="MyBatis3Simple">
        <property name="javaFileEncoding" value="UTF-8"/>
        <property name="javaFormatter" value="org.mybatis.generator.api.dom.DefaultJavaFormatter"/>
        <property name="beginningDelimiter" value="`"/>
        <property name="endingDelimiter" value="`"/>
        <!-- datastore插件 -->
        <!--
            targetProject: 生成文件的输出目录。
            targetPackage: 生成Mapper接口的包名。
            mapperSuffix: 生成Mapper接口名后缀，比如指定Mapper，实体类名为Order，则生成的Mapper类名为OrderMapper。
            namespace: 数据库配置的namespace，会自动增加到生成的Mapper接口上。
            enableLombok: 是否使用Lombok，默认false
                true：会在生成的实体类增加lombok相关的@Setter、@Getter、@ToString和@EqualsAndHashCode等注解；
                false：生成setter和getter方法。
        -->
        <plugin type="fm.lizhi.common.datastore.mysql.mybatis.generator.DataStoreMybatisPlugin">
            <property name="targetProject" value="src/main/java"/>
            <property name="targetPackage"
                      value="fm.lizhi.ocean.wavecenter.infrastructure.anchor.singer.datastore.mapper"/>
            <property name="mapperSuffix" value="Mapper"/>
            <property name="namespace" value="mysql_ocean_wavecenter"/>
            <property name="enableLombok" value="true"/>
            <property name="enableExampleClass" value="true"/>
        </plugin>

        <!-- 数据库实体类注释生成器 -->
        <commentGenerator type="fm.lizhi.common.datastore.mysql.mybatis.generator.DataStoreCommentGenerator">
        </commentGenerator>

        <!-- 数据库配置 -->
        <!--        <jdbcConnection driverClass="com.mysql.jdbc.Driver"-->
        <!--                        connectionURL="****************************************************************************************************"-->
        <!--                        userId="root"-->
        <!--                        password="db_admin#ops.fm">-->
        <!--            <property name="useInformationSchema" value="true"/>-->
        <!--        </jdbcConnection>-->
        <jdbcConnection driverClass="com.mysql.jdbc.Driver"
                        connectionURL="*************************************************************************************************"
                        userId="root"
                        password="db_admin#ops.fm">
            <property name="useInformationSchema" value="true"/>
        </jdbcConnection>
        <!--        <jdbcConnection driverClass="com.mysql.jdbc.Driver"-->
        <!--                        connectionURL="**************************************************************************************************"-->
        <!--                        userId="fmuser"-->
        <!--                        password="fmpass">-->
        <!--            <property name="useInformationSchema" value="true"/>-->
        <!--        </jdbcConnection>-->

        <!--        <jdbcConnection driverClass="com.mysql.jdbc.Driver"-->
        <!--                        connectionURL="**************************************************************************************************************"-->
        <!--                        userId="fmuser"-->
        <!--                        password="fmpass">-->
        <!--            <property name="useInformationSchema" value="true"/>-->
        <!--        </jdbcConnection>-->

        <javaTypeResolver type="fm.lizhi.common.datastore.mysql.mybatis.generator.DataStoreJavaTypeResolver">
            <!--
                true：DECIMAL/NUMERIC => BigDecimal
                false：default,
                    scale>0 length>18     => BigDecimal
                    scale=0 length[10,18] => Long
                    scale=0 length <= 9   => Integer
             -->
            <property name="forceBigDecimals" value="false"/>
        </javaTypeResolver>

        <!-- 实体类生成器配置 -->
        <javaModelGenerator targetPackage="fm.lizhi.ocean.wavecenter.infrastructure.anchor.singer.datastore.entity"
                            targetProject="src/main/java">
            <property name="constructorBased" value="false"/>
            <property name="enableSubPackages" value="true"/>
            <property name="immutable" value="false"/>
            <property name="trimStrings" value="false"/>
        </javaModelGenerator>

        <!-- 数据表配置 -->
        <!--
            schema: 数据库名
            tableName: 数据库中的表名
            domainObjectName: 生成的实体类名
        -->
        <!--        <table schema="ocean_wave" tableName="wavecenter_player_sign_charm_stat" domainObjectName="WcPlayerSignCharmStat">-->
        <!--            <property name="useActualColumnNames" value="false"/>-->
        <!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
        <!--        </table>-->
        <!--        <table schema="ocean_wave" tableName="player_sign" domainObjectName="PpPlayerSign">-->
        <!--            <property name="useActualColumnNames" value="false"/>-->
        <!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
        <!--        </table>-->

<!--        <table schema="ocean_wave" tableName="wavecenter_data_family_day" domainObjectName="WcDataFamilyDay">-->
<!--            <property name="useActualColumnNames" value="false"/>-->
<!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
<!--        </table>-->
<!--        <table schema="ocean_wave" tableName="wavecenter_data_family_month" domainObjectName="WcDataFamilyMonth">-->
<!--            <property name="useActualColumnNames" value="false"/>-->
<!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
<!--        </table>-->
<!--        <table schema="ocean_wave" tableName="wavecenter_data_family_week" domainObjectName="WcDataFamilyWeek">-->
<!--            <property name="useActualColumnNames" value="false"/>-->
<!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
<!--        </table>-->

        <!--        <table schema="ocean_wave" tableName="wavecenter_data_player_day" domainObjectName="WcDataPlayerDay">-->
        <!--            <property name="useActualColumnNames" value="false"/>-->
        <!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
        <!--        </table>-->
        <!--        <table schema="ocean_wave" tableName="wavecenter_data_player_month" domainObjectName="WcDataPlayerMonth">-->
        <!--            <property name="useActualColumnNames" value="false"/>-->
        <!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
        <!--        </table>-->

        <!--        <table schema="ocean_wave" tableName="wavecenter_data_player_week" domainObjectName="WcDataPlayerWeek">-->
        <!--            <property name="useActualColumnNames" value="false"/>-->
        <!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
        <!--        </table>-->
        <!--        <table schema="ocean_wave" tableName="wavecenter_data_player_room_day" domainObjectName="WcDataPlayerRoomDay">-->
        <!--            <property name="useActualColumnNames" value="false"/>-->
        <!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
        <!--        </table>-->
        <!--        <table schema="ocean_wave" tableName="wavecenter_data_player_room_month" domainObjectName="WcDataPlayerRoomMonth">-->
        <!--            <property name="useActualColumnNames" value="false"/>-->
        <!--            &lt;!&ndash;-->
        <!--                用于自动生成主键字段标识，需要自动生成（荔枝ID生成器）主键值时增加该属性:-->
        <!--                    column: 数据库中的主键字段名-->
        <!--            &ndash;&gt;-->
        <!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
        <!--        </table>-->

        <!--        <table schema="ocean_wave" tableName="wavecenter_data_player_room_week" domainObjectName="WcDataPlayerRoomWeek">-->
        <!--            <property name="useActualColumnNames" value="false"/>-->
        <!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
        <!--        </table>-->
        <!--        <table schema="ocean_wave" tableName="wavecenter_data_room_day" domainObjectName="WcDataRoomDay">-->
        <!--            <property name="useActualColumnNames" value="false"/>-->
        <!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
        <!--        </table>-->
        <!--        <table schema="ocean_wave" tableName="wavecenter_data_room_family_day" domainObjectName="WcDataRoomFamilyDay">-->
        <!--            <property name="useActualColumnNames" value="false"/>-->
        <!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
        <!--        </table>-->


        <!--        <table schema="ocean_wave" tableName="wavecenter_data_room_family_month" domainObjectName="WcDataRoomFamilyMonth">-->
        <!--            <property name="useActualColumnNames" value="false"/>-->
        <!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
        <!--        </table>-->

        <!--        <table schema="ocean_wave" tableName="wavecenter_data_room_family_week" domainObjectName="WcDataRoomFamilyWeek">-->
        <!--            <property name="useActualColumnNames" value="false"/>-->
        <!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
        <!--        </table>-->

        <!--        <table schema="ocean_wave" tableName="wavecenter_data_room_month" domainObjectName="WcDataRoomMonth">-->
        <!--            <property name="useActualColumnNames" value="false"/>-->
        <!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
        <!--        </table>-->
        <!--        <table schema="ocean_wave" tableName="wavecenter_data_room_week" domainObjectName="WcDataRoomWeek">-->
        <!--            <property name="useActualColumnNames" value="false"/>-->
        <!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
        <!--        </table>-->

        <!--        <table schema="ocean_wave" tableName="wavecenter_sign_flow" domainObjectName="WcSignFlow">-->
        <!--            <property name="useActualColumnNames" value="false"/>-->
        <!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
        <!--        </table>-->
        <!--        <table schema="ocean_wave" tableName="family_sign" domainObjectName="HyFamilySign">-->
        <!--            <property name="useActualColumnNames" value="false"/>-->
        <!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
        <!--            <columnOverride column="deleted" property="deleted" javaType="java.lang.Boolean"/>-->
        <!--        </table>-->

        <!-- <table schema="ocean_wave" tableName="activity_big_class" domainObjectName="ActivityBigClass">
            <property name="useActualColumnNames" value="false"/>
            <generatedKey column="id" sqlStatement="MySql" identity="true"/>
            <columnOverride column="deleted" property="deleted" javaType="java.lang.Boolean"/>
        </table> -->

        <!--          <table schema="ocean_wave" tableName="activity_flow_resource_give_record" domainObjectName="ActivityFlowResourceGiveRecord">-->
        <!--            <property name="useActualColumnNames" value="false"/>-->
        <!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
        <!--        </table>-->

        <!--        <table schema="ocean_wave" tableName="wavecenter_message_read_record" domainObjectName="WcMessageReadRecord">-->
        <!--            <property name="useActualColumnNames" value="false"/>-->
        <!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
        <!--        </table>-->

        <!-- 麦序福利相关表 -->
        <!--        <table schema="ocean_wave" tableName="wave_check_in_schedule" domainObjectName="WaveCheckInSchedule">-->
        <!--            <property name="useActualColumnNames" value="false"/>-->
        <!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
        <!--        </table>-->
        <!--        <table schema="ocean_wave" tableName="wave_check_in_record" domainObjectName="WaveCheckInRecord">-->
        <!--            <property name="useActualColumnNames" value="false"/>-->
        <!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
        <!--        </table>-->
        <!--        <table schema="ocean_wave" tableName="wave_check_in_user_task" domainObjectName="WaveCheckInUserTask">-->
        <!--            <property name="useActualColumnNames" value="false"/>-->
        <!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
        <!--            <columnOverride column="task_done" property="taskDone" javaType="java.lang.Boolean"/>-->
        <!--        </table>-->
        <!--        <table schema="ocean_wave" tableName="wave_check_in_un_done" domainObjectName="WaveCheckInUnDone">-->
        <!--            <property name="useActualColumnNames" value="false"/>-->
        <!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
        <!--        </table>-->
        <!--        <table schema="ocean_wave" tableName="wave_check_in_light_gift_record" domainObjectName="WaveCheckInLightGiftRecord">-->
        <!--            <property name="useActualColumnNames" value="false"/>-->
        <!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
        <!--        </table>-->

        <!-- 公会奖励 -->
        <!--        <table schema="ocean_wave" tableName="wavecenter_family_award_level_data" domainObjectName="WcFamilyAwardLevelData">-->
        <!--            <property name="useActualColumnNames" value="false"/>-->
        <!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
        <!--        </table>-->
        <!--        <table schema="ocean_wave" tableName="wavecenter_family_level_award_rule" domainObjectName="WcFamilyLevelAwardRule">-->
        <!--            <property name="useActualColumnNames" value="false"/>-->
        <!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
        <!--            <columnOverride column="deleted" property="deleted" javaType="java.lang.Boolean"/>-->
        <!--        </table>-->
        <!--        <table schema="ocean_wave" tableName="wavecenter_family_level_award_item" domainObjectName="WcFamilyLevelAwardItem">-->
        <!--            <property name="useActualColumnNames" value="false"/>-->
        <!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
        <!--            <columnOverride column="deleted" property="deleted" javaType="java.lang.Boolean"/>-->
        <!--        </table>-->
        <!--        <table schema="ocean_wave" tableName="wavecenter_family_special_recommend_card_name" domainObjectName="WcFamilySpecialRecommendCardName">-->
        <!--            <property name="useActualColumnNames" value="false"/>-->
        <!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
        <!--        </table>-->
        <!--        <table schema="ocean_wave" tableName="wavecenter_family_award_deliver_record" domainObjectName="WcFamilyAwardDeliverRecord">-->
        <!--            <property name="useActualColumnNames" value="false"/>-->
        <!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
        <!--        </table>-->
        <!--        <table schema="ocean_wave" tableName="wavecenter_family_award_deliver_execution" domainObjectName="WcFamilyAwardDeliverExecution">-->
        <!--            <property name="useActualColumnNames" value="false"/>-->
        <!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
        <!--        </table>-->
        <!--        <table schema="ocean_wave" tableName="wavecenter_family_award_deliver_item" domainObjectName="WcFamilyAwardDeliverItem">-->
        <!--            <property name="useActualColumnNames" value="false"/>-->
        <!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
        <!--        </table>-->
        <!--        <table schema="ocean_wave" tableName="wavecenter_data_family_week_data" domainObjectName="WcDataFamilyWeekData">-->
        <!--            <property name="useActualColumnNames" value="false"/>-->
        <!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
        <!--        </table>-->

<!--                <table schema="ocean_wave" tableName="singer_verify_record" domainObjectName="SingerVerifyRecord">-->
<!--                    <property name="useActualColumnNames" value="false"/>-->
<!--                    <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
<!--                    <columnOverride column="original_singer" property="originalSinger" javaType="java.lang.Boolean"/>-->
<!--                </table>-->


<!--        <table schema="ocean_wave" tableName="singer_operate_record" domainObjectName="SingerOperateRecord">-->
<!--            <property name="useActualColumnNames" value="false"/>-->
<!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
<!--            <columnOverride column="original_singer" property="originalSinger" javaType="java.lang.Boolean"/>-->
<!--        </table>-->

<!--                <table schema="ocean_wave" tableName="singer_audit_config" domainObjectName="SingerAuditConfig">-->
<!--                    <property name="useActualColumnNames" value="false"/>-->
<!--                    <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
<!--                    <columnOverride column="enabled" property="enabled" javaType="java.lang.Boolean"/>-->
<!--                    <columnOverride column="editable" property="editable" javaType="java.lang.Boolean"/>-->
<!--                </table>-->

<!--        <table schema="ocean_wave" tableName="singer_info" domainObjectName="SingerInfo">-->
<!--            <property name="useActualColumnNames" value="false"/>-->
<!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
<!--            <columnOverride column="original_singer" property="originalSinger" javaType="java.lang.Boolean"/>-->
<!--            <columnOverride column="rewards_issued" property="rewardsIssued" javaType="java.lang.Boolean"/>-->
<!--        </table>-->

<!--        <table schema="ocean_wave" tableName="singer_decorate_rule" domainObjectName="SingerDecorateRule">-->
<!--            <property name="useActualColumnNames" value="false"/>-->
<!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
<!--            <columnOverride column="enabled" property="enabled" javaType="java.lang.Boolean"/>-->
<!--            <columnOverride column="deleted" property="deleted" javaType="java.lang.Boolean"/>-->
<!--        </table>-->

<!--        <table schema="ocean_wave" tableName="singer_decorate_flow" domainObjectName="SingerDecorateFlow">-->
<!--            <property name="useActualColumnNames" value="false"/>-->
<!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
<!--            <columnOverride column="recycled" property="recycled" javaType="java.lang.Boolean"/>-->
<!--        </table>-->

<!--        <table schema="ocean_wave" tableName="singer_sing_hall_apply_record" domainObjectName="SingerSingHallApplyRecord">-->
<!--            <property name="useActualColumnNames" value="false"/>-->
<!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
<!--            <columnOverride column="deleted" property="deleted" javaType="java.lang.Boolean"/>-->
<!--        </table>-->

<!--        <table schema="ocean_wave" tableName="singer_data_room_day" domainObjectName="SingerDataRoomDay">-->
<!--            <property name="useActualColumnNames" value="false"/>-->
<!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
<!--        </table>-->

<!--        <table schema="ocean_wave" tableName="singer_data_room_day" domainObjectName="SingerDataRoomDay">-->
<!--            <property name="useActualColumnNames" value="false"/>-->
<!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
<!--        </table>-->

        <table schema="ocean_wave" tableName="singer_data_day" domainObjectName="SingerDataDay">
            <property name="useActualColumnNames" value="false"/>
            <generatedKey column="id" sqlStatement="MySql" identity="true"/>
            <columnOverride column="enabled" property="enabled" javaType="java.lang.Boolean"/>
            <columnOverride column="original_singer" property="originalSinger" javaType="java.lang.Boolean"/>
        </table>

<!--            <table schema="ocean_wave" tableName="singer_audit_chat_config" domainObjectName="SingerAuditChatConfig">-->
<!--                <property name="useActualColumnNames" value="false"/>-->
<!--                <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
<!--            </table>-->

<!--        <table schema="ocean_wave" tableName="activity_template_info" domainObjectName="ActivityTemplateInfo">-->
<!--            <property name="useActualColumnNames" value="false"/>-->
<!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
<!--            <columnOverride column="deleted" property="deleted" javaType="java.lang.Boolean"/>-->
<!--            <columnOverride column="hot_rec" property="hotRec" javaType="java.lang.Boolean"/>-->
<!--            <columnOverride column="ai_gen" property="aiGen" javaType="java.lang.Boolean"/>-->
<!--        </table>-->
<!--        <table schema="ocean_wave" tableName="activity_template_info" domainObjectName="ActivityTemplateInfo">-->
<!--            <property name="useActualColumnNames" value="false"/>-->
<!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
<!--            <columnOverride column="deleted" property="deleted" javaType="java.lang.Boolean"/>-->
<!--            <columnOverride column="hot_rec" property="hotRec" javaType="java.lang.Boolean"/>-->
<!--            <columnOverride column="ai_gen" property="aiGen" javaType="java.lang.Boolean"/>-->
<!--        </table>-->

<!--        <table schema="ocean_wave" tableName="activity_template_status_task" domainObjectName="ActivityTemplateStatusTask">-->
<!--            <property name="useActualColumnNames" value="false"/>-->
<!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
<!--        </table>-->
<!--        <table schema="ocean_wave" tableName="activity_big_class_category" domainObjectName="ActivityBigClassCategory">-->
<!--            <property name="useActualColumnNames" value="false"/>-->
<!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
<!--        </table>-->

<!--        <table schema="ocean_wave" tableName="activity_report_data_gift" domainObjectName="ActivityReportDataGift">-->
<!--            <property name="useActualColumnNames" value="false"/>-->
<!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
<!--        </table>-->

<!--        <table schema="ocean_wave" tableName="activity_report_data_player" domainObjectName="ActivityReportDataPlayer">-->
<!--            <property name="useActualColumnNames" value="false"/>-->
<!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
<!--        </table>-->


<!--        <table schema="ocean_wave" tableName="activity_notice_config" domainObjectName="ActivityNoticeConfig">-->
<!--            <property name="useActualColumnNames" value="false"/>-->
<!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
<!--        </table>-->

<!--        <table schema="ocean_wave" tableName="activity_notice_category_relation" domainObjectName="ActivityNoticeCategoryRelation">-->
<!--            <property name="useActualColumnNames" value="false"/>-->
<!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
<!--        </table>-->


<!--        <table schema="ocean_wave" tableName="activity_tools_info" domainObjectName="ActivityToolsInfo">-->
<!--            <property name="useActualColumnNames" value="false"/>-->
<!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
<!--        </table>-->

<!--        <table schema="ocean_wave" tableName="activity_apply_info" domainObjectName="ActivityApplyInfo">-->
<!--            <property name="useActualColumnNames" value="false"/>-->
<!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
<!--            <columnOverride column="notify_report_status" property="notifyReportStatus" javaType="java.lang.Boolean"/>-->
<!--        </table>-->

        <!-- AI生图评分 -->
<!--        <table schema="ocean_wave" tableName="activity_ai_result_rate" domainObjectName="ActivityAiResultRate">-->
<!--            <property name="useActualColumnNames" value="false"/>-->
<!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
<!--        </table>-->
    </context>
</generatorConfiguration>
