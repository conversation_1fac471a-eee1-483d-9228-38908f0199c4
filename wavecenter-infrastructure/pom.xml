<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>fm.lizhi.ocean.wavecenter</groupId>
        <artifactId>lz-ocean-wavecenter</artifactId>
        <version>1.5.3-SNAPSHOT</version>
    </parent>

    <artifactId>wavecenter-infrastructure</artifactId>

    <properties>
        <!-- 跳过安装 -->
<!--        <maven.install.skip>true</maven.install.skip>-->
        <!-- 跳过部署 -->
        <maven.deploy.skip>true</maven.deploy.skip>
    </properties>

    <dependencies>
        <!-- ====================        内部依赖        ==================== -->

        <dependency>
            <groupId>fm.lizhi.ocean.wavecenter</groupId>
            <artifactId>wavecenter-service</artifactId>
            <version>${project.version}</version>
        </dependency>

        <!-- ====================        创作者服务依赖        ==================== -->

        <dependency>
            <groupId>fm.lizhi.ocean.wave</groupId>
            <artifactId>user-export-api</artifactId>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.ocean.wave</groupId>
            <artifactId>user-export-pojo-api</artifactId>
        </dependency>

        <!-- ====================        平台服务依赖        ==================== -->

        <dependency>
            <groupId>fm.lizhi</groupId>
            <artifactId>lz-common-accountcenter-api</artifactId>
        </dependency>

        <dependency>
            <groupId>fm.lizhi</groupId>
            <artifactId>lz-account-security-api</artifactId>
        </dependency>

        <dependency>
            <groupId>fm.lizhi.common.verify</groupId>
            <artifactId>lz-commons-verify-api</artifactId>
        </dependency>

        <!-- ====================        基础架构的依赖        ==================== -->
        <dependency>
            <groupId>fm.lizhi.common</groupId>
            <artifactId>datastore-mysql-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.common</groupId>
            <artifactId>datastore-redis-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.common</groupId>
            <artifactId>dispatcher-executor</artifactId>
        </dependency>

        <dependency>
            <groupId>fm.lizhi</groupId>
            <artifactId>lz-common-trade-query-center-api</artifactId>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.common</groupId>
            <artifactId>kafka-client-spring-config</artifactId>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>fm.lizhi.commons</groupId>
            <artifactId>lz-commons-rome-push-cm-api</artifactId>
        </dependency>

        <!--===============        审核依赖        ==================== -->
        <dependency>
            <groupId>fm.lizhi</groupId>
            <artifactId>lz-content-review-api</artifactId>
        </dependency>

        <!-- ====================        西米服务依赖        ==================== -->
        <dependency>
            <groupId>fm.lizhi.xm</groupId>
            <artifactId>lz-xm-user-account-api</artifactId>
        </dependency>

        <dependency>
            <groupId>fm.lizhi.xm</groupId>
            <artifactId>lz-xm-family-api</artifactId>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.xm</groupId>
            <artifactId>lz-xm-room-api</artifactId>
        </dependency>


        <!-- ====================        黑叶依赖        ==================== -->
        <dependency>
            <groupId>fm.lizhi.hy</groupId>
            <artifactId>lz-hy-user-account-api</artifactId>
        </dependency>

        <dependency>
            <groupId>fm.hy.family</groupId>
            <artifactId>lz-hy-family-api</artifactId>
        </dependency>

        <dependency>
            <groupId>fm.lizhi.hy</groupId>
            <artifactId>lz-hy-activity-api</artifactId>
        </dependency>

        <dependency>
            <groupId>fm.lizhi.hy</groupId>
            <artifactId>lz-hy-content-api</artifactId>
        </dependency>

        <dependency>
            <groupId>fm.lizhi.hy</groupId>
            <artifactId>lz-hy-core-api</artifactId>
        </dependency>

        <!-- ====================        PP依赖        ==================== -->
        <dependency>
            <groupId>fm.pp.family</groupId>
            <artifactId>lz-pp-family-api</artifactId>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.pp</groupId>
            <artifactId>lz-pp-user-account-api</artifactId>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.trade</groupId>
            <artifactId>lz-trade-contract-api</artifactId>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.pp</groupId>
            <artifactId>lz-pp-security-api</artifactId>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.pp</groupId>
            <artifactId>lz-pp-common-util</artifactId>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.live</groupId>
            <artifactId>lz-pp-room-api</artifactId>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.pp</groupId>
            <artifactId>lz-pp-vip-api</artifactId>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.pp</groupId>
            <artifactId>lz-pp-content-api</artifactId>
        </dependency>

        <dependency>
            <groupId>fm.lizhi.pp</groupId>
            <artifactId>lz-pp-social-api</artifactId>
        </dependency>

        <dependency>
            <groupId>fm.lizhi.ocean.wave</groupId>
            <artifactId>wave-relocation-pp-api</artifactId>
        </dependency>

        <!-- ====================        XM依赖        ==================== -->
        <dependency>
            <groupId>fm.lizhi.xm</groupId>
            <artifactId>lz-xm-user-account-api</artifactId>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.xm</groupId>
            <artifactId>lz-xm-security-api</artifactId>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.ocean.wave</groupId>
            <artifactId>wave-relocation-xm-api</artifactId>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.xm</groupId>
            <artifactId>lz-xm-vip-api</artifactId>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>fm.lizhi.xm</groupId>
            <artifactId>lz-xm-content-api</artifactId>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.xm</groupId>
            <artifactId>lz-xm-social-api</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>lz-xm-core-api</artifactId>
                    <groupId>fm.lizhi.xm</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- ====================        HY依赖        ==================== -->
        <dependency>
            <groupId>fm.lizhi.hy</groupId>
            <artifactId>lz-hy-user-account-api</artifactId>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.security</groupId>
            <artifactId>lz-hy-security-api</artifactId>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.hy</groupId>
            <artifactId>lz-hy-room-api</artifactId>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.ocean.wave</groupId>
            <artifactId>wave-relocation-hy-api</artifactId>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.hy</groupId>
            <artifactId>lz-hy-vip-api</artifactId>
        </dependency>

        <dependency>
            <groupId>fm.lizhi.hy</groupId>
            <artifactId>lz-hy-social-api</artifactId>
        </dependency>

        <!-- ====================        交易中心依赖        ==================== -->
        <dependency>
            <groupId>fm.lizhi</groupId>
            <artifactId>lz-common-trade-query-center-api</artifactId>
        </dependency>

        <!-- ====================        支付依赖        ==================== -->
        <dependency>
            <groupId>fm.lizhi</groupId>
            <artifactId>lz-common-pay-settle-api</artifactId>
        </dependency>

    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.mybatis.generator</groupId>
                <artifactId>mybatis-generator-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

</project>
