package fm.lizhi.ocean.wavecenter.infrastructure.income.constants;

import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;

/**
 * <AUTHOR>
 * @date 2024/12/11 16:48
 */
public enum PayAppIdEnum {

    PP(BusinessEvnEnum.PP, "5022031727227306035")
    , HY(BusinessEvnEnum.HEI_YE, "")
    , XM(BusinessEvnEnum.XIMI, "")
    ;

    private BusinessEvnEnum businessEvnEnum;

    private String payAppId;

    PayAppIdEnum(BusinessEvnEnum businessEvnEnum, String payAppId) {
        this.businessEvnEnum = businessEvnEnum;
        this.payAppId = payAppId;
    }

    public BusinessEvnEnum getBusinessEvnEnum() {
        return businessEvnEnum;
    }

    public String getPayAppId() {
        return payAppId;
    }

    public static String getPayAppId(BusinessEvnEnum businessEvnEnum) {
        for (PayAppIdEnum value : values()) {
            if (value.businessEvnEnum == businessEvnEnum) {
                return value.payAppId;
            }
        }
        return "";
    }
}
