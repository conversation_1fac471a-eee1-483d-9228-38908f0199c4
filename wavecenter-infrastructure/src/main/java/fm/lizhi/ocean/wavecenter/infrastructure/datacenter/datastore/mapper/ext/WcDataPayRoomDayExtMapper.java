package fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.mapper.ext;

import fm.lizhi.common.datastore.core.annotation.DataStore;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.entity.WcDataPayRoomDay;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/24 14:59
 */
@DataStore(namespace = "mysql_ocean_wavecenter")
public interface WcDataPayRoomDayExtMapper {

    /**
     * 保存收入数据
     * @param entity
     * @param columnNames 列名称
     * @param amount 负数需要拼接负号
     * @return
     */
    @Insert({
            "<script>"
            , "insert into wavecenter_data_pay_room_day (id, app_id, stat_date, stat_date_value, room_id, family_id, create_time, modify_time, deploy_env, "
                , "<foreach collection='columnNames' item='c' separator=','>"
                , "${c}"
                , "</foreach>"
            , ") VALUES (#{entity.id}, #{entity.appId}, #{entity.statDate}, #{entity.statDateValue}, #{entity.roomId}, #{entity.familyId}, #{entity.createTime}, #{entity.modifyTime}, #{entity.deployEnv}, "
                , "<foreach collection='columnNames' item='c' separator=','>"
                , "${amount}"
                , "</foreach>"
            , ")"
            , "</script>"
    })
    int insertForColumn(@Param("entity") WcDataPayRoomDay entity
            , @Param("columnNames") List<String> columnNames
            , @Param("amount") String amount);

    /**
     * 更新收入数据
     * @param entity
     * @param columnNames 列名
     * @param amount 负数需要拼接负号
     * @return
     */
    @Update({
            "<script>"
            , "update wavecenter_data_pay_room_day set modify_time=#{entity.modifyTime}, "
            , "<foreach collection='columnNames' item='c' separator=','>"
            , "${c} = ${c}${amount}"
            , "</foreach>"
            , "where app_id=#{entity.appId} and stat_date_value=#{entity.statDateValue} and deploy_env=#{entity.deployEnv} and room_id=#{entity.roomId}"
            , "</script>"
    })
    int updateForColumn(@Param("entity") WcDataPayRoomDay entity
            , @Param("columnNames") List<String> columnNames
            , @Param("amount") String amount);

}
