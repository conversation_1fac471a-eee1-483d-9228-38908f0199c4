package fm.lizhi.ocean.wavecenter.infrastructure.sign.job;

import fm.lizhi.common.job.dispatcher.executor.handler.JobExecuteContext;
import fm.lizhi.common.job.dispatcher.executor.handler.JobHandler;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wavecenter.service.sign.handler.HySignConfirmHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 黑叶签约合同状态同步定时任务
 * 对应业务的family/confirm接口
 * param = 1,20 (pageNo,PageSize)
 * <AUTHOR>
 * @date 2024/10/26 15:32
 */
@Slf4j
@Component
public class HySignConfirmJob implements JobHandler {

    @Autowired
    protected HySignConfirmHandler hySignConfirmHandler;

    @Override
    public void execute(JobExecuteContext jobExecuteContext) throws Exception {
        try {
            ContextUtils.setBusinessEvnEnum(BusinessEvnEnum.HEI_YE);
            int pageNo = 1;
            int pageSize = 20;

            String param = jobExecuteContext.getParam();
            if (StringUtils.isNotBlank(param)) {
                String[] paramData = param.split(",");
                if (paramData.length != 2) {
                    log.error("HySignConfirmJob param error. param: {}", param);
                    return;
                }
                pageNo = Integer.parseInt(paramData[0]);
                pageSize = Integer.parseInt(paramData[1]);
            }

            //只处理解约的，签约的业务有兜底定时任务处理
            hySignConfirmHandler.doConfirmSign(pageNo, pageSize);
            hySignConfirmHandler.doConfirmCancel(pageNo, pageSize);
        } catch (Exception e) {
            log.error("HySignConfirmJob execute error. param: {}", jobExecuteContext.getParam(), e);
        } finally {
            ContextUtils.clearContext();
        }
    }
}
