package fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.datastore.mapper;

import fm.lizhi.common.datastore.core.annotation.DataStore;
import org.apache.ibatis.annotations.Update;

import java.util.Date;

@DataStore(namespace = "mysql_ocean_wavecenter")
public interface ActivityOfficialSeatTimeExtraMapper {

    /**
     * 修改矢量
     *
     * @param appId     应用ID
     * @param startTime 时间
     * @param endTime   结束时间
     * @param maxCount  最大数量
     * @return 行数
     */
    @Update("update activity_official_seat_time set count = count + 1 where app_id = #{appId} " +
            "and start_time =#{startTime} and end_time = #{endTime} and seat =#{seat} and count <= #{maxCount} and deploy_env = #{deployEnv}")
    int updateCount(int appId, Date startTime, Date endTime, int seat, int maxCount, String deployEnv);

    /**
     * 减少官频位档期的数量
     *
     * @param appId     应用ID
     * @param startTime 时间
     * @param endTime   结束时间
     * @param seat      官频位
     * @return 行数
     */
    @Update("update activity_official_seat_time set count = count - 1 where app_id = #{appId} " +
            "and start_time =#{startTime} and end_time = #{endTime} and seat =#{seat} and count > 0 and deploy_env = #{deployEnv}")
    int decreasedCount(int appId, Date startTime, Date endTime, int seat, String deployEnv);
}
