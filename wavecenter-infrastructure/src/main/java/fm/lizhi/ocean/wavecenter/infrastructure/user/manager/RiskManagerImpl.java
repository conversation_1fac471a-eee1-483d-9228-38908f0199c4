package fm.lizhi.ocean.wavecenter.infrastructure.user.manager;

import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.fastjson.JSONObject;
import fm.lizhi.account.security.api.AntiCheatService;
import fm.lizhi.account.security.protocol.AccountSecurityProto;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.service.user.dto.LoginContextDto;
import fm.lizhi.ocean.wavecenter.service.user.dto.UserInfoDto;
import fm.lizhi.ocean.wavecenter.service.user.manager.RiskManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/4/12 20:49
 */
@Slf4j
@Component
public class RiskManagerImpl implements RiskManager {

    private static final String EVENT_LOGIN = "lz_login";
    private static final String FIELD_SUB_APPID = "subAppId";
    private static final String FIELD_NETWORK = "network";
    private static final String FIELD_SM_ID = "smId";
    private static final String FIELD_PHONE = "phone";
    private static final String FIELD_PLATFORM = "platform";
    private static final String FIELD_APP_VERSION = "appVersion";
    private static final String FIELD_EMAIL = "email";
    private static final String FIELD_CHANNEL = "channel";

    private static final String RESULT_PASS = "PASS";

    @Autowired
    private AntiCheatService antiCheatService;

    @Override
    public boolean loginAntiRush(UserInfoDto userInfoDto, LoginContextDto context) {

        JSONObject riskParamsJson = new JSONObject();
        riskParamsJson.put(FIELD_SUB_APPID, context.getSubAppId());
        riskParamsJson.put(FIELD_NETWORK, context.getNetwork());
        riskParamsJson.put(FIELD_SM_ID, "");
        riskParamsJson.put(FIELD_PHONE, StringUtils.isEmpty(userInfoDto.getPhoneNum()) ? "" : userInfoDto.getPhoneNum());
        riskParamsJson.put(FIELD_PLATFORM, context.getDeviceType());
        riskParamsJson.put(FIELD_APP_VERSION, context.getClientVersion());
        riskParamsJson.put(FIELD_EMAIL, context.getAuthAccountId());
        riskParamsJson.put(FIELD_CHANNEL, context.getChannelId());
        log.info("isLoginRisk, context={}, riskParamsJson={}", context, riskParamsJson.toJSONString());

        Result<AccountSecurityProto.ResponseGuardActivityAntiRush> result =
                antiCheatService.guardActivityAntiRush(context.getRiskAppId(), EVENT_LOGIN, userInfoDto.getId(),
                        context.getDeviceId(), context.getClientIp(), 0L,
                        riskParamsJson.toJSONString());

        if (RpcResult.isSuccess(result)) {
            String riskLevel = result.target().getRiskLevel();
            if (!RESULT_PASS.equals(riskLevel)) {
                String riskDesc = result.target().getDesc();
                log.warn("riskDesc={}, context={}", riskDesc, context);
                return true;
            }
        } else {
            log.warn("isLoginRisk fail, rCode={}, context={}", result.rCode(), context);
        }
        return false;
    }
}
