package fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.manager;

import static fm.lizhi.ocean.wavecenter.api.activitycenter.service.ActivityReportDataService.GET_REPORT_DETAIL_FAIL;
import static fm.lizhi.ocean.wavecenter.api.activitycenter.service.ActivityReportDataService.GET_REPORT_SUMMARY_FAIL;
import static fm.lizhi.ocean.wavecenter.api.activitycenter.service.ActivityReportDataService.PAGE_REPORT_GIFT_FAIL;
import static fm.lizhi.ocean.wavecenter.api.activitycenter.service.ActivityReportDataService.PAGE_REPORT_PLAYER_FAIL;

import java.util.Collections;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import cn.hutool.core.collection.CollUtil;
import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityReportDataDetailBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityReportDataGiftBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityReportDataPlayerBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityReportDataSummaryBean;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.convert.ActivityReportDataConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.datastore.dao.ActivityReportDataDao;
import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.datastore.entity.ActivityReportDataDetail;
import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.datastore.entity.ActivityReportDataGift;
import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.datastore.entity.ActivityReportDataPlayer;
import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.datastore.entity.ActivityReportDataSummary;
import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.datastore.entity.ActivityReportDataSummaryExample;
import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.datastore.mapper.ActivityReportDataSummaryMapper;
import fm.lizhi.ocean.wavecenter.service.activitycenter.manager.ActivityReportDataManager;
import lombok.extern.slf4j.Slf4j;

/**
 * 活动数据
 * <AUTHOR>
 */
@Component
@Slf4j
public class ActivityReportDataManagerImpl implements ActivityReportDataManager {

    @Autowired
    private ActivityReportDataDao activityReportDataDao;

    @Autowired
    private ActivityReportDataSummaryMapper activityReportDataSummaryMapper;


    @Override
    public Result<ActivityReportDataSummaryBean> getReportSummary(Long activityId, int appId) {
        if (activityId == null || activityId <= 0) {
            log.warn("get report summary fail, appId:{}, activityId:{}", appId, activityId);
            return RpcResult.fail(GET_REPORT_SUMMARY_FAIL, "获取活动数据汇总失败");
        }

        ActivityReportDataSummary summary = activityReportDataDao.reportSummary(activityId, appId);
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, ActivityReportDataConvert.I.convertActivityReportDataSummary(summary));
    }

    @Override
    public Result<List<ActivityReportDataDetailBean>> getReportDetail(Long activityId, int appId) {

        if (activityId == null || activityId <= 0) {
            log.warn("get report detail fail, appId:{}, activityId:{}", appId, activityId);
            return RpcResult.fail(GET_REPORT_DETAIL_FAIL, "获取活动数据趋势图失败");
        }

        List<ActivityReportDataDetail> list = activityReportDataDao.reportDetail(activityId, appId);
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, ActivityReportDataConvert.I.convertActivityReportDataDetailList(list));
    }

    @Override
    public Result<PageBean<ActivityReportDataPlayerBean>> pageReportPlayer(Long activityId, int appId, int pageNo, int pageSize) {
        if (activityId == null || activityId <= 0) {
            log.warn("get report player fail, appId:{}, activityId:{}", appId, activityId);
            return RpcResult.fail(PAGE_REPORT_PLAYER_FAIL, "获取活动数据主播表现失败");
        }
        PageList<ActivityReportDataPlayer> pageList =  activityReportDataDao.reportPlayer(activityId, appId, pageNo, pageSize);
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, PageBean.of(pageList.getTotal(), ActivityReportDataConvert.I.convertActivityReportDataPlayerList(pageList)));
    }

    @Override
    public Result<PageBean<ActivityReportDataGiftBean>> pageReportGift(Long activityId, int appId, int pageNo, int pageSize) {
        if (activityId == null || activityId <= 0) {
            log.warn("get report gift fail, appId:{}, activityId:{}", appId, activityId);
            return RpcResult.fail(PAGE_REPORT_GIFT_FAIL, "获取活动数据送礼明细失败");
        }
        PageList<ActivityReportDataGift> pageList =  activityReportDataDao.reportGift(activityId, appId, pageNo, pageSize);
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, PageBean.of(pageList.getTotal(), ActivityReportDataConvert.I.convertActivityReportDataGiftList(pageList)));
    }

    @Override
    public List<ActivityReportDataSummaryBean> batchGetReportSummary(List<Long> activityIds) {

        if (CollUtil.isEmpty(activityIds)){
            return Collections.emptyList();
        }

        ActivityReportDataSummaryExample example = new ActivityReportDataSummaryExample();
        example.createCriteria().andActivityIdIn(activityIds);

        List<ActivityReportDataSummary> list = activityReportDataSummaryMapper.selectByExample(example);
        return ActivityReportDataConvert.I.convertActivityReportDataSummaryList(list);
    }
}
