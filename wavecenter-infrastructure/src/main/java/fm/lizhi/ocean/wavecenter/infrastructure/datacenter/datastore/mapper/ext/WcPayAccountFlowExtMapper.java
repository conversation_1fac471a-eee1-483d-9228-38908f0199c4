package fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.mapper.ext;

import fm.lizhi.common.datastore.core.annotation.DataStore;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

/**
 * <AUTHOR>
 * @date 2025/4/25 10:18
 */
@DataStore(namespace = "mysql_ocean_wavecenter")
public interface WcPayAccountFlowExtMapper {

    @Update("update wavecenter_pay_account_flow set consumer_times=consumer_times+1, last_consumer_time=now() where flow_id=#{flowId}")
    int updateConsumer(@Param("flowId") Long flowId);

}
