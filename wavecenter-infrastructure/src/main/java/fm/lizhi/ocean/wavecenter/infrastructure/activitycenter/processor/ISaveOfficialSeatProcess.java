package fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.processor;

import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.DeleteOfficialSeatParamDTO;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.OfficialSeatDetailDTO;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.SaveOfficialSeatParamDTO;
import fm.lizhi.ocean.wavecenter.service.common.processor.BusinessEnvAwareProcessor;
import org.apache.commons.lang3.tuple.Pair;

public interface ISaveOfficialSeatProcess<T extends OfficialSeatDetailDTO> extends BusinessEnvAwareProcessor {

    T buildSaveDTO(SaveOfficialSeatParamDTO param, T sameData);

    /**
     * 构建更新DTO
     *
     * @param param    请求参数
     * @param sameData 相同的数据
     * @return
     */
    T buildDeleteDTO(DeleteOfficialSeatParamDTO param, T sameData);

    /**
     * 校验数据合法性，是否可以配置
     *
     * @param sameData 相同的数据
     * @return left: 是否合法， right: 不合法原因
     */
    Pair<Boolean, String> checkDataValid(SaveOfficialSeatParamDTO param, T sameData);

    /**
     * 是否直接删除官频位记录
     *
     * @param param 参数
     * @param seatData 官频位数据
     * @return true: 是， false: 否
     */
    boolean isDirectDeleteSeatRecord(DeleteOfficialSeatParamDTO param, T seatData);

    default Class<? extends BusinessEnvAwareProcessor> getBaseBusinessProcessor() {
        return ISaveOfficialSeatProcess.class;
    }

}
