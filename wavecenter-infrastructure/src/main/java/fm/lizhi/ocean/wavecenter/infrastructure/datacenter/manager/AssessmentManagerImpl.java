package fm.lizhi.ocean.wavecenter.infrastructure.datacenter.manager;

import fm.lizhi.ocean.wavecenter.common.utils.WcAssert;
import fm.lizhi.ocean.wavecenter.infrastructure.common.constants.PaySettlePeriodNumberEnum;
import fm.lizhi.ocean.wavecenter.infrastructure.common.manager.CreatorDataQueryCommonManager;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.convert.DataCenterInfraConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.income.manager.PaymentManager;
import fm.lizhi.ocean.wavecenter.service.common.dto.PaySettlePeriodDto;
import fm.lizhi.ocean.wavecenter.service.datacenter.dto.AssessTimeDto;
import fm.lizhi.ocean.wavecenter.service.datacenter.dto.PerformanceInfoDto;
import fm.lizhi.ocean.wavecenter.service.datacenter.manager.AssessmentManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * 考核服务
 * <AUTHOR>
 * @date 2024/4/18 20:00
 */
@Component
public class AssessmentManagerImpl implements AssessmentManager {

    @Autowired
    private CreatorDataQueryCommonManager creatorDataQueryCommonManager;
    @Autowired
    private PaymentManager paymentManager;

    @Override
    public AssessTimeDto getCurrentTime(int appId, long familyId) {
        PaySettlePeriodDto period = creatorDataQueryCommonManager.getSettlePeriod(appId, familyId, PaySettlePeriodNumberEnum.CURRENT_PERIOD);
        WcAssert.notNull(period, "period is null");
        return DataCenterInfraConvert.I.toAssessTimeDto(period);
    }

    @Override
    public AssessTimeDto getPreTime(int appId, long familyId) {
        PaySettlePeriodDto period = creatorDataQueryCommonManager.getSettlePeriod(appId, familyId, PaySettlePeriodNumberEnum.PRE_PERIOD);
        WcAssert.notNull(period, "period is null");
        return DataCenterInfraConvert.I.toAssessTimeDto(period);
    }

    @Override
    public Optional<PerformanceInfoDto> getPerformanceInfo(Long familyId, Long roomId) {
        return paymentManager.getPerformanceInfo(familyId, roomId);
    }
}
