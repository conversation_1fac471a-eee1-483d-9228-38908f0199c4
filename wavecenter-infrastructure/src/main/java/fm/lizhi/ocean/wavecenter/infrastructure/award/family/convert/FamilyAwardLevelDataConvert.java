package fm.lizhi.ocean.wavecenter.infrastructure.award.family.convert;

import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.ocean.wavecenter.common.convert.CommonConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.award.family.datastore.entity.WcFamilyAwardLevelData;
import fm.lizhi.ocean.wavecenter.service.award.family.dto.FamilyAwardLevelDataDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

import java.util.Date;

@Mapper(unmappedTargetPolicy = ReportingPolicy.ERROR,
        uses = {
                CommonConvert.class
        },
        imports = {
                ConfigUtils.class,
                Date.class,
        }
)
public interface FamilyAwardLevelDataConvert {

    FamilyAwardLevelDataConvert I = org.mapstruct.factory.Mappers.getMapper(FamilyAwardLevelDataConvert.class);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "deployEnv", expression = "java(ConfigUtils.getEnvRequired().name())")
    @Mapping(target = "createTime", expression = "java(new Date())")
    @Mapping(target = "modifyTime", expression = "java(new Date())")
    WcFamilyAwardLevelData toCreateEntity(FamilyAwardLevelDataDTO dto);
}
