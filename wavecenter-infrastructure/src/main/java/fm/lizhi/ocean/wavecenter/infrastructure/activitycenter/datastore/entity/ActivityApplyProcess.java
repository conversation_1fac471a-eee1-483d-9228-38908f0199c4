package fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.datastore.entity;

import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 活动申请-流程表
 *
 * @date 2024-10-11 11:55:04
 */
@Table(name = "`activity_apply_process`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ActivityApplyProcess {
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    @Column(name= "`activity_id`")
    private Long activityId;

    @Column(name= "`name`")
    private String name;

    /**
     * 时长
     */
    @Column(name= "`duration`")
    private String duration;

    /**
     * 说明
     */
    @Column(name= "`explanation`")
    private String explanation;

    /**
     * 步骤
     */
    @Column(name= "`index`")
    private Integer index;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", activityId=").append(activityId);
        sb.append(", name=").append(name);
        sb.append(", duration=").append(duration);
        sb.append(", explanation=").append(explanation);
        sb.append(", index=").append(index);
        sb.append("]");
        return sb.toString();
    }
}