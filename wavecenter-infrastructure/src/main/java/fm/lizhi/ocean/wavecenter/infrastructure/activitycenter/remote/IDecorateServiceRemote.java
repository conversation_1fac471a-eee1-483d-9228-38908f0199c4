package fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.remote;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.infrastructure.common.remote.IRemote;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.DecorateInfoDTO;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.SendDecorateParamDTO;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.SendDecorateRecordDTO;

public interface IDecorateServiceRemote extends IRemote {

    /**
     * 发放装饰
     *
     * @param param 参数
     * @return 结果
     */
    Result<Void> sendDecorate(SendDecorateParamDTO param);

    /**
     * 获取装饰信息
     *
     * @param decorateId 装饰ID
     * @return 结果
     */
    Result<DecorateInfoDTO> getDecorateInfo(long decorateId);

    /**
     * 参数错误
     */
    int SEND_DECORATED_PARAM_ERROR = 1;

    /**
     * 发放失败(可重试)
     */
    int SEND_DECORATED_FAIL = 2;

    /**
     * 装饰不存在
     */
    int SEND_DECORATED_DECORATE_NO_EXIST = 3;

    /**
     * 获取装扮失败
     */
    int GET_DECORATED_FAIL = 1;

}
