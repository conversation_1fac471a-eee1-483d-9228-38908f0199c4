package fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.datastore.dao;

import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityResourceTypeEnum;
import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.datastore.entity.ActivityResourceGiveRecord;
import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.datastore.entity.ActivityResourceGiveRecordExample;
import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.datastore.mapper.ActivityResourceGiveRecordMapper;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.UpdateResourceRecordInfoDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class ActivityResourceGiveDao {

    @Autowired
    private ActivityResourceGiveRecordMapper activityResourceGiveRecordMapper;

    /**
     * 批量保存
     *
     * @param records 记录列表
     * @return 结果
     */
    public boolean batchSaveRecords(List<ActivityResourceGiveRecord> records) {
        if (CollectionUtils.isEmpty(records)) {
            return true;
        }

        return activityResourceGiveRecordMapper.batchInsert(records) == records.size();
    }

    public boolean updateResourceRecordInfo(UpdateResourceRecordInfoDTO dto) {
        ActivityResourceGiveRecordExample example = new ActivityResourceGiveRecordExample();
        example.createCriteria().andIdEqualTo(dto.getId()).andStatusEqualTo(dto.getOriginalStatus());
        ActivityResourceGiveRecord record = new ActivityResourceGiveRecord();
        record.setStatus(dto.getStatus());
        record.setId(dto.getId());
        if (dto.getTryCount() != null) {
            record.setTryCount(dto.getTryCount());
        }
        if (dto.getErrorMsg() != null) {
            record.setErrorMsg(dto.getErrorMsg());
        }
        if (dto.getErrorCode() != null) {
            record.setErrorCode(dto.getErrorCode());
        }

        return activityResourceGiveRecordMapper.updateByExample(record, example) > 0;
    }


    /**
     * 批量修改记录
     *
     * @param records 记录列表
     * @return 结果
     */
    public boolean batchUpdateRecords(List<ActivityResourceGiveRecord> records) {
        if (CollectionUtils.isEmpty(records)) {
            return true;
        }
        for (ActivityResourceGiveRecord record : records) {
            int res = activityResourceGiveRecordMapper.updateByPrimaryKey(record);
            if (res <= 0) {
                return false;
            }
        }

        return true;
    }

    /**
     * 查询活动资源领取记录
     *
     * @param activityId 活动ID
     * @return 结果
     */
    public List<ActivityResourceGiveRecord> getResourceGiveRecordList(Long activityId) {
        ActivityResourceGiveRecordExample example = new ActivityResourceGiveRecordExample();
        example.createCriteria().andActivityIdEqualTo(activityId);
        return activityResourceGiveRecordMapper.selectByExample(example);
    }

    /**
     * 根据活动ID查询头像框发放记录
     *
     * @param activityId 活动ID
     * @return 结果
     */
    public List<ActivityResourceGiveRecord> getDressUpResourceGiveRecordList(Long activityId) {
        ActivityResourceGiveRecordExample example = new ActivityResourceGiveRecordExample();
        example.createCriteria().andActivityIdEqualTo(activityId).andTypeEqualTo(ActivityResourceTypeEnum.DRESS_UP.getType());
        return activityResourceGiveRecordMapper.selectByExample(example);
    }

    /**
     * 批量修改资源发放记录状态
     *
     * @param records 记录列表
     * @return 结果
     */
    public boolean batchUpdateResourceGiveRecordStatus(List<ActivityResourceGiveRecord> records, Integer targetStatus) {
        if (CollectionUtils.isEmpty(records)) {
            return true;
        }

        for (ActivityResourceGiveRecord record : records) {
            ActivityResourceGiveRecordExample example = new ActivityResourceGiveRecordExample();
            example.createCriteria().andIdEqualTo(record.getId()).andStatusEqualTo(record.getStatus());

            ActivityResourceGiveRecord updateRecord = new ActivityResourceGiveRecord();
            updateRecord.setId(record.getId());
            updateRecord.setStatus(targetStatus);
            int row = activityResourceGiveRecordMapper.updateByExample(updateRecord, example);
            if (row <= 0) {
                return false;
            }
        }
        return true;
    }

    public boolean batchDeleteRecord(Long id, Integer type) {
        ActivityResourceGiveRecordExample example = new ActivityResourceGiveRecordExample();
        ActivityResourceGiveRecordExample.Criteria criteria = example.createCriteria();
        criteria.andActivityIdEqualTo(id);
        if (type != null) {
            criteria.andTypeEqualTo(type);
        }
        activityResourceGiveRecordMapper.deleteByExample(example);
        return true;
    }

    public boolean batchSaveRecord(List<ActivityResourceGiveRecord> resourceGiveRecords) {
        return activityResourceGiveRecordMapper.batchInsert(resourceGiveRecords) == resourceGiveRecords.size();
    }
}
