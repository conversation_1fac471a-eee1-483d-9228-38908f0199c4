package fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.remote.pp;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.remote.IBannerServiceRemote;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.BannerPlateDTO;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.DeleteBannerParamDTO;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.EditBannerParamDTO;
import fm.lizhi.pp.content.assistant.api.BannerManagementService;
import fm.lizhi.pp.content.assistant.constants.banner.BannerOperation;
import fm.lizhi.pp.content.assistant.protocol.BannerManagementProto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class PpBannerServiceRemote implements IBannerServiceRemote {

    @Autowired
    private BannerManagementService ppBannerManagementService;

    @Override
    public Result<Long> editBanner(EditBannerParamDTO param) {
        BannerPlateDTO bannerPlateDTO = param.getBannerPlateDTO();
        BannerManagementProto.BannerPlate bannerPlate = BannerManagementProto.BannerPlate.newBuilder()
                .setBannerPlateName(bannerPlateDTO.getBannerPlateName())
                .setIndex(bannerPlateDTO.getIndex()).setPlateTypeId(bannerPlateDTO.getPlateTypeId())
                .setBannerPlateId(bannerPlateDTO.getBannerPlateId())
                .build();

        BannerManagementProto.Banner.Builder builder = BannerManagementProto.Banner.newBuilder()
                .setAction(param.getAction())
                .setImgUrl(param.getImgUrl())
                .setScale(param.getScale())
                .setDropStatus(0)
                .setTitle(param.getTitle())
                .setStartTime(param.getStartTime())
                .setExpireTime(param.getExpireTime())
                .setWealthLevel("[-1,-1]")
                .setAndroidVersion("[-1,-1]")
                .setIosVersion("[-1,-1]")
                .setSeq(param.getSeq())
                .addBannerPlate(bannerPlate);
        if (param.getId() != null) {
            builder.setId(param.getId());
        }
        //该接口是业务的后台配置接口，rCode返回基本都是0，无法通过状态码确认是否成功
        Result<BannerManagementProto.ResponseEditBannerManagement> result = ppBannerManagementService.editBannerManagement(builder.build());
        return result.rCode() == 0 ? RpcResult.success(result.target().getId()) : RpcResult.fail(EDIT_BANNER_FAIL);
    }

    @Override
    public Result<Void> deleteBanner(DeleteBannerParamDTO param) {
        Result<BannerManagementProto.ResponseBannerManagementOperation> result = ppBannerManagementService.bannerManagementOperation(param.getId(), BannerOperation.DELETE.getStatus());
        if (RpcResult.isFail(result)) {
            return RpcResult.fail(DELETE_BANNER_FAIL);
        }
        return RpcResult.success();
    }

    @Override
    public BusinessEvnEnum getBusinessEvnEnum() {
        return BusinessEvnEnum.PP;
    }
}
