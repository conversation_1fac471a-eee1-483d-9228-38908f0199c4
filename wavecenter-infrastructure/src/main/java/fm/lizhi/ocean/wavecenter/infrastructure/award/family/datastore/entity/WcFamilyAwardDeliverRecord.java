package fm.lizhi.ocean.wavecenter.infrastructure.award.family.datastore.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 公会奖励发放记录
 *
 * @date 2025-03-25 04:09:01
 */
@Table(name = "`wavecenter_family_award_deliver_record`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WcFamilyAwardDeliverRecord {
    /**
     * id
     */
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 服务部署环境, TEST/PRE/PRO
     */
    @Column(name= "`deploy_env`")
    private String deployEnv;

    /**
     * 应用id
     */
    @Column(name= "`app_id`")
    private Integer appId;

    /**
     * 公会id
     */
    @Column(name= "`family_id`")
    private Long familyId;

    /**
     * 公会名称, 冗余
     */
    @Column(name= "`family_name`")
    private String familyName;

    /**
     * 公会长id
     */
    @Column(name= "`family_user_id`")
    private Long familyUserId;

    /**
     * 公会长昵称, 冗余
     */
    @Column(name= "`family_user_name`")
    private String familyUserName;

    /**
     * 奖励周期开始时间, 包含, 周一的00:00:00.000
     */
    @Column(name= "`award_start_time`")
    private Date awardStartTime;

    /**
     * 奖励周期结束时间, 包含, 周日的23:59:59.999
     */
    @Column(name= "`award_end_time`")
    private Date awardEndTime;

    /**
     * 发放时间
     */
    @Column(name= "`deliver_time`")
    private Date deliverTime;

    /**
     * 发放日期, 方便按天查询
     */
    @Column(name= "`deliver_date`")
    private Date deliverDate;

    /**
     * 状态, 1-发放中, 2-发放成功, 3-发放失败, 4-部分失败
     */
    @Column(name= "`status`")
    private Integer status;

    /**
     * 创建时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name= "`modify_time`")
    private Date modifyTime;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", deployEnv=").append(deployEnv);
        sb.append(", appId=").append(appId);
        sb.append(", familyId=").append(familyId);
        sb.append(", familyName=").append(familyName);
        sb.append(", familyUserId=").append(familyUserId);
        sb.append(", familyUserName=").append(familyUserName);
        sb.append(", awardStartTime=").append(awardStartTime);
        sb.append(", awardEndTime=").append(awardEndTime);
        sb.append(", deliverTime=").append(deliverTime);
        sb.append(", deliverDate=").append(deliverDate);
        sb.append(", status=").append(status);
        sb.append(", createTime=").append(createTime);
        sb.append(", modifyTime=").append(modifyTime);
        sb.append("]");
        return sb.toString();
    }
}