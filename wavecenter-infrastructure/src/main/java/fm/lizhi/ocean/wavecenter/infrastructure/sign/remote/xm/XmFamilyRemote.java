package fm.lizhi.ocean.wavecenter.infrastructure.sign.remote.xm;

import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.datastore.mapper.ext.XmFamilyExtMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.remote.IFamilyRemote;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/19 16:20
 */
@Component
public class XmFamilyRemote implements IFamilyRemote {

    @Autowired
    private XmFamilyExtMapper familyExtMapper;

    @Override
    public List<Long> getFamilyIdsByPage(Long lastFamilyId, Integer pageSize) {
        return familyExtMapper.getFamilyIdsByPage(lastFamilyId, pageSize);
    }

    @Override
    public BusinessEvnEnum getBusinessEvnEnum() {
        return BusinessEvnEnum.XIMI;
    }
}
