package fm.lizhi.ocean.wavecenter.infrastructure.anchor.singer.datastore.dao;

import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.ocean.wavecenter.infrastructure.anchor.singer.convert.SingerChatSceneConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.anchor.singer.datastore.entity.SingerAuditChatConfig;
import fm.lizhi.ocean.wavecenter.infrastructure.anchor.singer.datastore.entity.SingerAuditChatConfigExample;
import fm.lizhi.ocean.wavecenter.infrastructure.anchor.singer.datastore.mapper.SingerAuditChatConfigMapper;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.SingerChatSceneDTO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

@Component
public class SingerAuditChatConfigDao {

    @Autowired
    private SingerAuditChatConfigMapper singerAuditChatConfigMapper;

    /**
     * 查询审核私信
     *
     * @param appId      应用ID
     * @param singerType 歌手类型
     * @param sceneCode  场景码，可选
     * @return 配置列表
     */
    public List<SingerChatSceneDTO> getSingerAuditChatConfig(Integer appId, Integer singerType, String sceneCode) {
        SingerAuditChatConfig config = SingerAuditChatConfig.builder()
                .appId(appId)
                .singerType(singerType)
                .deployEnv(ConfigUtils.getEnvRequired().name())
                .build();
        if (StringUtils.isNotBlank(sceneCode)) {
            config.setSceneCode(sceneCode);
        }
        List<SingerAuditChatConfig> entities = singerAuditChatConfigMapper.selectMany(config);
        return entities.stream()
                .map(SingerChatSceneConvert.I::entityToDto)
                .collect(Collectors.toList());
    }

    /**
     * 新增审核私信配置
     *
     * @param dto 配置信息
     * @return 影响行数
     */
    public int insert(SingerChatSceneDTO dto) {
        SingerAuditChatConfig entity = SingerChatSceneConvert.I.dtoToEntity(dto);
        return singerAuditChatConfigMapper.insert(entity);
    }

    /**
     * 删除审核私信配置
     *
     * @param appId 应用ID
     * @param sceneCode 场景码
     * @return 影响行数
     */
    public int deleteByAppIdAndSceneCode(Integer appId, String sceneCode) {
        SingerAuditChatConfigExample example = new SingerAuditChatConfigExample();
        example.createCriteria()
                .andAppIdEqualTo(appId)
                .andSceneCodeEqualTo(sceneCode)
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name());
        return (int) singerAuditChatConfigMapper.deleteByExample(example);
    }

    /**
     * 修改审核私信配置
     *
     * @param dto 配置信息
     * @return 影响行数
     */
    public int updateByPrimaryKey(SingerChatSceneDTO dto) {
        SingerAuditChatConfig entity = SingerChatSceneConvert.I.dtoToEntity(dto);
        return singerAuditChatConfigMapper.updateByPrimaryKey(entity);
    }

    /**
     * 根据appId、singerType和sceneCode更新配置内容
     *
     * @param appId 应用ID
     * @param singerType 歌手类型
     * @param sceneCode 场景码
     * @param content 内容
     * @param actionUrl 跳转链接
     * @return 影响行数
     */
    public int updateByAppIdAndSceneCode(Integer appId, Integer singerType, String sceneCode, String content, String actionUrl) {
        SingerAuditChatConfigExample example = new SingerAuditChatConfigExample();
        example.createCriteria()
            .andAppIdEqualTo(appId)
            .andSingerTypeEqualTo(singerType)
            .andSceneCodeEqualTo(sceneCode)
            .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name());

        SingerAuditChatConfig record = new SingerAuditChatConfig();
        record.setContent(content);
        record.setActionUrl(actionUrl);
        return singerAuditChatConfigMapper.updateByExample(record, example);
    }
}
