package fm.lizhi.ocean.wavecenter.infrastructure.sign.manager;

import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.*;
import fm.lizhi.ocean.wavecenter.common.dto.PageDto;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.remote.IContractRemote;
import fm.lizhi.ocean.wavecenter.service.sign.manager.RoomManager;
import fm.lizhi.ocean.wavecenter.service.user.dto.SimpleUserDto;
import fm.lizhi.ocean.wavecenter.service.user.manager.UserManager;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/4/10 14:43
 */
@Component
public class RoomManagerImpl implements RoomManager {

    @Autowired
    private IContractRemote iContractRemote;

    @Autowired
    private UserManager userManager;

    @Override
    public PageBean<RoomSignBean> getAllSingGuildRooms(long familyId, int pageNo, int pageSize) {
        PageBean<RoomSignBean> allGuildRooms = iContractRemote.getAllSingGuildRooms(familyId, pageNo, pageSize);

        //查询厅主名称
        List<RoomSignBean> list = allGuildRooms.getList();
        List<Long> njIds = list.stream().map(RoomBean::getId).collect(Collectors.toList());

        List<SimpleUserDto> njList = userManager.getSimpleUserByIds(njIds);
        Map<Long, SimpleUserDto> njMap = njList.stream().collect(Collectors.toMap(SimpleUserDto::getId, v -> v, (k1, k2) -> k2));
        for (RoomSignBean roomSignBean : list) {
            SimpleUserDto simpleUserDto = njMap.get(roomSignBean.getId());
            if (simpleUserDto == null) {
                continue;
            }
            roomSignBean.setName(simpleUserDto.getName());
            roomSignBean.setBand(simpleUserDto.getBand());
            roomSignBean.setPhoto(simpleUserDto.getAvatar());
        }

        return allGuildRooms;
    }

    @Override
    public PageBean<RoomSignBean> getAllGuildRooms(long familyId, List<Long> roomIds, int pageNo, int pageSize) {
        PageBean<RoomSignBean> allGuildRooms = iContractRemote.getAllGuildRooms(familyId, roomIds, pageNo, pageSize);

        //查询厅主名称
        List<RoomSignBean> list = allGuildRooms.getList();
        Set<Long> njIds = list.stream().map(RoomBean::getId).collect(Collectors.toSet());

        List<SimpleUserDto> njList = userManager.getSimpleUserByIds(new ArrayList<>(njIds));
        Map<Long, SimpleUserDto> njMap = njList.stream().collect(Collectors.toMap(SimpleUserDto::getId, v -> v, (k1, k2) -> k2));
        for (RoomSignBean roomSignBean : list) {
            SimpleUserDto simpleUserDto = njMap.get(roomSignBean.getId());
            if (simpleUserDto == null) {
                continue;
            }
            roomSignBean.setName(simpleUserDto.getName());
            roomSignBean.setBand(simpleUserDto.getBand());
            roomSignBean.setPhoto(simpleUserDto.getAvatar());
        }

        return allGuildRooms;
    }

    @Override
    public PageBean<PlayerSignBean> getAllRoomPlayers(long roomId, int pageNo, int pageSize) {
        PageBean<PlayerSignBean> allRoomPlayers = iContractRemote.getAllRoomPlayers(roomId, pageNo, pageSize);

        //查询主播名称
        List<PlayerSignBean> list = allRoomPlayers.getList();
        Set<Long> userIds = list.stream().map(PlayerBean::getId).collect(Collectors.toSet());

        List<SimpleUserDto> njList = userManager.getSimpleUserByIds(new ArrayList<>(userIds));
        Map<Long, SimpleUserDto> userMap = njList.stream().collect(Collectors.toMap(SimpleUserDto::getId, v -> v, (k1, k2) -> k2));
        for (PlayerSignBean playerSignBean : list) {
            SimpleUserDto simpleUserDto = userMap.get(playerSignBean.getId());
            if (simpleUserDto == null) {
                continue;
            }
            playerSignBean.setName(simpleUserDto.getName());
            playerSignBean.setBand(simpleUserDto.getBand());
            playerSignBean.setPhoto(simpleUserDto.getAvatar());
        }
        return allRoomPlayers;
    }

    @Override
    public List<Long> getAllSignRoomPlayerIds(long roomId) {
        return iContractRemote.getAllSignRoomPlayerIds(roomId);
    }

    @Override
    public PageDto<PlayerSignBean> getAllGuildPlayer(QueryGuildPlayerBean req, int pageNo, int pageSize) {
        PageDto<PlayerSignBean> allGuildPlayer = iContractRemote.getAllGuildPlayer(req.getFamilyId(), req.getRoomIds(), req.getStatus(), pageNo, pageSize);

        //查询主播名称
        List<PlayerSignBean> list = allGuildPlayer.getList();
        Set<Long> userIds = list.stream().map(PlayerBean::getId).collect(Collectors.toSet());

        List<SimpleUserDto> njList = userManager.getSimpleUserByIds(new ArrayList<>(userIds));
        Map<Long, SimpleUserDto> userMap = njList.stream().collect(Collectors.toMap(SimpleUserDto::getId, v -> v, (k1, k2) -> k2));
        for (PlayerSignBean playerSignBean : list) {
            SimpleUserDto simpleUserDto = userMap.get(playerSignBean.getId());
            if (simpleUserDto == null) {
                continue;
            }
            playerSignBean.setName(simpleUserDto.getName());
            playerSignBean.setBand(simpleUserDto.getBand());
            playerSignBean.setPhoto(simpleUserDto.getAvatar());
        }
        return allGuildPlayer;
    }

    @Override
    public Optional<PlayerSignBean> getPlayerSignInfo(Long familyId, Long roomId, long userId) {
        Optional<PlayerSignBean> playerSignOp = iContractRemote.getPlayerSign(familyId, roomId, userId);
        if (!playerSignOp.isPresent()) {
            return Optional.empty();
        }
        PlayerSignBean signBean = playerSignOp.get();
        List<SimpleUserDto> simpleUserList = userManager.getSimpleUserByIds(Collections.singletonList(signBean.getId()));
        if (CollectionUtils.isEmpty(simpleUserList)) {
            return Optional.empty();
        }
        SimpleUserDto simpleUserDto = simpleUserList.get(0);
        signBean.setName(simpleUserDto.getName());
        signBean.setBand(simpleUserDto.getBand());
        signBean.setPhoto(simpleUserDto.getAvatar());
        return Optional.of(signBean);
    }

    @Override
    public List<Long> getFamilyAllNjId(Long familyId) {
        return iContractRemote.getFamilyAllNjId(familyId);
    }
}
