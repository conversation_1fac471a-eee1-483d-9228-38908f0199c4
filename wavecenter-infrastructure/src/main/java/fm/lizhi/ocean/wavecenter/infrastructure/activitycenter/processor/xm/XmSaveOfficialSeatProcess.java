package fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.processor.xm;

import java.util.List;

import com.alibaba.fastjson.JSONObject;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.DeleteOfficialSeatParamDTO;
import org.apache.commons.collections4.CollectionUtils;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.processor.ISaveOfficialSeatProcess;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.SaveOfficialSeatParamDTO;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.XmOfficialSeatDetailDTO;

@Component
public class XmSaveOfficialSeatProcess implements ISaveOfficialSeatProcess<XmOfficialSeatDetailDTO> {

    @Override
    public XmOfficialSeatDetailDTO buildSaveDTO(SaveOfficialSeatParamDTO param, XmOfficialSeatDetailDTO sameData) {
        if (sameData != null) {
            List<Long> userIds = sameData.getUserIds();
            userIds.addAll(param.getUserIds());
            return sameData;
        } else {
            XmOfficialSeatDetailDTO detailDto = new XmOfficialSeatDetailDTO();
            detailDto.setPosition(param.getPosition());
            detailDto.setUserIds(param.getUserIds());
            detailDto.setOperator("creator");
            detailDto.setStartTime(param.getStartTime());
            detailDto.setEndTime(param.getEndTime());
            detailDto.setBackgroundUrl(param.getBackgroundUrl());
            detailDto.setTabId(param.getTabId());
            detailDto.setType(param.getExtra().getActivityType());
            detailDto.setRemark(param.getExtra().getNote());
            return detailDto;
        }
    }

    @Override
    public XmOfficialSeatDetailDTO buildDeleteDTO(DeleteOfficialSeatParamDTO param, XmOfficialSeatDetailDTO sameData) {
        return null;
    }

    @Override
    public Pair<Boolean, String> checkDataValid(SaveOfficialSeatParamDTO param, XmOfficialSeatDetailDTO sameData) {
        if (sameData == null) {
            return Pair.of(Boolean.TRUE, "");
        }
  
        if (CollectionUtils.isNotEmpty(sameData.getUserGroupId())) {
            String msg = String.format("相同时间相同官频位已存在配置，需要在用户组进行添加，用户组ID：%s", JSONObject.toJSONString(sameData.getUserGroupId()));
            return Pair.of(Boolean.FALSE, msg);
        }
        return Pair.of(Boolean.TRUE, "");
    }

    @Override
    public boolean isDirectDeleteSeatRecord(DeleteOfficialSeatParamDTO param, XmOfficialSeatDetailDTO seatData) {
        return true;
    }

    @Override
    public BusinessEvnEnum getBusinessEnv() {
        return BusinessEvnEnum.XIMI;
    }
}
