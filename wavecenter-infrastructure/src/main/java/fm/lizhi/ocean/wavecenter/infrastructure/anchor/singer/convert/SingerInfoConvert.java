package fm.lizhi.ocean.wavecenter.infrastructure.anchor.singer.convert;

import com.lizhi.commons.config.core.util.ConfigUtils;

import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerRecordOperateTypeEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerStatusEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerTypeEnum;
import fm.lizhi.ocean.wavecenter.common.convert.CommonConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.anchor.singer.datastore.entity.SingerInfo;
import fm.lizhi.ocean.wavecenter.infrastructure.anchor.singer.datastore.entity.SingerVerifyRecord;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.SaveSingerInfoParamDTO;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.SingerInfoDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.ERROR, uses = {CommonConvert.class},
        imports = {ConfigUtils.class, Date.class, SingerTypeEnum.class})
public interface SingerInfoConvert {

    SingerInfoConvert I = Mappers.getMapper(SingerInfoConvert.class);


    @Mapping(target = "rewardsIssued", constant = "false")
    @Mapping(target = "modifyTime", expression = "java(new Date())")
    @Mapping(target = "deployEnv", expression = "java(ConfigUtils.getEnvRequired().name())")
    @Mapping(target = "createTime", expression = "java(new Date())")
    @Mapping(target = "auditTime", expression = "java(param.getAuditTime() != null? param.getAuditTime() : new Date())")
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "eliminationTime", ignore = true)
    @Mapping(target = "eliminationReason", ignore = true)
    SingerInfo buildSingerInfo(SaveSingerInfoParamDTO param);

    List<SingerInfoDTO> convertSingerInfoDTOList(List<SingerInfo> singerInfoList);

    List<SingerInfo> convertSingerInfoList(List<SingerInfoDTO> singerInfoList);

    SingerInfoDTO convertSingerInfoDTO(SingerInfo singerInfo);

    @Mapping(target = "modifyTime", ignore = true)
    @Mapping(target = "deployEnv", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    SingerInfo convertSingerInfo(SingerInfoDTO singerInfoDTO);

    default SingerRecordOperateTypeEnum singerStatusConvertOperateType(Integer singerStatus) {
        if (singerStatus == SingerStatusEnum.EFFECTIVE.getStatus()) {
            return SingerRecordOperateTypeEnum.PASS;
        }
        if (singerStatus == SingerStatusEnum.ELIMINATED.getStatus()) {
            return SingerRecordOperateTypeEnum.ELIMINATE;
        }
        return null;
    }

    default SingerInfo buildSingerInfo(SingerVerifyRecord singerVerifyRecord, SingerInfoDTO singerInfoDTO, Integer singerStatus, Integer passSingerType, String operator) {
        SingerInfo singerInfo = new SingerInfo();
        if (singerInfoDTO != null) {
            singerInfo = convertSingerInfo(singerInfoDTO);
            singerInfo.setSingerVerifyId(singerVerifyRecord.getId());
            singerInfo.setSingerStatus(singerStatus);
            singerInfo.setSongStyle(singerVerifyRecord.getSongStyle());
            singerInfo.setOriginalSinger(singerVerifyRecord.getOriginalSinger());
            singerInfo.setSingerVerifyId(singerVerifyRecord.getId());
            singerInfo.setAuditTime(singerStatus == SingerStatusEnum.EFFECTIVE.getStatus() ? new Date() : null);
            singerInfo.setEliminationTime(singerStatus != SingerStatusEnum.ELIMINATED.getStatus() ? null : new Date());
            singerInfo.setEliminationReason(singerStatus != SingerStatusEnum.ELIMINATED.getStatus() ? "" : singerInfoDTO.getEliminationReason());
            singerInfo.setOperator(operator);
            return singerInfo;
        }

        singerInfo.setSingerVerifyId(singerVerifyRecord.getId());
        singerInfo.setAppId(singerVerifyRecord.getAppId());
        singerInfo.setUserId(singerVerifyRecord.getUserId());
        singerInfo.setSingerStatus(singerStatus);
        singerInfo.setSongStyle(singerVerifyRecord.getSongStyle());
        singerInfo.setOriginalSinger(singerVerifyRecord.getOriginalSinger());
        singerInfo.setRewardsIssued(false);
        singerInfo.setSingerType(passSingerType);
        singerInfo.setAuditTime(singerStatus == SingerStatusEnum.EFFECTIVE.getStatus() ? new Date() : null);
        singerInfo.setEliminationTime(singerStatus != SingerStatusEnum.ELIMINATED.getStatus() ? null : new Date());
        singerInfo.setEliminationReason("");
        singerInfo.setOperator(operator);
        singerInfo.setCreateTime(singerVerifyRecord.getCreateTime());
        singerInfo.setModifyTime(singerVerifyRecord.getModifyTime());
        singerInfo.setNjId(singerVerifyRecord.getNjId());
        singerInfo.setFamilyId(singerVerifyRecord.getFamilyId());
        singerInfo.setDeployEnv(ConfigUtils.getEnvRequired().name());
        return singerInfo;
    }

}
