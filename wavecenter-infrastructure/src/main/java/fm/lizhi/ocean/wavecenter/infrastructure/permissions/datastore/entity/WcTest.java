package fm.lizhi.ocean.wavecenter.infrastructure.permissions.datastore.entity;

import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 测试
 *
 * @date 2024-03-25 05:41:11
 */
@Table(name = "`wavecenter_test`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WcTest {
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    @Column(name= "`test_name`")
    private String testName;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", testName=").append(testName);
        sb.append("]");
        return sb.toString();
    }
}