package fm.lizhi.ocean.wavecenter.infrastructure.common.convert;

import fm.lizhi.common.romefs.javasdk.config.RomeFsConfig;
import fm.lizhi.ocean.wavecenter.common.config.HyBizCommonConfig;
import fm.lizhi.ocean.wavecenter.common.config.PpBizCommonConfig;
import fm.lizhi.ocean.wavecenter.common.config.RomeConfig;
import fm.lizhi.ocean.wavecenter.common.config.XmBizCommonConfig;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @date 2024/4/20 14:51
 */
@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface FileManagerConvert {

    FileManagerConvert I = Mappers.getMapper(FileManagerConvert.class);

    RomeFsConfig convertRomeFsConfig(RomeConfig romeConfig);

//    RomeFsConfig convertRomeFsConfig(PpBizCommonConfig.RomeConfig config);
//
//    RomeFsConfig convertRomeFsConfig(XmBizCommonConfig.RomeConfig config);
//
//    RomeFsConfig convertRomeFsConfig(HyBizCommonConfig.RomeConfig config);
}
