package fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.mapper;

import fm.lizhi.common.datastore.core.annotation.DataStore;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.po.IncomeStatPo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@DataStore(namespace = "mysql_heiye_ppdata_r")
public interface HyFamilyFlowStateMapper {

    @Select({
            "<script>"
            , "select s.user_id, s.nj_id, sum(s.flow) income"
            , "from family_flow_stat s"
            , "where s.family_id = #{familyId}"
            , "and s.stat_day=#{date}"
            , "group by s.user_id, s.nj_id"
            , "order by income ${orderType}"
            , "limit 10"
            ,"</script>"
    })
    List<IncomeStatPo> guildPlayerIncomeList(@Param("familyId") long familyId, @Param("date") String date, @Param("orderType") String orderType);

    @Select({
            "<script>"
            , "select s.user_id, s.nj_id, sum(s.flow) income"
            , "from family_flow_stat s"
            , "where s.nj_id = #{njId}"
            , "and s.stat_day=#{date}"
            , "and s.family_id = #{familyId}"
            , "group by s.user_id, s.nj_id"
            , "order by income ${orderType}"
            , "limit 10"
            ,"</script>"
    })
    List<IncomeStatPo> roomPlayerIncomeList(@Param("njId") long njId, @Param("familyId") long familyId, @Param("date") String date, @Param("orderType") String orderType);
}
