package fm.lizhi.ocean.wavecenter.infrastructure.common.convert;

import fm.lizhi.content.review.protocol.CheckServiceProto;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.service.common.dto.ActivityApplyDataAuditParamDTO;
import fm.lizhi.ocean.wavecenter.service.common.dto.ActivityApplyDataAuditResultDTO;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
public class AuditContentConvert {

    /**
     * 构建活动申请送审数据
     *
     * @param paramDTO 参数类型
     * @return 结果
     */
    public CheckServiceProto.RequestCheckMultiData buildAuditActivityApplyDataParam(ActivityApplyDataAuditParamDTO paramDTO) {
        List<CheckServiceProto.MultiContent> list = new ArrayList<>();
        List<ActivityApplyDataAuditParamDTO.CheckDataInfo> checkDataList = paramDTO.getCheckDataList();
        for (ActivityApplyDataAuditParamDTO.CheckDataInfo checkDataInfo : checkDataList) {
            CheckServiceProto.MultiContent multiContent = CheckServiceProto.MultiContent.newBuilder()
                    .setInnerType(checkDataInfo.getInnerType())
                    .setContentId(String.valueOf(checkDataInfo.getContentId()))
                    .setContent(checkDataInfo.getContent())
                    .build();
            list.add(multiContent);
        }

        return CheckServiceProto.RequestCheckMultiData.newBuilder()
                .setAppName(BusinessEvnEnum.from(paramDTO.getAppId()).getName())
                .setCountry("CN")
                .setLanguage("zh")
                .setFromUserId(paramDTO.getApplicantUid())
                .setType(paramDTO.getParentInnerType())
                .addAllMultiContents(list)
                .build();
    }

    /**
     * 转换申请数据审核结果
     *
     * @param targetList 目标数据
     * @return 结果
     */
    public List<ActivityApplyDataAuditResultDTO.DiscernResult> convertApplyDataAuditResult(List<CheckServiceProto.DiscernResult> targetList) {
        List<ActivityApplyDataAuditResultDTO.DiscernResult> res = new ArrayList<>();
        for (CheckServiceProto.DiscernResult discernResult : targetList) {
            ActivityApplyDataAuditResultDTO.DiscernResult details = new ActivityApplyDataAuditResultDTO.DiscernResult()
                    .setContentId(Long.parseLong(discernResult.getContentId()))
                    .setRiskType(discernResult.getRiskTypes())
                    .setHitDetails(discernResult.getHitDetails());
            res.add(details);
        }
        return res;
    }
}
