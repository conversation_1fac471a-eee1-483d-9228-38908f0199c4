package fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.manager;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityResourceDeployTypeConstants;
import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.datastore.dao.ActivityApplyDecorateDao;
import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.datastore.entity.*;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.google.common.collect.Lists;
import com.lizhi.commons.config.core.util.ConfigUtils;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityFlowResourceAuditBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityResourceGiveStatusEnum;
import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityResourceTypeEnum;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.convert.ActivityMaterielConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.convert.ActivityMaterielGiveConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.datastore.dao.ActivityApplyDao;
import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.datastore.dao.ActivityMaterielDao;
import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.datastore.dao.ActivityRoomAnnouncementDeployDao;
import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.datastore.mapper.ActivityApplyFlowResourceMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.datastore.mapper.ActivityDressUpGiveRecordMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.datastore.mapper.ActivityFlowResourceGiveRecordMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.datastore.mapper.ActivityResourceGiveRecordMapper;
import fm.lizhi.ocean.wavecenter.service.activitycenter.manager.ActivityMaterielGiveManager;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.manager.ActivityResourceManager;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.model.dto.ActivityResourceSimpleInfoDTO;
import fm.lizhi.ocean.wavecenter.service.common.manager.IdManager;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class ActivityMaterielGiveManagerImpl implements ActivityMaterielGiveManager {

    private static final int RESOURCE_CONFIG_ENABLE_STATUS = 1;

    @Autowired
    private ActivityApplyDao activityApplyDao;

    @Autowired
    private ActivityApplyFlowResourceMapper activityApplyFlowResourceMapper;

    @Autowired
    private ActivityResourceManager activityResourceManager;

    @Autowired
    private ActivityMaterielDao activityMaterielDao;

    @Autowired
    private ActivityMaterielConvert activityMaterielConvert;

    @Autowired
    private ActivityResourceGiveRecordMapper activityResourceGiveRecordMapper;

    @Autowired
    private ActivityDressUpGiveRecordMapper activityDressUpGiveRecordMapper;

    @Autowired
    private ActivityFlowResourceGiveRecordMapper activityFlowResourceGiveRecordMapper;

    @Autowired
    private ActivityRoomAnnouncementDeployDao activityRoomAnnouncementDeployDao;

    @Autowired
    private IdManager idManager;

    @Autowired
    private ActivityApplyDecorateDao activityApplyDecorateDao;

    @Override
    public Result<Void> initActivityMateriel(long activityId, List<ActivityFlowResourceAuditBean> resourceAuditList) {
        try {
            //根据活动ID查询活动详情
            ActivityApplyInfo applyInfo = activityApplyDao.getActivityInfoById(activityId);
            if (applyInfo == null) {
                log.error("activity not exist, activityId:{}", activityId);
                return RpcResult.fail(INIT_ACTIVITY_MATERIEL_FAIL, "活动不存在，请刷新重试");
            }

            if (activityMaterielDao.isActivityMaterielExist(activityId)) {
                log.warn("activity materiel already exist, activityId:{}", activityId);
                //重复初始化正常往下走，不异常
                return RpcResult.success();
            }

            // 查询活动装扮资源
            List<ActivityApplyDecorate> decorates = activityApplyDecorateDao.getDecorates(activityId);

            ActivityApplyFlowResourceExample example = new ActivityApplyFlowResourceExample();
            example.createCriteria().andActivityIdEqualTo(activityId);
            List<ActivityApplyFlowResource> resourceList = activityApplyFlowResourceMapper.selectByExample(example);

            List<ActivityResourceSimpleInfoDTO> resourceConfigs = Collections.emptyList();
            if (CollectionUtils.isNotEmpty(resourceList)) {
                List<Long> resourceIds = resourceList.stream().map(ActivityApplyFlowResource::getResourceConfigId).collect(Collectors.toList());
                resourceConfigs = activityResourceManager.batchValidateResourceByIdsAndStatus(resourceIds, RESOURCE_CONFIG_ENABLE_STATUS);
            }

            //返回构建的流量资源实体
            Pair<List<ActivityResourceGiveRecord>, List<ActivityFlowResourceGiveRecord>> flowResources =
                    activityMaterielConvert.buildFlowResources(applyInfo, resourceList, resourceConfigs, resourceAuditList);
            //构建装扮资源实体
            Pair<List<ActivityResourceGiveRecord>, List<ActivityDressUpGiveRecord>> dressUpResources = activityMaterielConvert.buildDressUpResources(applyInfo, decorates);
            //构建活动公告实体
            ActivityAnnouncementDeployRecord roomAnnouncement = activityMaterielConvert.buildRoomAnnouncement(applyInfo);

            //事务保存初始化到数据库
            List<ActivityResourceGiveRecord> resourceGiveRecords = flowResources.getLeft();
            resourceGiveRecords.addAll(dressUpResources.getLeft());
            activityMaterielDao.initActivityMateriel(resourceGiveRecords, flowResources.getRight(), dressUpResources.getRight(), roomAnnouncement);
            return RpcResult.success();
        } catch (Exception e) {
            log.error("initActivityMateriel happen error, activityId:{}", activityId, e);
            return RpcResult.fail(INIT_ACTIVITY_MATERIEL_FAIL, e.getMessage());
        }
    }

    @Override
    public List<ActivityResourceGiveDTO> getWaitGiveResourceList(int appId, long minStartTime, int maxTryCount) {
        ActivityResourceGiveRecordExample example = new ActivityResourceGiveRecordExample();
        example.createCriteria()
                .andStartTimeGreaterThanOrEqualTo(new Date(minStartTime))
                .andTryCountLessThan(maxTryCount)
                .andAppIdEqualTo(appId)
                .andStatusIn(Lists.newArrayList(ActivityResourceGiveStatusEnum.WAIT_GIVE.getStatus(), ActivityResourceGiveStatusEnum.GIVE_FAIL.getStatus()))
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name());

        List<ActivityResourceGiveRecord> list = activityResourceGiveRecordMapper.selectByExample(example);
        return ActivityMaterielGiveConvert.I.convertResourceBeans2DTOs(list);
    }

    @Override
    public List<ActivityResourceGiveDTO> getGiveResourceListByActivityId(int appId, long activityId, List<Integer> statusList) {
        ActivityResourceGiveRecordExample example = new ActivityResourceGiveRecordExample();
        example.createCriteria()
                .andAppIdEqualTo(appId)
                .andActivityIdEqualTo(activityId)
                .andStatusIn(statusList)
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name());

        List<ActivityResourceGiveRecord> list = activityResourceGiveRecordMapper.selectByExample(example);
        return ActivityMaterielGiveConvert.I.convertResourceBeans2DTOs(list);
    }

    @Override
    public List<ActivityDressUpGiveDTO> getWaitGiveDressUpList(long giveId) {
        ActivityDressUpGiveRecordExample example = new ActivityDressUpGiveRecordExample();
        example.setOrderByClause("dress_up_id desc");
        example.createCriteria()
                .andStatusEqualTo(ActivityResourceGiveStatusEnum.WAIT_GIVE.getStatus())
                .andGiveIdEqualTo(giveId);
        List<ActivityDressUpGiveRecord> list = activityDressUpGiveRecordMapper.selectByExample(example);
        return ActivityMaterielGiveConvert.I.convertDressUpBean2DTO(list);
    }

    @Override
    public List<ActivityFlowResourceGiveDTO> getWaitGiveFlowResouceList(long giveId) {
        ActivityFlowResourceGiveRecordExample example = new ActivityFlowResourceGiveRecordExample();
        example.createCriteria()
                .andDeployTypeEqualTo(ActivityResourceDeployTypeConstants.AUTO_CONFIG)
                .andStatusEqualTo(ActivityResourceGiveStatusEnum.WAIT_GIVE.getStatus())
                .andGiveIdEqualTo(giveId);
        List<ActivityFlowResourceGiveRecord> list = activityFlowResourceGiveRecordMapper.selectByExample(example);
        return ActivityMaterielGiveConvert.I.convertFlowResourceBean2DTO(list);
    }

    /**
     * 修改装扮领取记录状态
     *
     * @param id           主键ID
     * @param targetStatus 状态
     * @return 结果
     */
    @Override
    public boolean updateDressUpRecordStatus(long id, int originalStatus, int targetStatus) {
        ActivityDressUpGiveRecord entity = new ActivityDressUpGiveRecord();
        entity.setId(id);
        entity.setStatus(targetStatus);
        entity.setModifyTime(new Date());

        ActivityDressUpGiveRecordExample example = new ActivityDressUpGiveRecordExample();
        example.createCriteria()
                .andStatusEqualTo(originalStatus)
                .andIdEqualTo(id);
        return activityDressUpGiveRecordMapper.updateByExample(entity, example) > 0;
    }

    @Override
    public boolean updateResourceGiveRecord(UpdateResourceGiveParamDTO param) {
        UpdateResourceRecordInfoDTO resourceGiveRecord = new UpdateResourceRecordInfoDTO()
                .setId(param.getId())
                .setStatus(param.getTargetStatus())
                .setTryCount(param.getTryCount())
                .setErrorCode(param.getErrorCode())
                .setErrorMsg(param.getErrorMsg())
                .setOriginalStatus(param.getOriginalStatus());

        List<UpdateFlowResourceStatusDTO> flowResources = new ArrayList<>();
        List<UpdateDressUpStatusDTO> dressUpResources = new ArrayList<>();
        if (param.getResourceType() == ActivityResourceTypeEnum.FLOW_RESOURCE.getType()) {
            if (CollectionUtils.isNotEmpty(param.getDetailParams())) {
                for (UpdateResourceGiveParamDTO.ResourceDetailGiveParam flowResource : param.getDetailParams()) {
                    UpdateFlowResourceStatusDTO giveRecord = new UpdateFlowResourceStatusDTO()
                            .setId(flowResource.getId())
                            .setStatus(flowResource.getTargetStatus())
                            .setOriginalStatus(flowResource.getOriginalStatus())
                            .setBizRecordId(flowResource.getBizRecordId());
                    flowResources.add(giveRecord);
                }
            }
        }

        if (param.getResourceType() == ActivityResourceTypeEnum.DRESS_UP.getType()) {
            if (CollectionUtils.isNotEmpty(param.getDetailParams())) {
                for (UpdateResourceGiveParamDTO.ResourceDetailGiveParam flowResource : param.getDetailParams()) {
                    UpdateDressUpStatusDTO giveRecord = new UpdateDressUpStatusDTO()
                            .setId(flowResource.getId())
                            .setStatus(flowResource.getTargetStatus())
                            .setOriginalStatus(flowResource.getOriginalStatus());
                    dressUpResources.add(giveRecord);
                }
            }
        }
        try {
            activityMaterielDao.updateResourceGive(resourceGiveRecord, flowResources, dressUpResources);
            return true;
        } catch (Exception e) {
            log.error("updateResourceGiveRecord happen error, giveId:{}", param.getId(), e);
            return false;
        }
    }

    @Override
    public List<ActivityRoomAnnouncementDeployDTO> getAnnouncementDeployList(Long time, List<Integer> statusList, int activityStatus) {
        List<ActivityAnnouncementDeployRecord> announcementDeployList = activityRoomAnnouncementDeployDao.getAnnouncementDeployList(time, statusList, activityStatus);
        return ActivityMaterielGiveConvert.I.convertAnnouncementDeployBean2DTO(announcementDeployList);
    }

    @Override
    public boolean updateAnnouncementDeployStatus(long id, int originalStatus, int targetStatus) {
        return activityRoomAnnouncementDeployDao.updateAnnouncementDeployStatus(id, originalStatus, targetStatus);
    }

    @Override
    public boolean saveOriginalAnnouncement(SaveOriginalAnnouncementParamDTO param) {
        return activityRoomAnnouncementDeployDao.saveOriginalAnnouncement(param.getId(), param.getContent(), param.getImageUrl());
    }

    @Override
    public ActivityResourceGiveDTO getGiveResource(int appId, Long resourceId) {

        ActivityResourceGiveRecord param = ActivityResourceGiveRecord.builder()
                .resourceId(resourceId)
                .appId(appId)
                .deployEnv(ConfigUtils.getEnvRequired().name())
                .build();
        ;
        return ActivityMaterielGiveConvert.I.convertResourceBean2DTO(activityResourceGiveRecordMapper.selectOne(param));
    }

}
