package fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.remote.xm;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.live.room.xm.dto.req.SendUserRecommendationCardReq;
import fm.lizhi.live.room.xm.dto.resp.SendUserRecommendationCardResp;
import fm.lizhi.live.room.xm.enums.RecommendationCardRewardType;
import fm.lizhi.live.room.xm.services.RecommendationCardSpringService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.remote.IRecommendCardServiceRemote;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.SendRecommendCardParamDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class XmRecommendCardServiceRemote implements IRecommendCardServiceRemote {

    @Autowired
    private RecommendationCardSpringService recommendationCardManageService;

    @Override
    public Result<Void> sendRecommendCard(SendRecommendCardParamDTO param) {
        SendUserRecommendationCardReq cardReq = new SendUserRecommendationCardReq();
        cardReq.setReason(param.getReason());
        cardReq.setUserId(param.getUserId());
        cardReq.setNums(param.getCount());
        cardReq.setValid(param.getValid());
        cardReq.setOperator("creator");
        cardReq.setRewardType(RecommendationCardRewardType.WAVE_ACTIVITY.getStatus());

        Result<SendUserRecommendationCardResp> result = recommendationCardManageService.sendUserRecommendationCard(cardReq);
        if (RpcResult.isFail(result)) {
            log.warn("xm.sendRecommendCard fail. rCode={}, userId={}, count={}", result.rCode(), param.getUserId(), param.getCount());
            return RpcResult.fail(SEND_RECOMMEND_CARD_FAIL);
        }
        return RpcResult.success();
    }

    @Override
    public BusinessEvnEnum getBusinessEvnEnum() {
        return BusinessEvnEnum.XIMI;
    }
}
