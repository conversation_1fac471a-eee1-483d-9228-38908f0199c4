package fm.lizhi.ocean.wavecenter.infrastructure.sign.remote.xm;

import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.sign.bean.FamilyAndNjContractBean;
import fm.lizhi.ocean.wavecenter.api.sign.constant.ContractTypeEnum;
import fm.lizhi.ocean.wavecenter.api.sign.constant.SignRelationEnum;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.remote.IContractRemote;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.remote.ISettleRemote;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.remote.request.RequestContractSettle;
import fm.lizhi.ocean.wavecenter.service.sign.dto.RequestFamilyAndNjContractDTO;
import fm.lizhi.ocean.wavecenter.service.sign.dto.SignSettleDTO;
import fm.lizhi.xm.family.api.SettleRatioService;
import fm.lizhi.xm.family.protocol.SettleRatioServiceProto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/10/15 19:21
 */
@Slf4j
@Component
public class XmSettleRemote implements ISettleRemote {

    @Autowired
    private SettleRatioService settleRatioService;
    @Autowired
    private IContractRemote iContractRemote;

    @Override
    public Optional<SignSettleDTO> querySettleByNj(Long njId) {
        //查询厅当前生效的合同
        PageBean<FamilyAndNjContractBean> contractList = iContractRemote.queryContract(RequestFamilyAndNjContractDTO.builder()
                .njId(njId)
                .type(ContractTypeEnum.SIGN)
                .type(ContractTypeEnum.SUBJECT_CHANGE)
                .type(ContractTypeEnum.RENEW)
                .relation(SignRelationEnum.SIGN_SUCCESS)
                .pageNo(1).pageSize(1)
                .build());
        if (CollectionUtils.isEmpty(contractList.getList())) {
            LogContext.addResLog("queryContract is empty. njId={}", njId);
            return Optional.empty();
        }

        FamilyAndNjContractBean contract = contractList.getList().get(0);
        SettleRatioServiceProto.GetBatchCurrentSettleRatioListParam param = SettleRatioServiceProto.GetBatchCurrentSettleRatioListParam.newBuilder()
                .setFamilyId(contract.getFamilyId())
                .addNjIds(njId)
                .build();
        Result<SettleRatioServiceProto.ResponseGetBatchCurrentSettleRatioList> rpcResult = settleRatioService.getBatchCurrentSettleRatioList(param);
        if (RpcResult.isFail(rpcResult)) {
            log.error("querySettleByNj getBatchCurrentSettleRatioList fail. rCode={},njId={}", rpcResult.rCode(), njId);
            return Optional.empty();
        }

        SettleRatioServiceProto.CurrentSettleRatio settle = rpcResult.target().getCurrentSettleRatio(0);
        return Optional.of(new SignSettleDTO().setSettleType("PUBLIC").setSettlePercentage(settle.getPercentage()));
    }

    @Override
    public Map<Long, SignSettleDTO> querySettle(RequestContractSettle request) {
        List<FamilyAndNjContractBean> contracts = request.getContracts();
        if (CollectionUtils.isEmpty(contracts)) {
            LogContext.addResLog("contract is empty");
            return Collections.emptyMap();
        }

        //根据家族分组
        Map<Long, List<FamilyAndNjContractBean>> familyMap = contracts.stream().collect(Collectors.groupingBy(FamilyAndNjContractBean::getFamilyId));
        Map<Long, SignSettleDTO> result = new HashMap<>();
        for (Map.Entry<Long, List<FamilyAndNjContractBean>> entry : familyMap.entrySet()) {
            Long familyId = entry.getKey();
            List<Long> njIds = entry.getValue().stream().map(FamilyAndNjContractBean::getNjUserId).collect(Collectors.toList());
            SettleRatioServiceProto.GetBatchCurrentSettleRatioListParam param = SettleRatioServiceProto.GetBatchCurrentSettleRatioListParam.newBuilder()
                    .setFamilyId(familyId)
                    .addAllNjIds(njIds)
                    .build();
            Result<SettleRatioServiceProto.ResponseGetBatchCurrentSettleRatioList> rpcResult = settleRatioService.getBatchCurrentSettleRatioList(param);
            if (RpcResult.isFail(rpcResult)) {
                log.error("getBatchCurrentSettleRatioList fail. familyId={},njIds={},rCode={}"
                        , familyId, JsonUtil.dumps(njIds), rpcResult.rCode());
                continue;
            }
            List<SettleRatioServiceProto.CurrentSettleRatio> ratioList = rpcResult.target().getCurrentSettleRatioList();
            for (SettleRatioServiceProto.CurrentSettleRatio ratio : ratioList) {
                result.put(ratio.getNjId(), new SignSettleDTO().setSettlePercentage(ratio.getPercentage()));
            }
        }

        Map<Long, SignSettleDTO> contractMap = new HashMap<>();
        for (FamilyAndNjContractBean contract : contracts) {
            Long njId = contract.getNjUserId();
            SignSettleDTO settleDTO = result.get(njId);
            if (settleDTO != null) {
                contractMap.put(contract.getContractId(), settleDTO);
            }
        }

        return contractMap;
    }

    @Override
    public BusinessEvnEnum getBusinessEvnEnum() {
        return BusinessEvnEnum.XIMI;
    }
}
