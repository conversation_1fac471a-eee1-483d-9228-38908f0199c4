package fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 支付configCode结算规则
 *
 * @date 2025-04-21 06:12:39
 */
@Table(name = "`wavecenter_pay_config_code`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WcPayConfigCode {
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 业务 ID
     */
    @Column(name= "`app_id`")
    private Integer appId;

    /**
     * 支付的租户CODE
     */
    @Column(name= "`tenant_code`")
    private String tenantCode;

    /**
     * 场景configCode
     */
    @Column(name= "`config_code`")
    private String configCode;

    /**
     * 账户编码
     */
    @Column(name= "`account_code`")
    private String accountCode;

    /**
     * 账户主体类型：room=厅,player=主播
     */
    @Column(name= "`account_subject_type`")
    private String accountSubjectType;

    /**
     * 交易引擎账户编码
     */
    @Column(name= "`account_engine_code`")
    private String accountEngineCode;

    /**
     * bizId
     */
    @Column(name= "`biz_id`")
    private Integer bizId;

    /**
     * 服务部署环境, TEST/PRE/PRO
     */
    @Column(name= "`deploy_env`")
    private String deployEnv;

    /**
     * 创建时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name= "`update_time`")
    private Date updateTime;

    /**
     * 0=未删除，1=已删除
     */
    @Column(name= "`deleted`")
    private Integer deleted;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", appId=").append(appId);
        sb.append(", tenantCode=").append(tenantCode);
        sb.append(", configCode=").append(configCode);
        sb.append(", accountCode=").append(accountCode);
        sb.append(", accountSubjectType=").append(accountSubjectType);
        sb.append(", accountEngineCode=").append(accountEngineCode);
        sb.append(", bizId=").append(bizId);
        sb.append(", deployEnv=").append(deployEnv);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", deleted=").append(deleted);
        sb.append("]");
        return sb.toString();
    }
}