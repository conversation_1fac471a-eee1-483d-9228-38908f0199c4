package fm.lizhi.ocean.wavecenter.infrastructure.sign.datastore.mapper;

import fm.lizhi.common.datastore.core.annotation.DataStore;
import fm.lizhi.common.datastore.mysql.constant.ParamContants;
import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.common.datastore.mysql.mybatis.sqlprovider.BaseDeleteProvider;
import fm.lizhi.common.datastore.mysql.mybatis.sqlprovider.BaseInsertProvider;
import fm.lizhi.common.datastore.mysql.mybatis.sqlprovider.BaseSelectProvider;
import fm.lizhi.common.datastore.mysql.mybatis.sqlprovider.BaseUpdateProvider;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.datastore.entity.PpPlayerSign;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.datastore.entity.PpPlayerSignExample;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.datastore.po.CountSignPlayerPo;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.datastore.po.QueryPlayerSignPo;
import fm.lizhi.ocean.wavecenter.service.sign.dto.SMSignPlayerPageListReqDto;
import org.apache.ibatis.annotations.*;

import java.util.List;
import java.util.Set;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 */
@DataStore(namespace = "mysql_pplive_lzppfamily_r")
public interface PpPlayerSignMapper {

    /**
     * 根据实体对象的字段值查询多条记录，查询条件会跳过NULL值的字段。
     *
     * @param entity  实体对象
     * @return  实体对象列表
     */
    @SelectProvider(type = BaseSelectProvider.class, method="select")
    List<PpPlayerSign> selectMany(PpPlayerSign entity);

    /**
     * 根据实体对象的字段值查询单条记录，查询条件会跳过NULL值的字段。
     *
     * @param entity  实体对象
     * @return  单个实体对象
     */
    @SelectProvider(type = BaseSelectProvider.class, method="select")
    PpPlayerSign selectOne(PpPlayerSign entity);

    /**
     * 根据实体对象中主键（{@link javax.persistence.Id}标注）字段查询单条数据。
     *
     * @param entity  实体对象
     * @return  单个实体对象
     */
    @SelectProvider(type = BaseSelectProvider.class, method="selectByPrimaryKey")
    PpPlayerSign selectByPrimaryKey(PpPlayerSign entity);

    /**
     * 根据实体对象的字段值查询分页记录，查询条件会跳过NULL值的字段。
     *
     * @param entity  实体对象
     * @return  分页数据
     */
    @SelectProvider(type = BaseSelectProvider.class, method="selectPage")
    PageList<PpPlayerSign> selectPage(@Param(ParamContants.ENTITIE) PpPlayerSign entity, @Param(ParamContants.PAGE_NUMBER)int pageNumber, @Param(ParamContants.PAGE_SIZE)int pageSize);

    /**
     * 根据实体对象中主键字段（{@link javax.persistence.Id}标注）删除数据。
     *
     * @param entity  实体对象
     * @return  影响行数
     */
    @DeleteProvider(type = BaseDeleteProvider.class, method="deleteByPrimaryKey")
    int deleteByPrimaryKey(PpPlayerSign entity);

    /**
     * 将实体对象写入数据库，会跳过NULL值的字段。<br/>
     * 如果主键字段（{@link javax.persistence.Id}标注）有设置{@link javax.persistence.GeneratedValue}时，会自动生成主键并设置到实体对象中。
     *
     * @param entity  实体对象
     * @return  影响行数
     */
    @InsertProvider(type = BaseInsertProvider.class, method="insert")
    int insert(PpPlayerSign entity);

    /**
     * 批量将实体对象写入数据库（不会跳过NULL值)。<br/>
     * 如果字段有设置{@link javax.persistence.Id}和{@link javax.persistence.GeneratedValue}时，会自动生成值，并设置到实体类实例中。
     *
     * @param entities  实体对象列表
     * @return  影响行数
     */
    @InsertProvider(type = BaseInsertProvider.class, method="batchInsert")
    int batchInsert(@Param(ParamContants.ENTITIES) List<PpPlayerSign> entities);

    /**
     * 根据实体类中主键（{@link javax.persistence.Id}标注的字段）更新数据，会跳过NULL值的字段。
     *
     * @param entity  实体对象
     * @return  影响行数
     */
    @UpdateProvider(type = BaseUpdateProvider.class, method="updateByPrimaryKey")
    int updateByPrimaryKey(PpPlayerSign entity);
       
    /**
     * 根据example类生成WHERE条件查询总记录条数
     * 
     * @param example
     * @return
     */
    @SelectProvider(type = BaseSelectProvider.class, method="countByExample")
    long countByExample(PpPlayerSignExample example);

    /**
     * 根据example类生成WHERE条件删除记录
     *
     * @param example
     * @return
     */
    @DeleteProvider(type = BaseDeleteProvider.class, method="deleteByExample")
    long deleteByExample(PpPlayerSignExample example);

    /**
     * 根据example类生成WHERE条件查询记录数
     * 
     * @param example
     * @return
     */
    @SelectProvider(type = BaseSelectProvider.class, method="selectByExample")
    List<PpPlayerSign> selectByExample(PpPlayerSignExample example);
    
    /**
     * 根据example类生成WHERE条件查询分页记录数
     *
     * @param example
     * @param pageNumber  页码
     * @param pageSize  每页数据大小
     * @return
     */
    @SelectProvider(type = BaseSelectProvider.class, method="pageByExample")
    PageList<PpPlayerSign> pageByExample(@Param(ParamContants.EXAMPLE) PpPlayerSignExample example, @Param(ParamContants.PAGE_NUMBER)int pageNumber, @Param(ParamContants.PAGE_SIZE)int pageSize);    

    /**
     * 根据example类生成WHERE条件更新记录数，会跳过实体类对象中的NULL值的字段。<br/>
     *
     * @param entity 实体类对象
     * @param example
     * @return
     */
    @UpdateProvider(type = BaseUpdateProvider.class, method="updateByExample")
    int updateByExample(@Param(ParamContants.ENTITIE) PpPlayerSign entity, @Param(ParamContants.EXAMPLE) PpPlayerSignExample example);

    @Select({
            "<script>"
            , "select * from player_sign where nj_id IN "
            , "<foreach collection='roomIds' item='rId' open='(' separator=',' close=')'>"
            , "#{rId}"
            , "</foreach>"
            , "and user_id=#{userId} and status='SIGN_SUCCEED' and type='SIGN' order by create_time desc limit 1"
            ,"</script>"
    })
    PpPlayerSign getLatestSignRecord(@Param("roomIds")List<Long> roomIds,@Param("userId") long userId);

    @Select({
            "<script>"
            , "SELECT *"
            ,"FROM player_sign"
            ,"WHERE type = 'SIGN' and status in ('SIGN_SUCCEED','STOP_CONTRACT') and nj_id IN "
            , "<foreach collection='njIds' item='njId' open='(' separator=',' close=')'>"
            , "#{njId}"
            , "</foreach>"
            ,"AND (nj_id, user_id, create_time) IN ("
            ,"    SELECT nj_id, user_id, MAX(create_time)"
            ,"    FROM player_sign WHERE type='SIGN' and status in ('SIGN_SUCCEED','STOP_CONTRACT') and nj_id IN"
            , "<foreach collection='njIds' item='njId' open='(' separator=',' close=')'>"
            , "#{njId}"
            , "</foreach>"
            ,"    GROUP BY nj_id, user_id"
            ,")"
            ,"</script>"
    })
    PageList<PpPlayerSign> pagePlayerSignBest(@Param("njIds") List<Long> njIds, @Param(ParamContants.PAGE_NUMBER)int pageNumber, @Param(ParamContants.PAGE_SIZE)int pageSize);

    @Select({
            "<script>"
            , "SELECT user_id"
            ,"FROM player_sign"
            ,"WHERE status='SIGN_SUCCEED' and type='SIGN' and nj_id IN "
            , "<foreach collection='njIds' item='njId' open='(' separator=',' close=')'>"
            , "#{njId}"
            , "</foreach>"
            ,"AND (nj_id, user_id, create_time) IN ("
            ,"    SELECT nj_id, user_id, MAX(create_time)"
            ,"    FROM player_sign WHERE status='SIGN_SUCCEED' and type='SIGN' and nj_id IN"
            , "<foreach collection='njIds' item='njId' open='(' separator=',' close=')'>"
            , "#{njId}"
            , "</foreach>"
            ,"    GROUP BY nj_id, user_id"
            ,")"
            ,"</script>"
    })
    Set<Long> selectAllSignPlayer(@Param("njIds") List<Long> njIds);

    @Select({
            "<script>"
            , "SELECT nj_id, count(user_id) as userCount"
            ,"FROM player_sign"
            ,"WHERE status='SIGN_SUCCEED' and type='SIGN' and nj_id IN "
            , "<foreach collection='njIds' item='njId' open='(' separator=',' close=')'>"
            , "#{njId}"
            , "</foreach>"
            , "group by nj_id"
            ,"</script>"
    })
    List<CountSignPlayerPo> countSignPlayerByRooms(@Param("njIds")List<Long> njIds);

    @Select({"<script>"
            , "SELECT *"
            , "FROM player_sign"
            , "WHERE type='SIGN' and status in ('SIGN_SUCCEED','STOP_CONTRACT') and nj_id in "

            , "<foreach collection='param.roomIds' item='njId' open='(' separator=',' close=')'>"
            , "#{njId}"
            , "</foreach>"

            , "<if test=' null != param.playerId and param.playerId > 0 '>"
            , "    and user_id = #{param.playerId}"
            , "</if>"

            , "<if test=' null != param.signStartDate '>"
            , "    and start_time&gt;=#{param.signStartDate}"
            , "</if>"
            , "<if test=' null != param.signEndDate '>"
            , "    and start_time&lt;=#{param.signEndDate}"
            , "</if>"

            , "<if test=' null != param.expireStartDate '>"
            , "    and end_time&gt;=#{param.expireStartDate}"
            , "</if>"
            , "<if test=' null != param.expireEndDate '>"
            , "    and end_time&lt;=#{param.expireEndDate}"
            , "</if>"

            , "<if test=' null != param.stopStartDate '>"
            , "    and stop_time&gt;=#{param.stopStartDate}"
            , "</if>"
            , "<if test=' null != param.stopEndDate '>"
            , "    and stop_time&lt;=#{param.stopEndDate}"
            , "</if>"

            , "<if test=' null != param.signStatus and param.signStatus == 1 '>"
            , "    and status='SIGN_SUCCEED'"
            , "</if>"

            , "<if test=' null != param.signStatus and param.signStatus == 0 '>"
            , "    and status = 'STOP_CONTRACT'"
            , "</if>"

            , "AND (user_id, create_time) IN ("
            , "    SELECT user_id, MAX(create_time)"
            , "    FROM player_sign"
            , "    WHERE type='SIGN' and status in ('SIGN_SUCCEED','STOP_CONTRACT') and nj_id in "

            , "<foreach collection='param.roomIds' item='njId' open='(' separator=',' close=')'>"
            , "#{njId}"
            , "</foreach>"

            , "<if test=' null != param.playerId and param.playerId > 0 '>"
            , "    and user_id = #{param.playerId}"
            , "</if>"

            , "    GROUP BY user_id"
            , ")"

            , "order by start_time desc"
            , "</script>"})
    PageList<PpPlayerSign> signPlayerPageList(@Param("param") SMSignPlayerPageListReqDto param, @Param(ParamContants.PAGE_NUMBER) int pageNumber, @Param(ParamContants.PAGE_SIZE) int pageSize);


    /**
     * 按照时间范围查询签约的用户
     * @param njIds
     * @param startDate
     * @param endDate
     * @return
     */
    @Select({"<script>"
            , "select distinct user_id"
            , "from player_sign"
            , "where type = 'SIGN' and nj_id IN"

            , "<foreach collection='njIds' item='njId' open='(' separator=',' close=')'>"
            , "#{njId}"
            , "</foreach>"

            , "  and ((status = 'SIGN_SUCCEED' and start_time &lt;= #{endDate} ) or"
            , "       (status = 'STOP_CONTRACT' and start_time &lt;= #{endDate} and"
            , "        stop_time &gt;= #{startDate}))"
            , "</script>"})
    List<Long> selectAllSignPlayerByDate(@Param("njIds") List<Long> njIds, @Param("startDate") String startDate, @Param("endDate") String endDate);

    @Select({
            "<script>"
            , "select * from player_sign"
            , "<where>"

            , "<if test='null != entity.contractId and entity.contractId > 0'>"
            , "and id = #{entity.contractId}"
            , "</if>"

            , "<if test='null != entity.parentId and entity.parentId > 0'>"
            , "and parent_id = #{entity.parentId}"
            , "</if>"

            , "<if test='null != entity.contractIdLists and entity.contractIdLists.size > 0'>"
            , "and id in "
            , "<foreach collection='entity.contractIdLists' item='cId' open='(' separator=',' close=')'>"
            , "#{cId}"
            , "</foreach>"
            , "</if>"

            , "<if test='null != entity.userId and entity.userId > 0'>"
            , "and user_id = #{entity.userId}"
            , "</if>"

            , "<if test='null != entity.njIds and entity.njIds.size > 0'>"
            , "and nj_id in "
            , "<foreach collection='entity.njIds' item='njId' open='(' separator=',' close=')'>"
            , "#{njId}"
            , "</foreach>"
            , "</if>"

            , "<if test='null != entity.types and entity.types.size > 0'>"
            , "and type in "
            , "<foreach collection='entity.types' item='t' open='(' separator=',' close=')'>"
            , "#{t}"
            , "</foreach>"
            , "</if>"

            , "<if test='null != entity.statuses and entity.statuses.size > 0'>"
            , "and status in "
            , "<foreach collection='entity.statuses' item='s' open='(' separator=',' close=')'>"
            , "#{s}"
            , "</foreach>"
            , "</if>"

            , "<if test='null != entity.userOrNjId and entity.userOrNjId > 0'>"
            , "and (user_id=#{entity.userOrNjId} or nj_id=#{entity.userOrNjId})"
            , "</if>"

            , "<if test='entity.descCreateTime'>"
            , "order by create_time desc "
            , "</if>"

            , "</where>"
            ,"</script>"
    })
    PageList<PpPlayerSign> pageByEntity(@Param("entity") QueryPlayerSignPo entity, @Param(ParamContants.PAGE_NUMBER) int pageNumber, @Param(ParamContants.PAGE_SIZE) int pageSize);


}
