package fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.datastore.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ActivityAnnouncementDeployRecordExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table activity_announcement_deploy_record
     *
     * @mbg.generated Sat Oct 19 16:17:17 CST 2024
     */
    public ActivityAnnouncementDeployRecordExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table activity_announcement_deploy_record
     *
     * @mbg.generated Sat Oct 19 16:17:17 CST 2024
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table activity_announcement_deploy_record
     *
     * @mbg.generated Sat Oct 19 16:17:17 CST 2024
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table activity_announcement_deploy_record
     *
     * @mbg.generated Sat Oct 19 16:17:17 CST 2024
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table activity_announcement_deploy_record
     *
     * @mbg.generated Sat Oct 19 16:17:17 CST 2024
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table activity_announcement_deploy_record
     *
     * @mbg.generated Sat Oct 19 16:17:17 CST 2024
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table activity_announcement_deploy_record
     *
     * @mbg.generated Sat Oct 19 16:17:17 CST 2024
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table activity_announcement_deploy_record
     *
     * @mbg.generated Sat Oct 19 16:17:17 CST 2024
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table activity_announcement_deploy_record
     *
     * @mbg.generated Sat Oct 19 16:17:17 CST 2024
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table activity_announcement_deploy_record
     *
     * @mbg.generated Sat Oct 19 16:17:17 CST 2024
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table activity_announcement_deploy_record
     *
     * @mbg.generated Sat Oct 19 16:17:17 CST 2024
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public Class getEntityClass() {
        return fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.datastore.entity.ActivityAnnouncementDeployRecord.class;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table activity_announcement_deploy_record
     *
     * @mbg.generated Sat Oct 19 16:17:17 CST 2024
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andActivityIdIsNull() {
            addCriterion("activity_id is null");
            return (Criteria) this;
        }

        public Criteria andActivityIdIsNotNull() {
            addCriterion("activity_id is not null");
            return (Criteria) this;
        }

        public Criteria andActivityIdEqualTo(Long value) {
            addCriterion("activity_id =", value, "activityId");
            return (Criteria) this;
        }

        public Criteria andActivityIdNotEqualTo(Long value) {
            addCriterion("activity_id <>", value, "activityId");
            return (Criteria) this;
        }

        public Criteria andActivityIdGreaterThan(Long value) {
            addCriterion("activity_id >", value, "activityId");
            return (Criteria) this;
        }

        public Criteria andActivityIdGreaterThanOrEqualTo(Long value) {
            addCriterion("activity_id >=", value, "activityId");
            return (Criteria) this;
        }

        public Criteria andActivityIdLessThan(Long value) {
            addCriterion("activity_id <", value, "activityId");
            return (Criteria) this;
        }

        public Criteria andActivityIdLessThanOrEqualTo(Long value) {
            addCriterion("activity_id <=", value, "activityId");
            return (Criteria) this;
        }

        public Criteria andActivityIdIn(List<Long> values) {
            addCriterion("activity_id in", values, "activityId");
            return (Criteria) this;
        }

        public Criteria andActivityIdNotIn(List<Long> values) {
            addCriterion("activity_id not in", values, "activityId");
            return (Criteria) this;
        }

        public Criteria andActivityIdBetween(Long value1, Long value2) {
            addCriterion("activity_id between", value1, value2, "activityId");
            return (Criteria) this;
        }

        public Criteria andActivityIdNotBetween(Long value1, Long value2) {
            addCriterion("activity_id not between", value1, value2, "activityId");
            return (Criteria) this;
        }

        public Criteria andAppIdIsNull() {
            addCriterion("app_id is null");
            return (Criteria) this;
        }

        public Criteria andAppIdIsNotNull() {
            addCriterion("app_id is not null");
            return (Criteria) this;
        }

        public Criteria andAppIdEqualTo(Integer value) {
            addCriterion("app_id =", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotEqualTo(Integer value) {
            addCriterion("app_id <>", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdGreaterThan(Integer value) {
            addCriterion("app_id >", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("app_id >=", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdLessThan(Integer value) {
            addCriterion("app_id <", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdLessThanOrEqualTo(Integer value) {
            addCriterion("app_id <=", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdIn(List<Integer> values) {
            addCriterion("app_id in", values, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotIn(List<Integer> values) {
            addCriterion("app_id not in", values, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdBetween(Integer value1, Integer value2) {
            addCriterion("app_id between", value1, value2, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotBetween(Integer value1, Integer value2) {
            addCriterion("app_id not between", value1, value2, "appId");
            return (Criteria) this;
        }

        public Criteria andNjIdIsNull() {
            addCriterion("nj_id is null");
            return (Criteria) this;
        }

        public Criteria andNjIdIsNotNull() {
            addCriterion("nj_id is not null");
            return (Criteria) this;
        }

        public Criteria andNjIdEqualTo(Long value) {
            addCriterion("nj_id =", value, "njId");
            return (Criteria) this;
        }

        public Criteria andNjIdNotEqualTo(Long value) {
            addCriterion("nj_id <>", value, "njId");
            return (Criteria) this;
        }

        public Criteria andNjIdGreaterThan(Long value) {
            addCriterion("nj_id >", value, "njId");
            return (Criteria) this;
        }

        public Criteria andNjIdGreaterThanOrEqualTo(Long value) {
            addCriterion("nj_id >=", value, "njId");
            return (Criteria) this;
        }

        public Criteria andNjIdLessThan(Long value) {
            addCriterion("nj_id <", value, "njId");
            return (Criteria) this;
        }

        public Criteria andNjIdLessThanOrEqualTo(Long value) {
            addCriterion("nj_id <=", value, "njId");
            return (Criteria) this;
        }

        public Criteria andNjIdIn(List<Long> values) {
            addCriterion("nj_id in", values, "njId");
            return (Criteria) this;
        }

        public Criteria andNjIdNotIn(List<Long> values) {
            addCriterion("nj_id not in", values, "njId");
            return (Criteria) this;
        }

        public Criteria andNjIdBetween(Long value1, Long value2) {
            addCriterion("nj_id between", value1, value2, "njId");
            return (Criteria) this;
        }

        public Criteria andNjIdNotBetween(Long value1, Long value2) {
            addCriterion("nj_id not between", value1, value2, "njId");
            return (Criteria) this;
        }

        public Criteria andOriginalAnnouncementIsNull() {
            addCriterion("original_announcement is null");
            return (Criteria) this;
        }

        public Criteria andOriginalAnnouncementIsNotNull() {
            addCriterion("original_announcement is not null");
            return (Criteria) this;
        }

        public Criteria andOriginalAnnouncementEqualTo(String value) {
            addCriterion("original_announcement =", value, "originalAnnouncement");
            return (Criteria) this;
        }

        public Criteria andOriginalAnnouncementNotEqualTo(String value) {
            addCriterion("original_announcement <>", value, "originalAnnouncement");
            return (Criteria) this;
        }

        public Criteria andOriginalAnnouncementGreaterThan(String value) {
            addCriterion("original_announcement >", value, "originalAnnouncement");
            return (Criteria) this;
        }

        public Criteria andOriginalAnnouncementGreaterThanOrEqualTo(String value) {
            addCriterion("original_announcement >=", value, "originalAnnouncement");
            return (Criteria) this;
        }

        public Criteria andOriginalAnnouncementLessThan(String value) {
            addCriterion("original_announcement <", value, "originalAnnouncement");
            return (Criteria) this;
        }

        public Criteria andOriginalAnnouncementLessThanOrEqualTo(String value) {
            addCriterion("original_announcement <=", value, "originalAnnouncement");
            return (Criteria) this;
        }

        public Criteria andOriginalAnnouncementLike(String value) {
            addCriterion("original_announcement like", value, "originalAnnouncement");
            return (Criteria) this;
        }

        public Criteria andOriginalAnnouncementNotLike(String value) {
            addCriterion("original_announcement not like", value, "originalAnnouncement");
            return (Criteria) this;
        }

        public Criteria andOriginalAnnouncementIn(List<String> values) {
            addCriterion("original_announcement in", values, "originalAnnouncement");
            return (Criteria) this;
        }

        public Criteria andOriginalAnnouncementNotIn(List<String> values) {
            addCriterion("original_announcement not in", values, "originalAnnouncement");
            return (Criteria) this;
        }

        public Criteria andOriginalAnnouncementBetween(String value1, String value2) {
            addCriterion("original_announcement between", value1, value2, "originalAnnouncement");
            return (Criteria) this;
        }

        public Criteria andOriginalAnnouncementNotBetween(String value1, String value2) {
            addCriterion("original_announcement not between", value1, value2, "originalAnnouncement");
            return (Criteria) this;
        }

        public Criteria andOriginalAnnouncementImgUrlIsNull() {
            addCriterion("original_announcement_img_url is null");
            return (Criteria) this;
        }

        public Criteria andOriginalAnnouncementImgUrlIsNotNull() {
            addCriterion("original_announcement_img_url is not null");
            return (Criteria) this;
        }

        public Criteria andOriginalAnnouncementImgUrlEqualTo(String value) {
            addCriterion("original_announcement_img_url =", value, "originalAnnouncementImgUrl");
            return (Criteria) this;
        }

        public Criteria andOriginalAnnouncementImgUrlNotEqualTo(String value) {
            addCriterion("original_announcement_img_url <>", value, "originalAnnouncementImgUrl");
            return (Criteria) this;
        }

        public Criteria andOriginalAnnouncementImgUrlGreaterThan(String value) {
            addCriterion("original_announcement_img_url >", value, "originalAnnouncementImgUrl");
            return (Criteria) this;
        }

        public Criteria andOriginalAnnouncementImgUrlGreaterThanOrEqualTo(String value) {
            addCriterion("original_announcement_img_url >=", value, "originalAnnouncementImgUrl");
            return (Criteria) this;
        }

        public Criteria andOriginalAnnouncementImgUrlLessThan(String value) {
            addCriterion("original_announcement_img_url <", value, "originalAnnouncementImgUrl");
            return (Criteria) this;
        }

        public Criteria andOriginalAnnouncementImgUrlLessThanOrEqualTo(String value) {
            addCriterion("original_announcement_img_url <=", value, "originalAnnouncementImgUrl");
            return (Criteria) this;
        }

        public Criteria andOriginalAnnouncementImgUrlLike(String value) {
            addCriterion("original_announcement_img_url like", value, "originalAnnouncementImgUrl");
            return (Criteria) this;
        }

        public Criteria andOriginalAnnouncementImgUrlNotLike(String value) {
            addCriterion("original_announcement_img_url not like", value, "originalAnnouncementImgUrl");
            return (Criteria) this;
        }

        public Criteria andOriginalAnnouncementImgUrlIn(List<String> values) {
            addCriterion("original_announcement_img_url in", values, "originalAnnouncementImgUrl");
            return (Criteria) this;
        }

        public Criteria andOriginalAnnouncementImgUrlNotIn(List<String> values) {
            addCriterion("original_announcement_img_url not in", values, "originalAnnouncementImgUrl");
            return (Criteria) this;
        }

        public Criteria andOriginalAnnouncementImgUrlBetween(String value1, String value2) {
            addCriterion("original_announcement_img_url between", value1, value2, "originalAnnouncementImgUrl");
            return (Criteria) this;
        }

        public Criteria andOriginalAnnouncementImgUrlNotBetween(String value1, String value2) {
            addCriterion("original_announcement_img_url not between", value1, value2, "originalAnnouncementImgUrl");
            return (Criteria) this;
        }

        public Criteria andAnnouncementIsNull() {
            addCriterion("announcement is null");
            return (Criteria) this;
        }

        public Criteria andAnnouncementIsNotNull() {
            addCriterion("announcement is not null");
            return (Criteria) this;
        }

        public Criteria andAnnouncementEqualTo(String value) {
            addCriterion("announcement =", value, "announcement");
            return (Criteria) this;
        }

        public Criteria andAnnouncementNotEqualTo(String value) {
            addCriterion("announcement <>", value, "announcement");
            return (Criteria) this;
        }

        public Criteria andAnnouncementGreaterThan(String value) {
            addCriterion("announcement >", value, "announcement");
            return (Criteria) this;
        }

        public Criteria andAnnouncementGreaterThanOrEqualTo(String value) {
            addCriterion("announcement >=", value, "announcement");
            return (Criteria) this;
        }

        public Criteria andAnnouncementLessThan(String value) {
            addCriterion("announcement <", value, "announcement");
            return (Criteria) this;
        }

        public Criteria andAnnouncementLessThanOrEqualTo(String value) {
            addCriterion("announcement <=", value, "announcement");
            return (Criteria) this;
        }

        public Criteria andAnnouncementLike(String value) {
            addCriterion("announcement like", value, "announcement");
            return (Criteria) this;
        }

        public Criteria andAnnouncementNotLike(String value) {
            addCriterion("announcement not like", value, "announcement");
            return (Criteria) this;
        }

        public Criteria andAnnouncementIn(List<String> values) {
            addCriterion("announcement in", values, "announcement");
            return (Criteria) this;
        }

        public Criteria andAnnouncementNotIn(List<String> values) {
            addCriterion("announcement not in", values, "announcement");
            return (Criteria) this;
        }

        public Criteria andAnnouncementBetween(String value1, String value2) {
            addCriterion("announcement between", value1, value2, "announcement");
            return (Criteria) this;
        }

        public Criteria andAnnouncementNotBetween(String value1, String value2) {
            addCriterion("announcement not between", value1, value2, "announcement");
            return (Criteria) this;
        }

        public Criteria andAnnouncementImgUrlIsNull() {
            addCriterion("announcement_img_url is null");
            return (Criteria) this;
        }

        public Criteria andAnnouncementImgUrlIsNotNull() {
            addCriterion("announcement_img_url is not null");
            return (Criteria) this;
        }

        public Criteria andAnnouncementImgUrlEqualTo(String value) {
            addCriterion("announcement_img_url =", value, "announcementImgUrl");
            return (Criteria) this;
        }

        public Criteria andAnnouncementImgUrlNotEqualTo(String value) {
            addCriterion("announcement_img_url <>", value, "announcementImgUrl");
            return (Criteria) this;
        }

        public Criteria andAnnouncementImgUrlGreaterThan(String value) {
            addCriterion("announcement_img_url >", value, "announcementImgUrl");
            return (Criteria) this;
        }

        public Criteria andAnnouncementImgUrlGreaterThanOrEqualTo(String value) {
            addCriterion("announcement_img_url >=", value, "announcementImgUrl");
            return (Criteria) this;
        }

        public Criteria andAnnouncementImgUrlLessThan(String value) {
            addCriterion("announcement_img_url <", value, "announcementImgUrl");
            return (Criteria) this;
        }

        public Criteria andAnnouncementImgUrlLessThanOrEqualTo(String value) {
            addCriterion("announcement_img_url <=", value, "announcementImgUrl");
            return (Criteria) this;
        }

        public Criteria andAnnouncementImgUrlLike(String value) {
            addCriterion("announcement_img_url like", value, "announcementImgUrl");
            return (Criteria) this;
        }

        public Criteria andAnnouncementImgUrlNotLike(String value) {
            addCriterion("announcement_img_url not like", value, "announcementImgUrl");
            return (Criteria) this;
        }

        public Criteria andAnnouncementImgUrlIn(List<String> values) {
            addCriterion("announcement_img_url in", values, "announcementImgUrl");
            return (Criteria) this;
        }

        public Criteria andAnnouncementImgUrlNotIn(List<String> values) {
            addCriterion("announcement_img_url not in", values, "announcementImgUrl");
            return (Criteria) this;
        }

        public Criteria andAnnouncementImgUrlBetween(String value1, String value2) {
            addCriterion("announcement_img_url between", value1, value2, "announcementImgUrl");
            return (Criteria) this;
        }

        public Criteria andAnnouncementImgUrlNotBetween(String value1, String value2) {
            addCriterion("announcement_img_url not between", value1, value2, "announcementImgUrl");
            return (Criteria) this;
        }

        public Criteria andTryCountIsNull() {
            addCriterion("try_count is null");
            return (Criteria) this;
        }

        public Criteria andTryCountIsNotNull() {
            addCriterion("try_count is not null");
            return (Criteria) this;
        }

        public Criteria andTryCountEqualTo(Integer value) {
            addCriterion("try_count =", value, "tryCount");
            return (Criteria) this;
        }

        public Criteria andTryCountNotEqualTo(Integer value) {
            addCriterion("try_count <>", value, "tryCount");
            return (Criteria) this;
        }

        public Criteria andTryCountGreaterThan(Integer value) {
            addCriterion("try_count >", value, "tryCount");
            return (Criteria) this;
        }

        public Criteria andTryCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("try_count >=", value, "tryCount");
            return (Criteria) this;
        }

        public Criteria andTryCountLessThan(Integer value) {
            addCriterion("try_count <", value, "tryCount");
            return (Criteria) this;
        }

        public Criteria andTryCountLessThanOrEqualTo(Integer value) {
            addCriterion("try_count <=", value, "tryCount");
            return (Criteria) this;
        }

        public Criteria andTryCountIn(List<Integer> values) {
            addCriterion("try_count in", values, "tryCount");
            return (Criteria) this;
        }

        public Criteria andTryCountNotIn(List<Integer> values) {
            addCriterion("try_count not in", values, "tryCount");
            return (Criteria) this;
        }

        public Criteria andTryCountBetween(Integer value1, Integer value2) {
            addCriterion("try_count between", value1, value2, "tryCount");
            return (Criteria) this;
        }

        public Criteria andTryCountNotBetween(Integer value1, Integer value2) {
            addCriterion("try_count not between", value1, value2, "tryCount");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Integer value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Integer value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Integer value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Integer value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Integer value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Integer> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Integer> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Integer value1, Integer value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStartTimeIsNull() {
            addCriterion("start_time is null");
            return (Criteria) this;
        }

        public Criteria andStartTimeIsNotNull() {
            addCriterion("start_time is not null");
            return (Criteria) this;
        }

        public Criteria andStartTimeEqualTo(Date value) {
            addCriterion("start_time =", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeNotEqualTo(Date value) {
            addCriterion("start_time <>", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeGreaterThan(Date value) {
            addCriterion("start_time >", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("start_time >=", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeLessThan(Date value) {
            addCriterion("start_time <", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeLessThanOrEqualTo(Date value) {
            addCriterion("start_time <=", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeIn(List<Date> values) {
            addCriterion("start_time in", values, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeNotIn(List<Date> values) {
            addCriterion("start_time not in", values, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeBetween(Date value1, Date value2) {
            addCriterion("start_time between", value1, value2, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeNotBetween(Date value1, Date value2) {
            addCriterion("start_time not between", value1, value2, "startTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeIsNull() {
            addCriterion("end_time is null");
            return (Criteria) this;
        }

        public Criteria andEndTimeIsNotNull() {
            addCriterion("end_time is not null");
            return (Criteria) this;
        }

        public Criteria andEndTimeEqualTo(Date value) {
            addCriterion("end_time =", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeNotEqualTo(Date value) {
            addCriterion("end_time <>", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeGreaterThan(Date value) {
            addCriterion("end_time >", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("end_time >=", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeLessThan(Date value) {
            addCriterion("end_time <", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeLessThanOrEqualTo(Date value) {
            addCriterion("end_time <=", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeIn(List<Date> values) {
            addCriterion("end_time in", values, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeNotIn(List<Date> values) {
            addCriterion("end_time not in", values, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeBetween(Date value1, Date value2) {
            addCriterion("end_time between", value1, value2, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeNotBetween(Date value1, Date value2) {
            addCriterion("end_time not between", value1, value2, "endTime");
            return (Criteria) this;
        }

        public Criteria andDeployEnvIsNull() {
            addCriterion("deploy_env is null");
            return (Criteria) this;
        }

        public Criteria andDeployEnvIsNotNull() {
            addCriterion("deploy_env is not null");
            return (Criteria) this;
        }

        public Criteria andDeployEnvEqualTo(String value) {
            addCriterion("deploy_env =", value, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvNotEqualTo(String value) {
            addCriterion("deploy_env <>", value, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvGreaterThan(String value) {
            addCriterion("deploy_env >", value, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvGreaterThanOrEqualTo(String value) {
            addCriterion("deploy_env >=", value, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvLessThan(String value) {
            addCriterion("deploy_env <", value, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvLessThanOrEqualTo(String value) {
            addCriterion("deploy_env <=", value, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvLike(String value) {
            addCriterion("deploy_env like", value, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvNotLike(String value) {
            addCriterion("deploy_env not like", value, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvIn(List<String> values) {
            addCriterion("deploy_env in", values, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvNotIn(List<String> values) {
            addCriterion("deploy_env not in", values, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvBetween(String value1, String value2) {
            addCriterion("deploy_env between", value1, value2, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvNotBetween(String value1, String value2) {
            addCriterion("deploy_env not between", value1, value2, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIsNull() {
            addCriterion("modify_time is null");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIsNotNull() {
            addCriterion("modify_time is not null");
            return (Criteria) this;
        }

        public Criteria andModifyTimeEqualTo(Date value) {
            addCriterion("modify_time =", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotEqualTo(Date value) {
            addCriterion("modify_time <>", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeGreaterThan(Date value) {
            addCriterion("modify_time >", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("modify_time >=", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeLessThan(Date value) {
            addCriterion("modify_time <", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeLessThanOrEqualTo(Date value) {
            addCriterion("modify_time <=", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIn(List<Date> values) {
            addCriterion("modify_time in", values, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotIn(List<Date> values) {
            addCriterion("modify_time not in", values, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeBetween(Date value1, Date value2) {
            addCriterion("modify_time between", value1, value2, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotBetween(Date value1, Date value2) {
            addCriterion("modify_time not between", value1, value2, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table activity_announcement_deploy_record
     *
     * @mbg.generated do_not_delete_during_merge Sat Oct 19 16:17:17 CST 2024
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table activity_announcement_deploy_record
     *
     * @mbg.generated Sat Oct 19 16:17:17 CST 2024
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}