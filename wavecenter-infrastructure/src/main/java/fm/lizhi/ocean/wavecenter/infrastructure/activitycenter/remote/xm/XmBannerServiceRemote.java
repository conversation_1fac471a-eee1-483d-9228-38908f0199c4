package fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.remote.xm;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.remote.IBannerServiceRemote;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.BannerPlateDTO;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.DeleteBannerParamDTO;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.EditBannerParamDTO;
import fm.lizhi.xm.content.api.BannerManagementService;
import fm.lizhi.xm.content.constants.banner.BannerDropStatus;
import fm.lizhi.xm.content.constants.banner.BannerOperation;
import fm.lizhi.xm.content.protocol.BannerManagementProto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class XmBannerServiceRemote implements IBannerServiceRemote {

    @Autowired
    private BannerManagementService xmBannerManagementService;

    @Override
    public Result<Long> editBanner(EditBannerParamDTO param) {
        BannerPlateDTO bannerPlateDTO = param.getBannerPlateDTO();
        BannerManagementProto.BannerPlate bannerPlate = BannerManagementProto.BannerPlate.newBuilder()
                .setBannerPlateName(bannerPlateDTO.getBannerPlateName())
                .setIndex(bannerPlateDTO.getIndex()).setPlateTypeId(bannerPlateDTO.getPlateTypeId())
                .setBannerPlateId(bannerPlateDTO.getBannerPlateId())
                .build();

        BannerManagementProto.Banner banner = BannerManagementProto.Banner.newBuilder()
                .setAction(param.getAction())
                .setImgUrl(param.getImgUrl())
                .setScale(param.getScale())
                .setTitle(param.getTitle())
                .setDropStatus(BannerDropStatus.UP_SHELVES.getStatus())
                .setStartTime(param.getStartTime())
                .setExpireTime(param.getExpireTime())
                .setWealthLevel("[-1,-1]")
                .setAndroidVersion("[-1,-1]")
                .setIosVersion("[-1,-1]")
                .setSeq(param.getSeq())
                .addBannerPlate(bannerPlate)
                .build();

        //该接口是业务的后台配置接口，rCode返回基本都是0，无法通过状态码确认是否成功
        Result<BannerManagementProto.ResponseEditBannerManagement> result = xmBannerManagementService.editBannerManagement(banner);
        return result.rCode() == 0 ? RpcResult.success(result.target().getId()) : RpcResult.fail(EDIT_BANNER_FAIL);
    }

    @Override
    public Result<Void> deleteBanner(DeleteBannerParamDTO param) {
        Result<BannerManagementProto.ResponseBannerManagementOperation> result = xmBannerManagementService.bannerManagementOperation(param.getId(), BannerOperation.DELETE.getStatus());
        if (RpcResult.isFail(result)) {
            return RpcResult.fail(DELETE_BANNER_FAIL);
        }
        return RpcResult.success();
    }

    @Override
    public BusinessEvnEnum getBusinessEvnEnum() {
        return BusinessEvnEnum.XIMI;
    }
}
