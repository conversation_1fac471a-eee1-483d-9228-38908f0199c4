package fm.lizhi.ocean.wavecenter.infrastructure.award.family.manager;

import fm.lizhi.common.datastore.mysql.exception.DatastoreMysqlOperationException;
import fm.lizhi.ocean.wavecenter.infrastructure.award.family.datastore.dao.FamilyAwardLevelDataDao;
import fm.lizhi.ocean.wavecenter.infrastructure.award.family.datastore.entity.WcFamilyAwardLevelData;
import fm.lizhi.ocean.wavecenter.infrastructure.common.util.ExceptionUtil;
import fm.lizhi.ocean.wavecenter.service.award.family.dto.FamilyAwardLevelDataDTO;
import fm.lizhi.ocean.wavecenter.service.award.family.manager.FamilyAwardLevelDataManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

@Slf4j
@Component
public class FamilyAwardLevelDataManagerImpl implements FamilyAwardLevelDataManager {

    @Autowired
    private FamilyAwardLevelDataDao familyAwardLevelDataDao;

    @Override
    public void save(FamilyAwardLevelDataDTO data) {
        Integer appId = data.getAppId();
        Long familyId = data.getFamilyId();
        Date startTime = data.getStartTime();
        WcFamilyAwardLevelData oldEntity = familyAwardLevelDataDao.getData(appId, familyId, startTime);
        if (oldEntity == null) {
            try {
                familyAwardLevelDataDao.createData(data);
            } catch (DatastoreMysqlOperationException e) {
                // 可能并发插入导致的重复, 再给一次更新的机会
                if (ExceptionUtil.isDuplicateKeyException(e)) {
                    log.trace("DuplicateKeyException, try to update data, data={}", data, e);
                    familyAwardLevelDataDao.updateData(data);
                } else {
                    throw e;
                }
            }
        } else {
            familyAwardLevelDataDao.updateData(data);
        }
    }
}
