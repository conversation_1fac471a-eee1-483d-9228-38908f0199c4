package fm.lizhi.ocean.wavecenter.infrastructure.award.family.datastore.param;

import fm.lizhi.ocean.wavecenter.api.award.family.constants.FamilyAwardResourceTypeEnum;
import lombok.Data;

/**
 * 创建公会奖励发放发放条目参数
 */
@Data
public class CreateFamilyAwardDeliverItemParam {

    /**
     * 资源类型
     */
    private FamilyAwardResourceTypeEnum resourceType;

    /**
     * 资源数量
     */
    private int resourceNumber;

    /**
     * 资源有效期, 默认单位为天
     */
    private int resourceValidPeriod;

    /**
     * 资源id, 如果资源类型需要具体的业务资源记录, 则是业务资源id
     */
    private long resourceId;

    /**
     * 资源名称, 冗余存储具体的业务资源名称
     */
    private String resourceName;

    /**
     * 资源图片, 冗余存储具体的业务资源图片
     */
    private String resourceImage;
}
