package fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.manager;

import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.bean.ActivityLevelConfigBean;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.bean.ActivityRecommendCardConfigBean;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestSaveActivityRecommendCard;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestUpdateActivityRecommendCard;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.service.ActivityRecommendCardConfigService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.convert.ActivityRecommendCardConfigConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.datastore.entity.ActivityRecommendCardConfig;
import fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.datastore.mapper.ActivityRecommendCardConfigMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.common.constants.LogicDeleteConstants;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.manager.ActivityLevelManager;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.manager.ActivityRecommendCardManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class ActivityRecommendCardManagerImpl implements ActivityRecommendCardManager {

    @Autowired
    private ActivityRecommendCardConfigMapper activityRecommendCardConfigMapper;

    @Autowired
    private ActivityLevelManager activityLevelManager;


    @Override
    public Result<Void> saveRecommendCard(RequestSaveActivityRecommendCard param) {

        Result<Void> checkResult = preCheckRecommend(param.getAppId(), param.getLevelId(), true);
        if (RpcResult.isFail(checkResult)){
            return checkResult;
        }

        ActivityRecommendCardConfig paramBean = ActivityRecommendCardConfigConvert.I.buildRecommendCardSaveParam(param);
        paramBean.setDeployEnv(ConfigUtils.getEnvRequired().name());
        paramBean.setDeleted(LogicDeleteConstants.NOT_DELETED);

        return activityRecommendCardConfigMapper.insert(paramBean) > 0
                ? RpcResult.success()
                : RpcResult.fail(ActivityRecommendCardConfigService.SAVE_RECOMMEND_CARD_FAIL, "保存推荐卡失败");
    }

    @Override
    public Result<Void> updateRecommendCard(RequestUpdateActivityRecommendCard param) {

        ActivityRecommendCardConfig recommendCard = activityRecommendCardConfigMapper.selectByPrimaryKey(ActivityRecommendCardConfig.builder().id(param.getId()).build());
        Result<Void> checkResult = preCheckRecommend(param.getAppId(), param.getLevelId(), !recommendCard.getLevelId().equals(param.getLevelId()));
        if (RpcResult.isFail(checkResult)){
            return checkResult;
        }

        ActivityRecommendCardConfig paramBean = ActivityRecommendCardConfigConvert.I.buildRecommendCardUpdateParam(param);
        return activityRecommendCardConfigMapper.updateByPrimaryKey(paramBean) > 0
                ? RpcResult.success()
                : RpcResult.fail(ActivityRecommendCardConfigService.UPDATE_RECOMMEND_CARD_FAIL, "更新推荐卡失败");
    }

    @Override
    public Result<Void> deleteRecommendCard(Long id, String operator) {

        ActivityRecommendCardConfig param = ActivityRecommendCardConfig.builder().id(id).deleted(LogicDeleteConstants.DELETED).operator(operator).build();
        return activityRecommendCardConfigMapper.updateByPrimaryKey(param) > 0
                ? RpcResult.success()
                : RpcResult.fail(ActivityRecommendCardConfigService.DELETE_RECOMMEND_CARD_FAIL, "删除推荐卡失败");
    }


    @Override
    public Result<List<ActivityRecommendCardConfigBean>> listByAppId(Integer appId) {

        ActivityRecommendCardConfig param = ActivityRecommendCardConfig.builder()
                .appId(appId)
                .deleted(LogicDeleteConstants.NOT_DELETED)
                .deployEnv(ConfigUtils.getEnvRequired().name())
                .build();
        List<ActivityRecommendCardConfig> recommendCardList = activityRecommendCardConfigMapper.selectMany(param);

        List<ActivityRecommendCardConfigBean> resultList = recommendCardList.stream()
                .map(ActivityRecommendCardConfigConvert.I::buildResponseActivityRecommendCard).
                collect(Collectors.toList());

        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, resultList);
    }

    @Override
    public ActivityRecommendCardConfigBean getRecommendCardByAppIdAndLevelId(Integer appId, Long levelId) {
        ActivityRecommendCardConfig param = ActivityRecommendCardConfig.builder().appId(appId).levelId(levelId).build();
        return ActivityRecommendCardConfigConvert.I.recommendCardConfig2RecommendCardConfigBean(activityRecommendCardConfigMapper.selectOne(param));
    }


    /**
     * 推荐卡变更前的检查
     */
    private Result<Void> preCheckRecommend(int appId, Long levelId, boolean checkCardExist) {
        // 校验等级是否存在
        Boolean levelExist = activityLevelManager.existLevelById(appId, levelId);
        if (!levelExist){
            log.warn("level is not exist. appId={}, levelId={}", appId, levelId);
            return RpcResult.fail(ActivityRecommendCardConfigService.RECOMMEND_CARD_LEVEL_NOT_EXIST, "活动等级不存在");
        }

        if (checkCardExist){
            ActivityRecommendCardConfigBean recommendCard = this.getRecommendCardByAppIdAndLevelId(appId, levelId);
            if (recommendCard != null){
                log.warn("recommend card is exist. appId={}, levelId={}", appId, levelId);
                return RpcResult.fail(ActivityRecommendCardConfigService.RECOMMEND_CARD_REPEAT, "推荐卡已存在");
            }
        }
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, null);
    }

}
