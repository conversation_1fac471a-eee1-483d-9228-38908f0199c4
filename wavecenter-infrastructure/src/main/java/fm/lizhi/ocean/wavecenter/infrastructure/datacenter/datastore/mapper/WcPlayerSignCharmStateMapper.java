package fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.mapper;

import fm.lizhi.common.datastore.core.annotation.DataStore;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.entity.PlayerSignCharmSumPo;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.po.CharmStatPo;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.po.PlayerSignPerformancePo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@DataStore(namespace = "mysql_ocean_wavecenter")
public interface WcPlayerSignCharmStateMapper {

    @Select({
            "<script>"
                    + "select s.user_id, s.room_id as njId, sum(s.value) value "
                    + "from wavecenter_player_sign_charm_stat s "
                    + "where s.stat_day=#{date} and s.app_id=#{appId} and s.family_id=#{familyId} "
                    + "<if test='userIds != null and userIds.size() > 0'>"
                    + "and user_id in "
                    +   "<foreach item='userId' collection='userIds' open='(' separator=',' close=')'>"
                    +       "#{userId}"
                    +   "</foreach>"
                    + "</if>"
                    + "group by s.user_id, s.room_id"
                    + "</script>"
    })
    List<CharmStatPo> getPlayerCharmInfo(@Param("appId") Integer appId, @Param("date") String date,
                                         @Param("userIds") List<Long> userIds, @Param("familyId") Long familyId);

    @Select({
            "<script>"
                    + "select user_id , sum(value) value "
                    + "from wavecenter_player_sign_charm_stat s "
                    + "where s.stat_day=#{date} and room_id = #{njId} and s.app_id=#{appId} "
                    + "<if test='userIds != null and userIds.size() > 0'>"
                    + "and user_id in "
                    +   "<foreach item='userId' collection='userIds' open='(' separator=',' close=')'>"
                    +       "#{userId}"
                    +   "</foreach>"
                    + "</if>"
                    + "group by s.user_id "
                    + "</script>"
    })
    List<CharmStatPo> roomPlayerCharmInfo(@Param("appId") Integer appId, @Param("njId") long njId, @Param("date") String date, @Param("userIds") List<Long> userIds, @Param("orderType") String orderType);

    @Select({
            "<script>"
            , "select s.user_id, s.room_id as njId, sum(s.value) value "
            , "from wavecenter_player_sign_charm_stat s"
            , "where s.stat_day&gt;=#{startDate} and s.stat_day&lt;=#{endDate} and s.app_id=#{appId} "
            , "<if test='roomIds != null and roomIds.size() > 0'>"
            , "and s.room_id in "
            ,   "<foreach item='roomId' collection='roomIds' open='(' separator=',' close=')'>"
            ,       "#{roomId}"
            ,   "</foreach>"
            , "</if>"
            , "group by s.user_id, s.room_id"
            , "</script>"
    })
    List<CharmStatPo> getRoomPlayerCharmByRooms(@Param("appId") Integer appId, @Param("roomIds") List<Long> roomIds, @Param("startDate") String startDate, @Param("endDate") String endDate);

    @Select({
            "<script>"
            , "select s.user_id, sum(s.value) totalValue"
            , "from wavecenter_player_sign_charm_stat s"
            , "where s.family_id = #{familyId}"
            , "and s.room_id = #{njId}"
            , "and s.app_id= #{appId}"
            , "and s.stat_day &gt;= #{startDate}"
            , "and s.stat_day &lt;= #{endDate}"
            , "group by s.user_id "
//            , "having totalValue != 0"
//            , "order by totalValue"
            ,"</script>"
    })
    List<PlayerSignPerformancePo> selectPlayerSignCharmSum(@Param("njId") long njId
            , @Param("familyId") long familyId
            , @Param("appId") int appId
            , @Param("startDate") String startDate
            , @Param("endDate") String endDate);

    @Select({
            "<script>"
            , "select s.user_id, sum(s.value) totalValue"
            , "from wavecenter_player_sign_charm_stat s"
            , "where s.room_id = #{njId} and s.app_id=#{appId} and s.family_id=#{familyId}"
            , "<if test='null != userIds and userIds.size > 0'>"
            , "and s.user_id in "
            , "<foreach collection='userIds' item='uId' open='(' separator=',' close=')'>"
            , "#{uId}"
            , "</foreach>"
            , "</if>"
            , "and s.stat_day &gt;= #{startDate}"
            , "and s.stat_day &lt;= #{endDate}"
            , "group by s.user_id"
            ,"</script>"
    })
    List<PlayerSignCharmSumPo> selectPlayerSignCharmSumByUsers(@Param("njId") long njId
            , @Param("familyId") long familyId
            , @Param("appId") int appId
            , @Param("userIds") List<Long> userIds
            , @Param("startDate") String startDate
            , @Param("endDate") String endDate);

}
