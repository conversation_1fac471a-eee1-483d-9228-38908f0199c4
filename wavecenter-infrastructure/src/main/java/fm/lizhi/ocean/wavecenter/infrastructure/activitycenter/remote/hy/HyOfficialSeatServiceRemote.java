package fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.remote.hy;

import com.alibaba.fastjson.JSONObject;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.hy.content.api.LiveActivityResourceService;
import fm.lizhi.hy.content.protocol.LiveActivityResourceProto;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.convert.ActivityOfficialSeatConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.remote.IOfficialSeatServiceRemote;
import fm.lizhi.ocean.wavecenter.service.activitycenter.config.ActivityConfig;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.HyOfficialSeatDetailDTO;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.ResultDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

@Slf4j
@Component
public class HyOfficialSeatServiceRemote implements IOfficialSeatServiceRemote<HyOfficialSeatDetailDTO> {

    @Autowired
    private LiveActivityResourceService liveActivityResourceService;

    @Autowired
    private ActivityConfig activityConfig;

    @Override
    public Result<ResultDTO> saveOfficialSeatWithActivity(HyOfficialSeatDetailDTO officialSeatDetailDTO) {
        if (officialSeatDetailDTO.getId() == null) {
            //新增
            LiveActivityResourceProto.AddLiveActivityResource resource = LiveActivityResourceProto.AddLiveActivityResource.newBuilder()
                    .setBgImageUrl(officialSeatDetailDTO.getBgImageUrl())
                    .setStartTime(officialSeatDetailDTO.getStartTime())
                    .setEndTime(officialSeatDetailDTO.getEndTime())
                    .setOperator(officialSeatDetailDTO.getOperator())
                    .setPosition(officialSeatDetailDTO.getPosition())
                    .setRemark(officialSeatDetailDTO.getRemark())
                    .setType(officialSeatDetailDTO.getType())
                    .setTabId(officialSeatDetailDTO.getTabId())
                    .setUserIds(officialSeatDetailDTO.getUserIds())
                    .build();
            Result<LiveActivityResourceProto.ResponseAddLiveActivityResource> result = liveActivityResourceService.addLiveActivityResource(resource);
            if (RpcResult.isFail(result)) {
                log.warn("HyOfficialSeatServiceRemote.saveOfficialSeatWithActivity code:{},officialSeatDetailDTO:{}", result.rCode(), JSONObject.toJSONString(officialSeatDetailDTO));
                if (result.rCode() == LiveActivityResourceService.ADD_LIVE_ACTIVITY_RESOURCE_DUPLICATE_POSITION) {
                    return RpcResult.fail(SAVE_OFFICIAL_SEAT_WITH_ACTIVITY_DUPLICATE_POSITION);
                }
                return RpcResult.fail(SAVE_OFFICIAL_SEAT_WITH_ACTIVITY_FAIL);
            }
            return RpcResult.success(ResultDTO.success(result.target().getId()));
        } else {
            LiveActivityResourceProto.UpdateLiveActivityResource resource = LiveActivityResourceProto.UpdateLiveActivityResource.newBuilder()
                    .setId(officialSeatDetailDTO.getId())
                    .setBgImageUrl(officialSeatDetailDTO.getBgImageUrl())
                    .setStartTime(officialSeatDetailDTO.getStartTime())
                    .setEndTime(officialSeatDetailDTO.getEndTime())
                    .setOperator(officialSeatDetailDTO.getOperator())
                    .setPosition(officialSeatDetailDTO.getPosition())
                    .setRemark(officialSeatDetailDTO.getRemark())
                    .setType(officialSeatDetailDTO.getType())
                    .setTabId(officialSeatDetailDTO.getTabId())
                    .setUserIds(officialSeatDetailDTO.getUserIds())
                    .build();
            Result<LiveActivityResourceProto.ResponseUpdateLiveActivityResource> result = liveActivityResourceService.updateLiveActivityResource(resource);
            if (RpcResult.isFail(result)) {
                log.warn("HyOfficialSeatServiceRemote.updateLiveActivityResource code:{},officialSeatDetailDTO:{}", result.rCode(), JSONObject.toJSONString(officialSeatDetailDTO));
                if (result.rCode() == LiveActivityResourceService.UPDATE_LIVE_ACTIVITY_RESOURCE_DUPLICATE_POSITION) {
                    return RpcResult.fail(SAVE_OFFICIAL_SEAT_WITH_ACTIVITY_DUPLICATE_POSITION);
                }
                return RpcResult.fail(SAVE_OFFICIAL_SEAT_WITH_ACTIVITY_FAIL);
            }
            return RpcResult.success(ResultDTO.success(officialSeatDetailDTO.getId()));
        }
    }

    @Override
    public Result<HyOfficialSeatDetailDTO> findSameTimeOfficialSeatConfig(Long startTime, Long endTime, Integer seat, Long tabId) {
        LiveActivityResourceProto.QueryParam.Builder pageParam = LiveActivityResourceProto.QueryParam.newBuilder().setPageNumber(1).setPageSize(activityConfig.getMaxPageSize());
        LiveActivityResourceProto.PageListParam param = LiveActivityResourceProto.PageListParam.newBuilder()
                .setStartBeginTime(startTime)
                .setStatus(0)
                .setPosition(seat)
                .setParam(pageParam)
                .setTabId(tabId)
                .build();
        Result<LiveActivityResourceProto.ResponsePageList> result = liveActivityResourceService.pageList(param);
        if (RpcResult.isFail(result)) {
            return RpcResult.fail(FIND_SAME_TIME_OFFICIAL_SEAT_CONFIG_FAIL);
        }

        List<LiveActivityResourceProto.LiveActivityResource> resources = result.target().getLiveActivityResourceList();
        //查找相同时间的相同位置的
        Optional<LiveActivityResourceProto.LiveActivityResource> filterResult = resources.stream()
                .filter(resource -> resource.getStartTime() == startTime && resource.getEndTime() == endTime)
                .findFirst();
        if (!filterResult.isPresent()) {
            return RpcResult.fail(FIND_SAME_TIME_OFFICIAL_SEAT_CONFIG_NOT_SAME);
        }
        LiveActivityResourceProto.LiveActivityResource resource = filterResult.get();
        HyOfficialSeatDetailDTO hyOfficialSeatDetailDTO = ActivityOfficialSeatConvert.I.convertHySeatProto2WaveDTO(resource);
        return RpcResult.success(hyOfficialSeatDetailDTO);
    }

    @Override
    public Result<Void> deleteOfficialSeat(long bizRecordId) {
        Result<LiveActivityResourceProto.ResponseDeleteLiveActivityResource> result = liveActivityResourceService.deleteLiveActivityResource(bizRecordId, "creator");
        if (RpcResult.isFail(result)) {
            log.warn("HyOfficialSeatServiceRemote.deleteOfficialSeat code:{},bizRecordId:{}", result.rCode(), bizRecordId);
            return RpcResult.fail(DELETE_OFFICIAL_SEAT_FAIL);
        }
        return RpcResult.success();
    }

    @Override
    public Result<HyOfficialSeatDetailDTO> findOfficialSeatByRecordId(long bizRecordId) {
        Result<LiveActivityResourceProto.ResponseGetLiveActivityResourceById> result = liveActivityResourceService.getLiveActivityResourceById(bizRecordId);
        if (RpcResult.isFail(result)) {
            log.warn("HyOfficialSeatServiceRemote.findOfficialSeatByRecordId code:{},bizRecordId:{}", result.rCode(), bizRecordId);
            if (result.rCode() == LiveActivityResourceService.GET_LIVE_ACTIVITY_RESOURCE_BY_ID_NOT_EXIST) {
                return RpcResult.fail(FIND_OFFICIAL_SEAT_BY_RECORD_ID_NOT_EXIST);
            }
            return RpcResult.fail(FIND_OFFICIAL_SEAT_BY_RECORD_ID_FAIL);
        }
        if (result.target() != null) {
            LiveActivityResourceProto.ResponseGetLiveActivityResourceById resourceBean = result.target();
            LiveActivityResourceProto.LiveActivityResource resource = resourceBean.getResources();

            HyOfficialSeatDetailDTO seatDetailDTO = new HyOfficialSeatDetailDTO()
                    .setUserIds(resource.getUserIds())
                    .setBgImageUrl(resource.getBgImageUrl())
                    .setEndTime(resource.getEndTime())
                    .setId(resource.getId())
                    .setOperator(resource.getOperator())
                    .setPosition(resource.getPosition())
                    .setId(resource.getId())
                    .setRemark(resource.getRemark())
                    .setStartTime(resource.getStartTime())
                    .setType(resource.getType())
                    .setTabId(resource.getTabId())
                    .setUserGroupId(resource.getUserGroupId());
            return RpcResult.success(seatDetailDTO);
        }
        return RpcResult.fail(FIND_OFFICIAL_SEAT_BY_RECORD_ID_FAIL);
    }


    @Override
    public BusinessEvnEnum getBusinessEvnEnum() {
        return BusinessEvnEnum.HEI_YE;
    }


}
