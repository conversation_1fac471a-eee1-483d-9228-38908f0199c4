package fm.lizhi.ocean.wavecenter.infrastructure.award.singer.singleton;

import fm.lizhi.common.dubbo.DubboClientBuilder;
import fm.lizhi.hy.amusement.api.DressUpGoodsService;
import fm.lizhi.ocean.lamp.common.generic.annotation.ScanBusinessProviderAPI;
import fm.lizhi.ocean.lamp.common.generic.annotation.ScanOceanStandardAPI;
import fm.lizhi.ocean.wavecenter.infrastructure.common.constants.ServiceProviderConstants;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @description:
 * @author: guoyibin
 * @create: 2025/04/09 14:42
 */
@Configuration
@ScanOceanStandardAPI(values = {
})
@ScanBusinessProviderAPI(values = {
//        @ScanBusinessProviderAPI.RegisterProvider(interfaceClass = LivePpUserService.class),
})
public class SingerAwardServiceProvider {

    @Bean
    public DressUpGoodsService hyDressUpGoodsService() {
        return new DubboClientBuilder<>(DressUpGoodsService.class)
                .connections(ServiceProviderConstants.connections)
                .timeoutInMillis(ServiceProviderConstants.timeout)
                .retries(ServiceProviderConstants.readRetries)
                .build();
    }
}
