package fm.lizhi.ocean.wavecenter.infrastructure.anchor.singer.datastore.mapper.extr;

import fm.lizhi.common.datastore.core.annotation.DataStore;
import fm.lizhi.ocean.wavecenter.infrastructure.anchor.singer.datastore.entity.SingerInfo;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.SingerAuthCntDTO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;


@DataStore(namespace = "mysql_ocean_wavecenter")
public interface SingerInfoExtraMapper {


    @Select("<script>" +
            "SELECT " +
            "  nj_id AS njId, " +
            "  effective_type AS singerType, " +
            "  COUNT(*) AS cnt " +
            "FROM (" +
            "   SELECT " +
            "     nj_id, " +
            "     user_id, " +
            "     MAX(CASE WHEN singer_type IN (2,3) THEN 2 ELSE singer_type END) AS effective_type " +
            "   FROM singer_info " +
            "   WHERE app_id = #{appId} " +
            "     AND nj_id IN " +
            "     <foreach item='id' collection='njIds' open='(' separator=',' close=')'>#{id}</foreach> " +
            "     AND singer_status != 3 " +
            "   GROUP BY nj_id, user_id" +
            ") t " +
            "GROUP BY nj_id, effective_type " +
            "ORDER BY nj_id, effective_type" +
            "</script>")
    List<SingerAuthCntDTO> querySingerAuthCnt(@Param("appId") int appId, @Param("njIds") List<Long> njIds);

    /**
     * 根据应用ID、用户ID、歌手类型修改歌手状态
     */
    @Update("<script>" +
            "UPDATE singer_info " +
            "SET singer_status = #{singerInfo.singerStatus}, " +
            "    elimination_reason = #{singerInfo.eliminationReason}, " +
            "    elimination_time = #{singerInfo.eliminationTime}, " +
            "    operator = #{singerInfo.operator}" +
            "    <if test='singerInfo.auditTime != null'>" +
            "        , audit_time = #{singerInfo.auditTime}" +
            "    </if>" +
            "    <if test='singerInfo.singerVerifyId != null'>" +
            "        , singer_verify_id = #{singerInfo.singerVerifyId}" +
            "    </if>" +
            "    <if test='singerInfo.songStyle != null'>" +
            "        , song_style = #{singerInfo.songStyle}" +
            "    </if>" +
            "    <if test='singerInfo.singerType != null'>" +
            "        , singer_type = #{singerInfo.singerType}" +
            "    </if>" +
            "    <if test='singerInfo.originalSinger != null'>" +
            "        , original_singer = #{singerInfo.originalSinger}" +
            "    </if>" +
            "    <if test='singerInfo.familyId != null'>" +
            "        , family_id = #{singerInfo.familyId}" +
            "    </if>" +
            "WHERE id = #{singerInfo.id} " +
            "AND singer_status = #{currentSingerStatus}" +
            "</script>")
    int updateSingerStatus(@Param("singerInfo") SingerInfo singerInfo,
                          @Param("currentSingerStatus") int currentSingerStatus);

    /**
     * 根据应用ID、用户ID、歌手类型修改歌手状态
     */
    @Update("<script>" +
            "UPDATE singer_info " +
            "SET singer_status = #{singerInfo.singerStatus}, " +
            "    elimination_reason = #{singerInfo.eliminationReason}, " +
            "    elimination_time = #{singerInfo.eliminationTime}, " +
            "    operator = #{singerInfo.operator}" +
            "    <if test='singerInfo.auditTime != null'>" +
            "        , audit_time = #{singerInfo.auditTime}" +
            "    </if>" +
            "    <if test='singerInfo.familyId != null'>" +
            "        , family_id = #{singerInfo.familyId}" +
            "    </if>" +
            "WHERE app_id = #{singerInfo.appId} " +
            "AND user_id = #{singerInfo.userId} " +
            "AND singer_type = #{singerInfo.singerType} " +
            "AND deploy_env = #{singerInfo.deployEnv} " +
            "</script>")
    int updateSingerInfo(@Param("singerInfo") SingerInfo singerInfo);




    /**
     * 统计高级歌手数量
     * @param appId 应用ID
     * @param status 歌手状态
     * @param deployEnv 部署环境
     * @return 高级歌手数量
     */
    @Select("<script>" +
            "SELECT COUNT(DISTINCT user_id) AS total_users " +
            "FROM singer_info " +
            "WHERE app_id = #{appId} " +
            "<if test='singerType != null'>" +
            "AND singer_type = #{singerType} " +
            "</if>" +
            "AND singer_status = #{status} " +
            "AND deploy_env = #{deployEnv} " +
            "</script>")
    long countSingerByStatus(@Param("appId") Integer appId, @Param("status") int status, @Param("deployEnv") String deployEnv, @Param("singerType") Integer singerType);

    /**
     * 统计有收入的高级歌手数量
     * @param appId 应用ID
     * @param status 歌手状态
     * @param env 部署环境
     * @param date 统计日期
     * @return 有收入的高级歌手数量
     */
    @Select("<script>" +
            "SELECT COUNT(DISTINCT si.user_id) AS total_users " +
            "FROM singer_info AS si " +
            "LEFT JOIN singer_data_day AS sdd ON si.user_id = sdd.user_id " +
            "WHERE si.app_id = #{appId} " +
            "<if test='singerType != null'>" +
            "AND si.singer_type = #{singerType} " +
            "</if>" +
            "AND si.singer_status = #{status} " +
            "AND si.deploy_env = #{env} " +
            "AND sdd.stat_date_value = #{date} " +
            "AND sdd.last_day_income > 0 " +
            "</script>"
    )
    long countIncomeSingerByStatDate(@Param("appId") Integer appId, @Param("status") int status, @Param("env") String env, @Param("date") Integer date, @Param("singerType") Integer singerType);

    /**
     * 统计原创高级歌手数量
     * @param appId 应用ID
     * @param status 歌手状态
     * @param deployEnv 部署环境
     * @return 原创高级歌手数量
     */
    @Select("<script>" +
            "SELECT COUNT(DISTINCT user_id) AS total_users " +
            "FROM singer_info " +
            "WHERE app_id = #{appId} " +
            "AND singer_type = #{singerType} " +
            "AND singer_status = #{status} " +
            "AND original_singer = #{originalSinger} " +
            "AND deploy_env = #{deployEnv} " +
            "</script>")
    long countSingerByOriginal(@Param("appId") Integer appId, @Param("status") int status, @Param("deployEnv") String deployEnv, @Param("singerType") Integer singerType, @Param("originalSinger") Boolean originalSinger);

    /**
     * 统计有收入的原创高级歌手数量
     * @param appId 应用ID
     * @param status 歌手状态
     * @param env 部署环境
     * @param date 统计日期
     * @return 有收入的原创高级歌手数量
     */
    @Select("<script>" +
            "SELECT COUNT(DISTINCT si.user_id) AS total_users " +
            "FROM singer_info AS si " +
            "LEFT JOIN singer_data_day AS sdd ON si.user_id = sdd.user_id " +
            "WHERE si.app_id = #{appId} " +
            "AND si.singer_type = #{singerType} " +
            "AND si.singer_status = #{status} " +
            "AND si.original_singer = #{originalSinger} " +
            "AND si.deploy_env = #{env} " +
            "AND sdd.stat_date_value = #{date} " +
            "AND sdd.last_day_income > 0 " +
            "</script>"
    )
    long countIncomeSingerByOriginalAndSingerType(@Param("appId") Integer appId, @Param("status") int status, @Param("env") String env,
                                                  @Param("date") Integer date, @Param("singerType") Integer singerType, @Param("originalSinger") Boolean originalSinger
    );
}
