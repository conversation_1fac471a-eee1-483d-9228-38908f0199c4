package fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.job;

import fm.lizhi.common.job.dispatcher.executor.handler.JobExecuteContext;
import fm.lizhi.common.job.dispatcher.executor.handler.JobHandler;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.common.utils.ResultHandler;
import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.manager.ActivityResourceGiveManagerImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 资源发放任务
 */
@Component
@Slf4j
public class ActivityResourceGiveJob implements JobHandler {

    @Autowired
    private ActivityResourceGiveManagerImpl activityResourceManager;

    @Override
    public void execute(JobExecuteContext context) throws Exception {
        for (BusinessEvnEnum env : BusinessEvnEnum.values()) {
            if (env.getOnline() != 1) {
                continue;
            }
            //资源发放操作
            try {
                ResultHandler.handle(env.getAppId(), () -> activityResourceManager.giveResourceJobExecute(env.getAppId()));
            } catch (Exception e) {
                log.error("ActivityResourceGiveJob happen error: appId:{}", env.getAppId(), e);
            }
        }
    }
}
