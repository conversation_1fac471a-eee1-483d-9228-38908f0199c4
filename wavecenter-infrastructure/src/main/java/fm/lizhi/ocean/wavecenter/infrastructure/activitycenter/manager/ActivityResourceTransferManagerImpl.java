package fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.manager;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.file.FileNameUtil;
import cn.hutool.core.text.StrPool;
import fm.lizhi.common.romefs.javasdk.util.MD5Utils;
import fm.lizhi.ocean.wavecenter.common.config.CommonConfig;
import fm.lizhi.ocean.wavecenter.common.config.RomeConfig;
import fm.lizhi.ocean.wavecenter.common.utils.UrlUtils;
import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.constants.ActivityResourceTransferStatusEnum;
import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.datastore.dao.ActivityResourceTransferDao;
import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.datastore.entity.ActivityResourceTransfer;
import fm.lizhi.ocean.wavecenter.infrastructure.common.constants.ExtensionContentTypeEnum;
import fm.lizhi.ocean.wavecenter.infrastructure.common.convert.FileManagerConvert;
import fm.lizhi.ocean.wavecenter.service.activitycenter.config.ActivityConfig;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.ActivityResourceTransferDTO;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.ActivityResourceTransferResultDTO;
import fm.lizhi.ocean.wavecenter.service.activitycenter.manager.ActivityResourceTransferManager;
import fm.lizhi.ocean.wavecenter.service.common.manager.RomeFsManager;
import fm.lizhi.ocean.wavecenter.service.common.param.RomeFsTransferParam;
import fm.lizhi.ocean.wavecenter.service.common.result.RomeFsPutResult;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class ActivityResourceTransferManagerImpl implements ActivityResourceTransferManager {

    @Autowired
    private RomeFsManager romeFsManager;

    @Autowired
    private CommonConfig commonConfig;

    @Autowired
    private ActivityConfig activityConfig;

    @Autowired
    private ActivityResourceTransferDao activityResourceTransferDao;


    @Override
    public void transfer(ActivityResourceTransferDTO dto) {
        List<String> sourceUrls = dto.getSourceUrls();
        if (CollUtil.isEmpty(sourceUrls)) {
            log.info("sourceUrls is empty. appId: {}", dto.getAppId());
            return;
        }

        // 获取已经转存成功的资源，并过滤掉
        List<ActivityResourceTransfer> transferSuccessUrls = activityResourceTransferDao.getTransferSuccessUrls(dto.getAppId(), sourceUrls);
        //查询转存失败次数达到最大的图片列表
        List<ActivityResourceTransfer> transferMaxFailUrls = activityResourceTransferDao.getTransferMaxFailUrls(dto.getAppId(), sourceUrls, activityConfig.getMaxTransferTryCount());
        //过滤出未转存成功的图片
        List<String> ableTransferUrls = filterAbleTransferUrls(sourceUrls, dto.getAppId(), transferMaxFailUrls, transferSuccessUrls);

        if (CollUtil.isEmpty(ableTransferUrls)) {
            return;
        }

        log.info("all ableTransferUrls. appId: {}, urls: {}", dto.getAppId(), ableTransferUrls);
        // 转存资源
        dto.setSourceUrls(ableTransferUrls);
        doTransfer(dto);
    }

    @Override
    public ActivityResourceTransferResultDTO getResourceTransfer(int appId, String sourceUri) {
        return activityResourceTransferDao.getResourceTransfer(appId, sourceUri);
    }

    private void doTransfer(ActivityResourceTransferDTO dto) {
        if (CollUtil.isEmpty(dto.getSourceUrls())) {
            log.info("transfer sourceUrls is empty. appId:{}", dto.getAppId());
            return;
        }

        List<ActivityResourceTransfer> transferList = dto.getSourceUrls().stream().map(url -> {
            ActivityResourceTransfer transfer = new ActivityResourceTransfer();
            transfer.setAppId(dto.getAppId());
            transfer.setSourceUri(UrlUtils.removeHostOrEmpty(url));
            transfer.setStatus(ActivityResourceTransferStatusEnum.SUCCESS.getStatus());
            transfer.setCreateTime(new Date());
            transfer.setUpdateTime(new Date());
            transfer.setTryCount(0);

            try {
                RomeFsTransferParam param = buildTransferParam(dto, url);
                RomeFsPutResult result = romeFsManager.uploadTransfer(param);
                if (result == null) {
                    log.info("transfer fail. romeResult is null. appId:{}, url:{}", dto.getAppId(), url);
                    transfer.setStatus(ActivityResourceTransferStatusEnum.FAIL.getStatus());
                    transfer.setTargetUri("");
                } else {
                    transfer.setTargetUri(UrlUtils.removeHostOrEmpty(result.getFilePath()));
                }

            } catch (Exception e) {
                log.error("transfer error.appId:{}, url:{}", dto.getAppId(), url, e);
                transfer.setStatus(ActivityResourceTransferStatusEnum.FAIL.getStatus());
            }

            return transfer;
        }).collect(Collectors.toList());

        //批量保存
        int row = activityResourceTransferDao.batchSaveTransferResult(transferList);
        log.info("doTransfer appId:{}, total:{}, success:{}", dto.getAppId(), transferList.size(), row);
    }

    /**
     * 构建罗马转存参数
     */
    private @NotNull RomeFsTransferParam buildTransferParam(ActivityResourceTransferDTO dto, String url) {
        RomeConfig romeConfig = commonConfig.getBizConfig(dto.getAppId()).getRomeConfig();
        RomeFsTransferParam param = new RomeFsTransferParam();
        param.setSourceUrl(UrlUtils.addHostOrEmpty(url, commonConfig.getRomeFsDownloadIntranetCdn()));
        param.setAccessModifier(romeConfig.getAccessModifier());
        param.setRomeFsConfig(FileManagerConvert.I.convertRomeFsConfig(romeConfig));
        String urlPrefix = activityConfig.getBizConfig(dto.getAppId()).getResourceTransferUrlPrefix(MD5Utils.MD5(url.getBytes(StandardCharsets.UTF_8)));
        String contentType = resolveContentType(null, url);
        param.setFileName(urlPrefix + StrPool.DOT + FileNameUtil.getSuffix(url));
        param.setContentType(contentType);
        return param;
    }


    private String resolveContentType(String contentType, String fileName) {
        if (StringUtils.isNotBlank(contentType)) {
            return contentType;
        }
        String extension = FilenameUtils.getExtension(fileName);
        return ExtensionContentTypeEnum.getContentType(extension);
    }

    /**
     * 过滤出可转换的图片地址
     *
     * @param sourceUrls          原数图片地址
     * @param appId               应用ID
     * @param transferMaxFailUrls 转换后的失败记录
     * @param transferSuccessUrls 转换后的成功记录
     * @return 可转换的图片地址
     */
    private List<String> filterAbleTransferUrls(List<String> sourceUrls, Integer appId, List<ActivityResourceTransfer> transferMaxFailUrls,
                                                List<ActivityResourceTransfer> transferSuccessUrls) {
        List<String> existSourceUrls = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(transferMaxFailUrls)) {
            List<String> failSourceUris = transferMaxFailUrls.stream().map(ActivityResourceTransfer::getSourceUri).collect(Collectors.toList());
            existSourceUrls.addAll(failSourceUris);
        }

        if (CollectionUtils.isNotEmpty(transferSuccessUrls)) {
            List<String> failSourceUris = transferSuccessUrls.stream().map(ActivityResourceTransfer::getSourceUri).collect(Collectors.toList());
            existSourceUrls.addAll(failSourceUris);
        }

        return sourceUrls.stream().filter(uri -> !existSourceUrls.contains(uri)).collect(Collectors.toList());
    }
}
