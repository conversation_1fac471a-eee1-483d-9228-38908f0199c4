package fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.datastore.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 活动装扮资源表
 *
 * @date 2025-04-15 04:14:58
 */
@Table(name = "`activity_apply_decorate`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ActivityApplyDecorate {
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 应用ID
     */
    @Column(name= "`app_id`")
    private Integer appId;

    /**
     * 活动申请ID
     */
    @Column(name= "`activity_id`")
    private Long activityId;

    /**
     * 装扮ID
     */
    @Column(name= "`decorate_id`")
    private Long decorateId;

    /**
     * 装扮类型 1=头像框,2=房间背景,3=座驾,4=勋章
     */
    @Column(name= "`decorate_type`")
    private Integer decorateType;

    /**
     * 可选值  TEST/PRE/PRO
     */
    @Column(name= "`deploy_env`")
    private String deployEnv;

    /**
     * 是否删除，默认不删除
     */
    @Column(name= "`deleted`")
    private Integer deleted;

    /**
     * 修改时间
     */
    @Column(name= "`modify_time`")
    private Date modifyTime;

    /**
     * 创建时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", appId=").append(appId);
        sb.append(", activityId=").append(activityId);
        sb.append(", decorateId=").append(decorateId);
        sb.append(", decorateType=").append(decorateType);
        sb.append(", deployEnv=").append(deployEnv);
        sb.append(", deleted=").append(deleted);
        sb.append(", modifyTime=").append(modifyTime);
        sb.append(", createTime=").append(createTime);
        sb.append("]");
        return sb.toString();
    }
}