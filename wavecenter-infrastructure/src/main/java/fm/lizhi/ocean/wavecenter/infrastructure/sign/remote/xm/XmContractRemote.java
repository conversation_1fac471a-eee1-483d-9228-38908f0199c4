package fm.lizhi.ocean.wavecenter.infrastructure.sign.remote.xm;

import com.alibaba.fastjson.JSONObject;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.collect.Lists;
import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.util.DateUtil;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.permissions.constants.RoleEnum;
import fm.lizhi.ocean.wavecenter.api.sign.bean.FamilyAndNjContractBean;
import fm.lizhi.ocean.wavecenter.api.sign.bean.RoomSignInfoBean;
import fm.lizhi.ocean.wavecenter.api.sign.bean.SignPlayerInfoBean;
import fm.lizhi.ocean.wavecenter.api.sign.constant.AuditStatusEnum;
import fm.lizhi.ocean.wavecenter.api.sign.constant.ContractTypeEnum;
import fm.lizhi.ocean.wavecenter.api.sign.constant.SignRelationEnum;
import fm.lizhi.ocean.wavecenter.api.sign.request.RequestAdminApplyCancelFamily;
import fm.lizhi.ocean.wavecenter.api.sign.request.RequestFamilyInviteAdmin;
import fm.lizhi.ocean.wavecenter.api.sign.request.RequestUserApplyAdmin;
import fm.lizhi.ocean.wavecenter.api.sign.response.ResponseAdminApplyCancelFamily;
import fm.lizhi.ocean.wavecenter.api.sign.response.ResponseFamilyInviteAdmin;
import fm.lizhi.ocean.wavecenter.api.sign.response.ResponseUserApplyAdmin;
import fm.lizhi.ocean.wavecenter.api.user.bean.FamilyBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.PlayerSignBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.RoomSignBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserBean;
import fm.lizhi.ocean.wavecenter.api.user.constants.SingStatusEnum;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.common.dto.PageDto;
import fm.lizhi.ocean.wavecenter.infrastructure.income.datastore.mapper.XmFamilyNjSettleRatioMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.convert.SignInfraConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.datastore.entity.*;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.datastore.mapper.XmContractMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.datastore.mapper.XmFamilyNjMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.datastore.mapper.XmPlayerSignMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.datastore.mapper.XmUnwindApplyMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.datastore.po.XmFamilyNjSettleRatioPo;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.remote.IContractRemote;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.remote.request.RequestFamilyAdminCancel;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.remote.request.RequestFamilyAndNjCancelApply;
import fm.lizhi.ocean.wavecenter.infrastructure.user.convert.UserConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.user.remote.res.PlayerIncomeStatRes;
import fm.lizhi.ocean.wavecenter.service.resource.recommendcard.dto.ContractInfoDto;
import fm.lizhi.ocean.wavecenter.service.sign.dto.*;
import fm.lizhi.ocean.wavecenter.service.user.config.UserConfig;
import fm.lizhi.ocean.wavecenter.service.user.dto.PlayerSignInfoDto;
import fm.lizhi.trade.contract.constant.ContractType;
import fm.lizhi.xm.family.api.ContractService;
import fm.lizhi.xm.family.api.SignService;
import fm.lizhi.xm.family.api.UnwindContractService;
import fm.lizhi.xm.family.bean.JoinFamilyParam;
import fm.lizhi.xm.family.bean.UnwindContractInfo;
import fm.lizhi.xm.family.protocol.ContractServiceProto;
import fm.lizhi.xm.family.protocol.SignServiceProto;
import fm.lizhi.xm.family.protocol.UnwindContractServiceProto;
import fm.pp.family.bean.ContractInfo;
import fm.pp.family.constants.FamilyConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import xm.fm.lizhi.live.usergroup.api.UserGroupService;
import xm.fm.lizhi.live.usergroup.protocol.UserGroupProto;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Component
public class XmContractRemote implements IContractRemote {

    @Autowired
    private ContractService contractService;

    @Autowired
    private XmContractMapper contractMapper;

    @Autowired
    private XmPlayerSignMapper playerSignMapper;

    @Autowired
    private UserConfig userConfig;

    @Autowired
    private UserGroupService userGroupService;

    @Autowired
    private XmFamilyNjSettleRatioMapper familyNjSettleRatioMapper;

    @Autowired
    private UnwindContractService unwindContractService;

    @Autowired
    private XmUnwindApplyMapper unwindApplyMapper;

    @Autowired
    private XmFamilyNjMapper familyNjMapper;

    @Autowired
    private SignService signService;

    @Override
    public BusinessEvnEnum getBusinessEvnEnum() {
        return BusinessEvnEnum.XIMI;
    }

    private final LoadingCache<Long, Optional<Long>> FAMILY_ID_CACHE = CacheBuilder.newBuilder()
            .maximumSize(2000)
            .expireAfterAccess(10, TimeUnit.MINUTES)
            .recordStats()
            .build(new CacheLoader<Long, Optional<Long>>() {
                @Override
                public Optional<Long> load(Long roomId) throws Exception {
                    return getRoomBestFamily(roomId);
                }
            });


    @Override
    public PageBean<RoomSignBean> getAllSingGuildRooms(long familyId, int pageNo, int pageSize) {
        ContractServiceProto.contractListReq req = ContractServiceProto.contractListReq.newBuilder()
                .setFamilyId(familyId)
                .addAllContractType(Arrays.asList(
                        ContractType.SIGN.getCode(),
                        ContractType.RENEW.getCode(),
                        ContractType.SUBJECT_CHANGE.getCode()
                ))
                .addStatus(FamilyConstant.ContractStatus.SIGN_SUCCEED.getCode())
                .setPageNum(pageNo)
                .setPageSize(pageSize)
                .build();
        Result<ContractServiceProto.ResponseContractListV2> result = contractService.contractListV2(req);
        if (RpcResult.isFail(result)) {
            log.warn("xm,getAllSingGuildRooms,familyId={},pageNo={},pageSize={},rCode={}", familyId, pageNo, pageSize, result.rCode());
            return PageBean.empty();
        }
        String contractsJsonArrayStr = result.target().getContracts();
        List<ContractInfo> contractInfos = JsonUtil.loadsArray(contractsJsonArrayStr, ContractInfo.class);
        if (CollectionUtils.isEmpty(contractInfos)) {
            return PageBean.empty();
        }
        List<RoomSignBean> roomSignDtos = UserConvert.I.contractPbs2RoomSignBeans(contractInfos);
        return PageBean.of(result.target().getTotal(), roomSignDtos);
    }

    @Override
    public PageBean<Long> getSingGuildRoomsByDate(long familyId, Date startDate, Date endDate, int pageNo, int pageSize) {
        PageList<Long> pageList = contractMapper.getSingGuildRoomsByDate(familyId,
                DateUtil.formatDateNormal(startDate),
                DateUtil.formatDateNormal(endDate),
                pageNo,
                pageSize);
        if (CollectionUtils.isEmpty(pageList)) {
            return PageBean.empty();
        }
        return PageBean.of(pageList.getTotal(), pageList);
    }

    @Override
    public List<RoomSignBean> getAllSingGuildRoomsList(long familyId) {
        XmContractExample example = new XmContractExample();
        example.createCriteria()
                .andFamilyIdEqualTo(familyId)
                .andTypeIn(Lists.newArrayList(ContractType.SIGN.getCode(), ContractType.RENEW.getCode(), ContractType.SUBJECT_CHANGE.getCode()))
                .andStatusEqualTo(FamilyConstant.ContractStatus.SIGN_SUCCEED.getCode())
        ;
        List<XmContract> poList = contractMapper.selectByExample(example);
        return UserConvert.I.xmContractPos2RoomSignBeans(poList);
    }

    public List<RoomSignBean> getAllSingGuildRoomsList(long familyId, List<Long> roomIds) {
        XmContractExample example = new XmContractExample();
        XmContractExample.Criteria criteria = example.createCriteria();
        criteria.andFamilyIdEqualTo(familyId)
                .andTypeIn(Lists.newArrayList(ContractType.SIGN.getCode(), ContractType.RENEW.getCode(), ContractType.SUBJECT_CHANGE.getCode()))
                .andStatusEqualTo(FamilyConstant.ContractStatus.SIGN_SUCCEED.getCode());

        if (CollectionUtils.isNotEmpty(roomIds)) {
            criteria.andNjIdIn(roomIds);
        }

        List<XmContract> poList = contractMapper.selectByExample(example);
        return UserConvert.I.xmContractPos2RoomSignBeans(poList);
    }

    @Override
    public PageBean<RoomSignBean> getAllGuildRooms(long familyId, List<Long> roomIds, int pageNo, int pageSize) {
        PageList<XmContract> pageList = contractMapper.pageFamilyNjBest(familyId, roomIds, pageNo, pageSize);
        if (CollectionUtils.isEmpty(pageList)) {
            return PageBean.empty();
        }

        List<RoomSignBean> roomSignDtos = UserConvert.I.xmContractPos2RoomSignBeans(pageList);
        return PageBean.of(pageList.getTotal(), roomSignDtos);
    }

    @Override
    public PageBean<PlayerSignBean> getAllRoomPlayers(long roomId, int pageNo, int pageSize) {
        PageList<XmPlayerSign> pageList = playerSignMapper.pagePlayerSignBest(Collections.singletonList(roomId), pageNo, pageSize);
        if (CollectionUtils.isEmpty(pageList)) {
            return PageBean.empty();
        }
        List<PlayerSignBean> beanList = UserConvert.I.xmPlayerSignPos2PlayerSignBeans(pageList);
        return PageBean.of(pageList.getTotal(), beanList);
    }

    @Override
    public List<Long> getAllSignRoomPlayerIds(long roomId) {
        return new ArrayList<>(playerSignMapper.selectAllSignPlayer(Collections.singletonList(roomId)));
    }

    @Override
    public Set<Long> getAllSignRoomPlayerIds(List<Long> roomIds) {
        if (CollectionUtils.isEmpty(roomIds)) {
            LogContext.addResLog("roomIds is empty");
            return Collections.emptySet();
        }
        return playerSignMapper.selectAllSignPlayer(roomIds);
    }

    @Override
    public PageDto<PlayerSignBean> getAllGuildPlayer(long familyId, List<Long> scopeRoomIds, int status, int pageNo, int pageSize) {
        List<RoomSignBean> roomsList = getAllSingGuildRoomsList(familyId, scopeRoomIds);
        if (CollectionUtils.isEmpty(roomsList)) {
            return PageDto.empty();
        }
        List<Long> roomIds = roomsList.stream().map(UserBean::getId).collect(Collectors.toList());
        PageList<XmPlayerSign> pageList = playerSignMapper.pagePlayerSignBest(roomIds, pageNo, pageSize);
        //过滤掉重复的陪玩：不分陪玩在同一个公会下，存在转移签约厅的情况，在公会视角下，只关心当前签约的状态
        List<Long> removeIds = new ArrayList<>();
        Map<Long, List<XmPlayerSign>> beanMap = pageList.stream().collect(Collectors.groupingBy(XmPlayerSign::getUserId));
        for (Map.Entry<Long, List<XmPlayerSign>> longListEntry : beanMap.entrySet()) {
            if (longListEntry.getValue().size() > 1) {
                longListEntry.getValue().sort(Comparator.comparing(XmPlayerSign::getCreateTime).reversed());
                for (int i = 0; i < longListEntry.getValue().size(); i++) {
                    if (i != 0) {
                        removeIds.add(longListEntry.getValue().get(i).getId());
                    }
                }
            }
        }

        List<XmPlayerSign> onlyList = pageList.stream().filter(v -> {
            return !removeIds.contains(v.getId());
        }).collect(Collectors.toList());

        List<PlayerSignBean> resultList = UserConvert.I.xmPlayerSignPos2PlayerSignBeans(onlyList);
        return PageDto.of(pageList.getTotal(), resultList);
    }

    @Override
    public Optional<RoomSignBean> getRoomSign(long familyId, long njId) {
        //查询签约信息，优先查询已签约记录，如果没有再查询未签约数据
        RoomSignBean bean = new RoomSignBean();
        bean.setId(njId);

        XmContractExample example = new XmContractExample();
        example.createCriteria()
                .andNjIdEqualTo(njId)
                .andFamilyIdEqualTo(familyId)
                .andTypeIn(Lists.newArrayList("SIGN", "RENEW", "SUBJECT_CHANGE"))
                .andStatusEqualTo("SIGN_SUCCEED");
        List<XmContract> xmContracts = contractMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(xmContracts)) {
            bean.setSignStatus(SingStatusEnum.STOP.getValue());
        } else {
            bean.setSignStatus(SingStatusEnum.SING.getValue());
        }

        return Optional.of(bean);
    }

    @Override
    public Optional<PlayerSignBean> getPlayerSign(Long familyId, Long roomId, long playerId) {
        PlayerSignBean playerSignBean = new PlayerSignBean();
        playerSignBean.setId(playerId);

        XmPlayerSignExample example = new XmPlayerSignExample();
        XmPlayerSignExample.Criteria criteria = example.createCriteria();
        criteria.andUserIdEqualTo(playerId)
                .andTypeEqualTo("SIGN")
                .andStatusEqualTo("SIGN_SUCCEED")
        ;

        List<XmPlayerSign> list = new ArrayList<>();
        if (roomId != null) {
            criteria.andNjIdEqualTo(roomId);
            list = playerSignMapper.selectByExample(example);
        } else if (familyId != null) {
            //查询公会下所有厅
            List<Long> familyAllNjId = getFamilyAllNjId(familyId);
            if (CollectionUtils.isNotEmpty(familyAllNjId)) {
                criteria.andNjIdIn(familyAllNjId);
                list = playerSignMapper.selectByExample(example);
            }
        }

        if (CollectionUtils.isEmpty(list)) {
            playerSignBean.setSignStatus(SingStatusEnum.STOP.getValue());
        } else {
            playerSignBean.setSignStatus(SingStatusEnum.SING.getValue());
        }
        return Optional.of(playerSignBean);
    }

    /**
     * 查询公会下所有厅
     *
     * @param familyId
     * @return
     */
    public List<Long> getFamilyAllNjId(Long familyId) {
        if (familyId == null) {
            return Collections.emptyList();
        }
        XmContract param = new XmContract();
        param.setFamilyId(familyId);
        List<XmContract> list = contractMapper.selectMany(param);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        return list.stream().map(XmContract::getNjId).collect(Collectors.toList());
    }

    @Override
    public List<PlayerIncomeStatRes> queryAdminPlayerIncomeList(long userId, Date startDate, Date endDate) {
        return Collections.EMPTY_LIST;
    }

    @Override
    public Optional<PlayerSignInfoDto> getLatestSignRecord(long roomId, long userId) {
        return Optional.empty();
    }

    @Override
    public Optional<PlayerSignInfoDto> getLatestSignRecord(List<Long> roomIds, long userId) {
        XmPlayerSign latestSignRecord = playerSignMapper.getLatestSignRecord(roomIds, userId);
        return Optional.ofNullable(UserConvert.I.xmPlayerSign2PlayerSignInfoDto(latestSignRecord));
    }

    @Override
    public Optional<Long> getRoomBestFamily(long roomId) {
        XmContractExample example = new XmContractExample();
        example.createCriteria().andNjIdEqualTo(roomId);
        example.setOrderByClause("create_time desc");
        List<XmContract> xmContracts = contractMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(xmContracts)) {
            log.warn("getRoomBestFamily ppContracts empty roomId={}", roomId);
            return Optional.empty();
        }
        return Optional.ofNullable(xmContracts.get(0).getFamilyId());
    }

    @Override
    public Optional<Long> getPlayerBestFamily(long playerId) {
        Optional<Long> njIdOp = getUserBestNj(playerId);
        if (!njIdOp.isPresent()) {
            log.warn("getPlayerBestFamily sign empty playerId={}", playerId);
            return Optional.empty();
        }

        Long njId = njIdOp.get();
        return getRoomBestFamily(njId);
    }

    @Override
    public Optional<Long> getUserBestNj(long userId) {
        //优先查询签约
        XmPlayerSignExample example = new XmPlayerSignExample();
        example.createCriteria()
                .andTypeEqualTo("SIGN")
                .andStatusEqualTo("SIGN_SUCCEED")
                .andUserIdEqualTo(userId);
        example.setOrderByClause("create_time desc");
        List<XmPlayerSign> signList = playerSignMapper.selectByExample(example);
        if (CollectionUtils.isNotEmpty(signList)) {
            return Optional.ofNullable(signList.get(0).getNjId());
        }

        XmPlayerSignExample example2 = new XmPlayerSignExample();
        example2.createCriteria()
                .andUserIdEqualTo(userId);
        example2.setOrderByClause("create_time desc");
        List<XmPlayerSign> signList2 = playerSignMapper.selectByExample(example2);
        if (CollectionUtils.isNotEmpty(signList2)) {
            return Optional.ofNullable(signList2.get(0).getNjId());
        }
        return Optional.empty();
    }

    @Override
    public Long getPlayerLastRoom(long familyId, long playerId) {
        List<Long> njIds = getFamilyAllNjId(familyId);
        if (CollectionUtils.isEmpty(njIds)) {
            return null;
        }

        //优先查询已签约
        XmPlayerSignExample example = new XmPlayerSignExample();
        example.createCriteria()
                .andTypeEqualTo("SIGN")
                .andStatusEqualTo("SIGN_SUCCEED")
                .andNjIdIn(njIds)
                .andUserIdEqualTo(playerId);
        example.setOrderByClause("create_time desc");
        List<XmPlayerSign> signList = playerSignMapper.selectByExample(example);
        if (CollectionUtils.isNotEmpty(signList)) {
            log.info("has sign");
            return signList.get(0).getNjId();
        }

        //没有已签约就返回最近的旧签约
        XmPlayerSignExample example2 = new XmPlayerSignExample();
        example2.createCriteria()
                .andNjIdIn(njIds)
                .andUserIdEqualTo(playerId);
        example2.setOrderByClause("create_time desc");
        List<XmPlayerSign> noSignList = playerSignMapper.selectByExample(example2);
        if (CollectionUtils.isNotEmpty(noSignList)) {
            log.info("has no sign");
            return noSignList.get(0).getNjId();
        }
        return null;
    }

    @Override
    public List<Long> getOfficialRoomIds() {
        Long officialRoomGroupId = userConfig.getBizConfig().getOfficialRoomGroupId();
        if (officialRoomGroupId == null) {
            return Collections.emptyList();
        }

        Result<UserGroupProto.ResponseGetGroupUserIds> result = userGroupService.getGroupUserIds(officialRoomGroupId);
        if (RpcResult.isFail(result)) {
            log.warn("xm getOfficialRoomIds fail. rCode={}", result.rCode());
            return Collections.emptyList();
        }
        return result.target().getUserIdList();
    }

    @Override
    public Optional<Long> getRoomBestFamilyByCache(long roomId) {
        return FAMILY_ID_CACHE.getUnchecked(roomId);
    }

    @Override
    public Optional<Long> getRoomSignFamilyInDate(long roomId, Date date) {
        List<Long> list = contractMapper.getRoomSignFamilyInDate(roomId, DateUtil.formatDateNormal(date));
        if (CollectionUtils.isEmpty(list)) {
            return Optional.empty();
        }
        return Optional.ofNullable(list.get(0));
    }

    @Override
    public Optional<FamilyBean> getFamily(long familyId) {

        return Optional.empty();
    }

    @Override
    public PageBean<RoomSignInfoBean> guildSignRoomPageList(SMSignRoomPageListReqDto reqDto) {
        PageList<XmContract> pageList = contractMapper.guildSignRoomPageList(reqDto, reqDto.getPageNo(), reqDto.getPageSize());
        List<RoomSignInfoBean> beanList = SignInfraConvert.I.xmContracts2Beans(pageList);
        return PageBean.of(pageList.getTotal(), beanList);
    }

    @Override
    public PageBean<SignPlayerInfoBean> signPlayerPageList(SMSignPlayerPageListReqDto reqDto) {
        //结算比例参数
        if (reqDto.getSettleMax() != null || reqDto.getSettleMin() != null) {
            List<Long> filterRoomIds = familyNjSettleRatioMapper.selectSettleScopeNjIds(reqDto.getFamilyId(), reqDto.getSettleMax(), reqDto.getSettleMin());
            if (CollectionUtils.isEmpty(filterRoomIds)) {
                LogContext.addResLog("filterRoomIds is empty");
                return PageBean.empty();
            }
            reqDto.setRoomIds(filterRoomIds);
        }

        PageList<XmPlayerSign> poList = playerSignMapper.signPlayerPageList(reqDto, reqDto.getPageNo(), reqDto.getPageSize());
        if (CollectionUtils.isEmpty(poList)) {
            return PageBean.empty();
        }

        List<SignPlayerInfoBean> beanList = SignInfraConvert.I.xmPlayerSigns2Beans(poList);
        Set<Long> njIds = poList.stream().map(XmPlayerSign::getNjId).collect(Collectors.toSet());

        List<XmFamilyNjSettleRatioPo> incomeList = familyNjSettleRatioMapper.selectBest(reqDto.getFamilyId(), new ArrayList<>(njIds));
        Map<Long, Integer> incomeMap = incomeList.stream().collect(Collectors.toMap(XmFamilyNjSettleRatioPo::getNjId, XmFamilyNjSettleRatioPo::getPercentage, (k1, k2) -> k2));
        for (SignPlayerInfoBean bean : beanList) {
            UserBean roomInfo = bean.getRoomInfo();
            if (roomInfo == null) {
                continue;
            }

            bean.setSettle(incomeMap.get(roomInfo.getId()));
        }

        return PageBean.of(poList.getTotal(), beanList);
    }

    @Override
    public List<Long> selectAllSignPlayerByDate(List<Long> njIds, Date startDate, Date endDate) {
        String startStr = DateUtil.formatDateNormal(startDate);
        String endStr = DateUtil.formatDateNormal(endDate);
        return playerSignMapper.selectAllSignPlayerByDate(njIds, startStr, endStr);
    }

    @Override
    public Optional<FamilyAndNjContractBean> queryLastCancel(Long userId) {
        Result<UnwindContractServiceProto.ResponseApplyUnwindList> result = unwindContractService.applyUnwindList(userId, 0, "", 1, 1, "");
        if (RpcResult.isFail(result)) {
            log.error("xm applyUnwindList fail. userId={},rCode={}", userId, result.rCode());
            return Optional.empty();
        }

        List<UnwindContractInfo> unwindContracts = JSONObject.parseArray(result.target().getUnWinds(),
                UnwindContractInfo.class);
        if (CollectionUtils.isEmpty(unwindContracts)) {
            return Optional.empty();
        }

        UnwindContractInfo unwindContract = unwindContracts.get(0);
        FamilyAndNjContractBean contractBean = new FamilyAndNjContractBean();
        contractBean.setId(unwindContract.getId());
        contractBean.setContractId(unwindContract.getUnwindContract().getId());
        contractBean.setSignId(unwindContract.getUnwindContract().getSignId());
        contractBean.setType(ContractTypeEnum.CANCEL.getCode());
        contractBean.setNjUserId(unwindContract.getNjId());
        contractBean.setFamilyId(unwindContract.getFamilyId());
        //1管理员3家族长
        contractBean.setCreateUser(Objects.equals(unwindContract.getApplyUserType(), 1) ? RoleEnum.ROOM.getRoleCode() : RoleEnum.FAMILY.getRoleCode());

        contractBean.setStatus(
                SignInfraConvert.I.xmSignStatusTrans(unwindContract.getUnwindContract().getStatus())
        );

        return Optional.of(contractBean);
    }

    @Override
    public Optional<FamilyAndNjContractBean> queryLastSign(Long userId) {
        XmContractExample example = new XmContractExample();
        example.setOrderByClause("create_time desc");
        XmContractExample.Criteria criteria = example.createCriteria();
        criteria.andNjIdEqualTo(userId);
        criteria.andTypeEqualTo(ContractTypeEnum.SIGN.getCode());

        PageList<XmContract> xmContracts = contractMapper.pageByExample(example, 1, 1);
        if (CollectionUtils.isEmpty(xmContracts)) {
            return Optional.empty();
        }

        XmContract contract = xmContracts.get(0);
        FamilyAndNjContractBean contractBean = new FamilyAndNjContractBean();
        contractBean.setContractId(contract.getId());
        contractBean.setSignId(contract.getSignId());
        contractBean.setType(ContractTypeEnum.SIGN.getCode());
        contractBean.setNjUserId(contract.getNjId());
        contractBean.setFamilyId(contract.getFamilyId());
        contractBean.setCreateUser("ADMIN".equals(contract.getUserType()) ? RoleEnum.ROOM.getRoleCode() : RoleEnum.FAMILY.getRoleCode());

        contractBean.setStatus(
                SignInfraConvert.I.xmSignStatusTrans(contract.getStatus())
        );

        return Optional.of(contractBean);
    }

    @Override
    public boolean isUserSignAsRoom(Long userId) {
        return false;
    }

    @Override
    public PageBean<FamilyAndNjContractBean> queryContract(RequestFamilyAndNjContractDTO request) {
        XmContractExample example = new XmContractExample();
        if (request.isDescCreateTime()) {
            example.setOrderByClause("create_time desc");
        }
        XmContractExample.Criteria criteria = example.createCriteria();
        if (request.isNoExpire()) {
            Date now = new Date();
            criteria.andBeginTimeLessThanOrEqualTo(now)
                    .andExpireTimeGreaterThan(now);
        }
        if (CollectionUtils.isNotEmpty(request.getTypes())) {
            criteria.andTypeIn(request.getTypes().stream().map(ContractTypeEnum::getCode).collect(Collectors.toList()));
        }
        if (CollectionUtils.isNotEmpty(request.getRelations())) {
            criteria.andStatusIn(request.getRelations()
                    .stream().map(SignInfraConvert.I::waveSignStatus2xm)
                    .collect(Collectors.toList()));
        }
        if (request.getFamilyId() != null) {
            criteria.andFamilyIdEqualTo(request.getFamilyId());
        }
        if (request.getNjId() != null) {
            criteria.andNjIdEqualTo(request.getNjId());
        }
        if (request.getContractId() != null) {
            criteria.andIdEqualTo(request.getContractId());
        }
        if (StringUtils.isNotBlank(request.getSignId())) {
            criteria.andSignIdEqualTo(request.getSignId());
        }
        if (CollectionUtils.isNotEmpty(request.getContractLists())) {
            criteria.andIdIn(request.getContractLists());
        }
        if (request.getOtherFamilyId() != null) {
            criteria.andFamilyIdNotEqualTo(request.getOtherFamilyId());
        }

        PageList<XmContract> pageList = contractMapper.pageByExample(example, request.getPageNo(), request.getPageSize());
        List<FamilyAndNjContractBean> beanList = SignInfraConvert.I.xmContracts2ContractBeans(pageList);
        return PageBean.of(pageList.getTotal(), beanList);
    }

    @Override
    public PageBean<FamilyAndNjContractBean> queryCancelApply(RequestFamilyAndNjCancelApply request) {
        XmUnwindApplyExample example = new XmUnwindApplyExample();
        example.setOrderByClause("create_time desc");
        XmUnwindApplyExample.Criteria criteria = example.createCriteria();
        if (request.getFamilyId() != null) {
            criteria.andFamilyIdEqualTo(request.getFamilyId());
        }
        if (CollectionUtils.isNotEmpty(request.getAudits())) {
            criteria.andAuditStatusIn(request.getAudits().stream().map(AuditStatusEnum::getCode).collect(Collectors.toList()));
        }
        if (CollectionUtils.isNotEmpty(request.getRelations())) {
            criteria.andStatusIn(request.getRelations()
                    .stream().map(SignInfraConvert.I::waveSignStatus2xm)
                    .collect(Collectors.toList()));
        }
        if (request.getNjId() != null) {
            criteria.andNjIdEqualTo(request.getNjId());
        }

        PageList<XmUnwindApply> pageList = unwindApplyMapper.pageByExample(example, request.getPageNo(), request.getPageSize());
        if (CollectionUtils.isEmpty(pageList)) {
            return PageBean.empty();
        }

        //查询解约电子合同，补充时间信息
        Map<Long, XmContract> contractMap = new HashMap<>();
        List<Long> unwindContractIds = pageList.stream().filter(v -> v.getUnwindContractId() != null && v.getUnwindContractId() > 0).map(XmUnwindApply::getUnwindContractId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(unwindContractIds)) {
            XmContractExample contractExample = new XmContractExample();
            contractExample.createCriteria().andIdIn(unwindContractIds);
            List<XmContract> contracts = contractMapper.selectByExample(contractExample);
            contractMap = contracts.stream().collect(Collectors.toMap(XmContract::getId, resource -> resource, (existing, replacement) -> replacement));
        }

        List<FamilyAndNjContractBean> list = new ArrayList<>();
        for (XmUnwindApply xmUnwindApply : pageList) {
            FamilyAndNjContractBean bean = SignInfraConvert.I.xmUnwindApply2ContractBean(xmUnwindApply);
            if (AuditStatusEnum.PENDING.getCode().equals(xmUnwindApply.getAuditStatus())) {
                bean.setStatus(SignRelationEnum.WAIT_AUDIT.getCode());
            }
            if (AuditStatusEnum.REFUSED.getCode().equals(xmUnwindApply.getAuditStatus())) {
                bean.setStatus(SignRelationEnum.AUDIT_FAIL.getCode());
            }
            bean.setType(ContractTypeEnum.CANCEL.getCode());

            if (contractMap.containsKey(xmUnwindApply.getUnwindContractId())) {
                XmContract xmContract = contractMap.get(xmUnwindApply.getUnwindContractId());
                bean.setBeginTime(xmContract.getBeginTime());
                bean.setExpireTime(xmContract.getExpireTime());
                bean.setSignDeadline(xmContract.getSignDeadline());
                bean.setSignFinishTime(xmContract.getSignFinishTime());
            }

            list.add(bean);
        }

        return PageBean.of(pageList.getTotal(), list);
    }

    @Override
    public List<Long> queryAllSignNjId(Long familyId) {
        XmFamilyNjExample example = new XmFamilyNjExample();
        example.createCriteria()
                .andFamilyIdEqualTo(familyId)
                .andStatusEqualTo("JOIN");

        List<XmFamilyNj> list = familyNjMapper.selectByExample(example);
        if(CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }

        return list.stream().map(XmFamilyNj::getNjId).collect(Collectors.toList());
    }

    @Override
    public Integer countSignRoomNum(long familyId) {
        XmContractExample example = new XmContractExample();
        example.createCriteria()
                .andFamilyIdEqualTo(familyId)
                .andTypeIn(Lists.newArrayList(ContractTypeEnum.SIGN.getCode()
                        , ContractTypeEnum.SUBJECT_CHANGE.getCode()
                        , ContractTypeEnum.RENEW.getCode()
                ))
                .andStatusEqualTo("SIGN_SUCCEED");

        return Math.toIntExact(contractMapper.countByExample(example));
    }

    @Override
    public ResponseUserApplyAdmin userApplyAdmin(RequestUserApplyAdmin request) {
        ResponseUserApplyAdmin res = new ResponseUserApplyAdmin().setCode(0);

        JoinFamilyParam joinFamilyParam = new JoinFamilyParam();
        joinFamilyParam.setTargetId(request.getTargetUserId());
        joinFamilyParam.setUserId(request.getCurUserId());
        Result<SignServiceProto.ResponseJoinFamily> result = signService.joinFamily(JSONObject.toJSONString(joinFamilyParam));
        if (RpcResult.isFail(result)) {
            log.error("xm joinFamily fail. targetId={},userId={},rCode={}", request.getTargetUserId(), request.getCurUserId(), result.rCode());
            return res.setCode(-1);
        }
        int code = result.target().getCode();
        if (code != 0) {
            return res.setCode(code).setMsg(result.target().getMsg());
        }

        PageBean<FamilyAndNjContractBean> pageBean = queryContract(RequestFamilyAndNjContractDTO.builder()
                .type(ContractTypeEnum.SIGN)
                .relation(SignRelationEnum.WAIT_SIGN)
                .njId(request.getCurUserId())
                .familyId(request.getFamilyId())
                .build());
        if (CollectionUtils.isNotEmpty(pageBean.getList())) {
            res.setContractId(pageBean.getList().get(0).getContractId());
        }

        return res.setSignId(result.target().getSignId());
    }

    @Override
    public List<FamilyAndNjContractBean> queryIdentityNoJoinFamily(String identityNo) {
        return Collections.emptyList();
    }

    @Override
    public ResponseFamilyInviteAdmin familyInviteAdmin(RequestFamilyInviteAdmin request) {
        return null;
    }

    @Override
    public List<FamilyNjJoinRecordDTO> queryFamilyNjJoinRecord(QueryFamilyNjJoinRecordDTO request) {
        return Collections.emptyList();
    }

    @Override
    public Optional<String> doSignGenSignId(Long contractId) {
        return Optional.empty();
    }

    @Override
    public Optional<String> familyAdminCancel(RequestFamilyAdminCancel request) {
        Result<UnwindContractServiceProto.ResponseApplyUnwind> result = unwindContractService.applyUnwind(request.getCurUserId(), request.getContractId());
        if (RpcResult.isFail(result)) {
            log.error("xm applyUnwind curUserId={},contractId={},rCode={}", request.getCurUserId(), request.getContractId(), result.rCode());
            return Optional.empty();
        }
        return Optional.ofNullable(result.target().getUnwindSignId());
    }

    @Override
    public ResponseAdminApplyCancelFamily adminApplyCancelFamily(RequestAdminApplyCancelFamily request) {
        return null;
    }

    @Override
    public Pair<Integer, String> familyAdminInviteConfirm(Long curUserId, Long targetUserId, RoleEnum operateRole) {
        return null;
    }

    @Override
    public Pair<Integer, String> familyAdminInviteCancelConfirm(Long familySignId, RoleEnum operateRole, Long curUserId) {
        return null;
    }

    @Override
    public PageBean<FamilyNjSignRecordDTO> querySignRecord(QueryFamilyNjSignRecordDTO param) {
        return PageBean.empty();
    }

    @Override
    public Pair<Integer, String> doFamilyNjConfirmSign(Long familyNjSignId, Long curUserId, ContractTypeEnum type, RoleEnum operateRole) {
        return null;
    }

    @Override
    public List<FamilyNjCancelRecordDTO> queryCancelRecordByContractId(List<Long> contractIds) {
        if (CollectionUtils.isEmpty(contractIds)) {
            return Collections.emptyList();
        }

        XmUnwindApplyExample example = new XmUnwindApplyExample();
        example.createCriteria()
                .andUnwindContractIdIn(contractIds);

        List<XmUnwindApply> list = unwindApplyMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }

        return list.stream().map(v->new FamilyNjCancelRecordDTO()
                .setCancelContractId(v.getUnwindContractId())
                .setOldContractId(v.getContractId())
        ).collect(Collectors.toList());
    }

    @Override
    public List<ContractInfoDto> queryContractInfoByTime(long familyId, Date startDate, Date endDate) {
        ContractServiceProto.GetContractListByTimeReq.Builder req = ContractServiceProto.GetContractListByTimeReq.newBuilder();
        req.setUserId(0L);
        req.setFamilyId(familyId);
        req.setStartTimeLte(startDate.getTime());
        req.setEndTimeGte(endDate.getTime());

        Result<ContractServiceProto.ResponseGetContractListByTime> res = contractService.getContractListByTime(req.build());
        if (RpcResult.isFail(res)) {
            log.error("xm getContractListByTime fail. familyId={},startDate={},endDate={},rCode={}", familyId, startDate, endDate, res.rCode());
            return Collections.emptyList();
        }

        return JSONObject.parseArray(res.target().getContracts(), ContractInfoDto.class);
    }
}
