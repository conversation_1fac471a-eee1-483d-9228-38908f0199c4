package fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.job;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import fm.lizhi.common.job.dispatcher.executor.handler.JobExecuteContext;
import fm.lizhi.common.job.dispatcher.executor.handler.JobHandler;
import fm.lizhi.ocean.wavecenter.service.activitycenter.manager.RoomAnnouncementDeployManager;
import lombok.extern.slf4j.Slf4j;

/**
 * 房间公告设置定时任务
 */
@Slf4j
@Component
public class RoomAnnouncementRecoveryJob implements JobHandler {

    @Autowired
    private RoomAnnouncementDeployManager roomAnnouncementDeployManager;

    @Override
    public void execute(JobExecuteContext context) throws Exception {
        try {
            roomAnnouncementDeployManager.recoveryAnnouncement();
        } catch (Exception e) {
            log.error("房间公告恢复定时任务执行异常:", e);
        }
    }
}
