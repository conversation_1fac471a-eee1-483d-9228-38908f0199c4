package fm.lizhi.ocean.wavecenter.infrastructure.award.family.job;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.Week;
import fm.lizhi.common.job.dispatcher.executor.handler.JobExecuteContext;
import fm.lizhi.common.job.dispatcher.executor.handler.JobHandler;
import fm.lizhi.ocean.wavecenter.base.util.JsonUtils;
import fm.lizhi.ocean.wavecenter.common.utils.DateTimeUtils;
import fm.lizhi.ocean.wavecenter.infrastructure.award.family.manager.deliver.FamilyLevelAwardDeliverManager;
import fm.lizhi.ocean.wavecenter.service.award.family.config.FamilyAwardConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedHashSet;
import java.util.List;

/**
 * 公会等级奖励发放任务
 */
@Component
@Slf4j
public class FamilyLevelAwardDeliverJob implements JobHandler {

    @Autowired
    private FamilyAwardConfig familyAwardConfig;

    @Autowired
    private FamilyLevelAwardDeliverManager familyLevelAwardDeliverManager;

    @Override
    public void execute(JobExecuteContext context) throws Exception {
        FamilyLevelAwardDeliverJobParam param = parseParam(context);
        List<Integer> appIds = getAppIds(param);
        Date awardStartTime = getAwardStartTime(param);

        if (param == null || param.getIgnoreDayCheck() == null) {
            // 线上空参数执行，必须保证当前是周一，否则收入数据结算会缺失
            Week week = DateUtil.thisDayOfWeekEnum();
            if (week != Week.MONDAY) {
                log.info("not MONDAY. week={}", week);
                return;
            }
        }

        log.info("FamilyLevelAwardDeliverJob start, appIds: {}, awardStartTime: {}", appIds, awardStartTime);
        familyLevelAwardDeliverManager.deliverAward(appIds, awardStartTime);
        log.info("FamilyLevelAwardDeliverJob end");
    }

    private FamilyLevelAwardDeliverJobParam parseParam(JobExecuteContext context) {
        if (StringUtils.isBlank(context.getParam())) {
            return null;
        }
        try {
            return JsonUtils.fromJsonString(context.getParam(), FamilyLevelAwardDeliverJobParam.class);
        } catch (RuntimeException e) {
            log.info("Failed to parse param: {}", context.getParam(), e);
            return null;
        }
    }

    /**
     * 获取应用id列表, 如果参数中没有指定则返回默认的值: 陪伴和西米
     *
     * @param param 参数
     * @return 应用id列表
     */
    private List<Integer> getAppIds(FamilyLevelAwardDeliverJobParam param) {
        if (param != null && param.getAppIds() != null) {
            return new ArrayList<>(new LinkedHashSet<>(param.getAppIds()));
        }
        return new ArrayList<>(familyAwardConfig.getLevelAwardJobAppIds());
    }

    /**
     * 获取奖励周期开始, 如果参数中没有指定则返回上一周周一的开始时间
     *
     * @param param 参数
     * @return 奖励开始时间
     */
    private Date getAwardStartTime(FamilyLevelAwardDeliverJobParam param) {
        if (param != null && param.getAwardStartTime() != null) {
            return param.getAwardStartTime();
        }
        return DateTimeUtils.getLastWeekMondayStartTime();
    }
}
