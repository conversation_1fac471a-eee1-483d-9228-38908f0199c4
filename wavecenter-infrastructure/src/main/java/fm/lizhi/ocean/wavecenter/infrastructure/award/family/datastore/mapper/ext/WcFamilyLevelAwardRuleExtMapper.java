package fm.lizhi.ocean.wavecenter.infrastructure.award.family.datastore.mapper.ext;

import fm.lizhi.common.datastore.core.annotation.DataStore;
import fm.lizhi.ocean.wavecenter.infrastructure.award.family.datastore.entity.WcFamilyLevelAwardRule;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

@DataStore(namespace = "mysql_ocean_wavecenter")
public interface WcFamilyLevelAwardRuleExtMapper {

    @Update("UPDATE `wavecenter_family_level_award_rule`\n" +
            "SET `deleted` = 1, `delete_time` = NOW(), `modify_time` = NOW(), `modifier` = #{operator}\n" +
            "WHERE `id` = #{id} AND `deleted` = 0")
    int deleteById(@Param("id") long id, @Param("operator") String operator);

    @Select("SELECT * FROM `wavecenter_family_level_award_rule`\n" +
            "WHERE `app_id` = #{appId} AND `deploy_env` = #{deployEnv} AND `deleted` = 0\n" +
            "ORDER BY `create_time` DESC")
    List<WcFamilyLevelAwardRule> getRulesByAppId(@Param("appId") int appId, @Param("deployEnv") String deployEnv);
}
