package fm.lizhi.ocean.wavecenter.infrastructure.anchor.singer.processor.hy;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.google.common.collect.Lists;

import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerHallApplyStatusEnum;
import fm.lizhi.ocean.wavecenter.infrastructure.anchor.singer.datastore.dao.SingerHallApplyDao;
import fm.lizhi.ocean.wavecenter.infrastructure.anchor.singer.datastore.entity.SingerSingHallApplyRecord;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.processor.ISingerHallApplyProcessor;

@Component
public class HySingerHallApplyProcessor implements ISingerHallApplyProcessor {

    @Autowired
    private SingerHallApplyDao singerHallApplyDao;

    @Override
    public boolean isInSingingHall(Integer appId, Long njId) {
        SingerSingHallApplyRecord singerHallApplyRecord = singerHallApplyDao.getSingerHallApplyValidRecord(appId, njId);
        return singerHallApplyRecord != null;
    }

    @Override
    public BusinessEvnEnum getBusinessEnv() {
        return BusinessEvnEnum.HEI_YE;
    }

    @Override
    public Map<Long, Integer> batchGetSingerHallStatusMap(int appId, List<Long> ids) {
        List<Integer> auditStatusList = Lists.newArrayList(SingerHallApplyStatusEnum.APPLYED.getStatus(), SingerHallApplyStatusEnum.APPLYING.getStatus());
        List<SingerSingHallApplyRecord> recordList = singerHallApplyDao.batchGetSingerHallApplyRecordList(appId, ids, auditStatusList);
        if (CollectionUtils.isEmpty(recordList)) {
            return new HashMap<>();
        }
        //转成map, key为njId, value为auditStatus
        return recordList.stream().collect(Collectors.toMap(SingerSingHallApplyRecord::getNjId, SingerSingHallApplyRecord::getAuditStatus));
    }
}
