package fm.lizhi.ocean.wavecenter.infrastructure.user.remote.pp;

import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.infrastructure.user.remote.IPlayerAuthServiceRemote;
import fm.lizhi.ocean.wavecenter.infrastructure.user.remote.res.GetUserPassTypeRes;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import pp.fm.lizhi.live.pp.player.api.PpPlayerAuthService;
import pp.fm.lizhi.live.pp.player.protocol.PpPlayerAuthProto;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/4/12 20:14
 */
@Component
public class PpPlayerAuthServiceRemote implements IPlayerAuthServiceRemote {

    @Autowired
    private PpPlayerAuthService ppPlayerAuthService;

    @Override
    public BusinessEvnEnum getBusinessEvnEnum() {
        return BusinessEvnEnum.PP;
    }

    @Override
    public Optional<GetUserPassTypeRes> getUserPassType(Long userId) {
        Result<PpPlayerAuthProto.ResponseGetUserPassType> result = ppPlayerAuthService.getUserPassType(userId);
        if (RpcResult.isFail(result)) {
            LogContext.addResLog("getUserPassType rCode={}", result.rCode());
            return Optional.empty();
        }

        return Optional.of(GetUserPassTypeRes.builder()
                .passPlayerSkill(result.target().getPassPlayerSkill())
                .passRoomPlayer(result.target().getPassRoomPlayer())
                .passRoomHost(result.target().getPassRoomHost())
                .build());
    }
}
