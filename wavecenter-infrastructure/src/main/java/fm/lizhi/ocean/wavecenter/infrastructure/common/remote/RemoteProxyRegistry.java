package fm.lizhi.ocean.wavecenter.infrastructure.common.remote;

import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.beans.factory.config.ConfigurableListableBeanFactory;
import org.springframework.beans.factory.support.BeanDefinitionRegistry;
import org.springframework.beans.factory.support.BeanDefinitionRegistryPostProcessor;
import org.springframework.beans.factory.support.GenericBeanDefinition;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.lang.reflect.Proxy;

/**
 * <AUTHOR>
 * @date 2024/3/28 14:06
 */
@Component
public class RemoteProxyRegistry implements BeanDefinitionRegistryPostProcessor, ApplicationContextAware {

    private ApplicationContext applicationContext;

    public static final String BEAN_NAME_PREFIX = "REMOTEPROXY_";

    @Override
    public void postProcessBeanDefinitionRegistry(BeanDefinitionRegistry registry) throws BeansException {
        for (String beanDefinitionName : registry.getBeanDefinitionNames()) {
            BeanDefinition beanDefinition = registry.getBeanDefinition(beanDefinitionName);
            String beanClassName = beanDefinition.getBeanClassName();
            if (beanClassName == null) {
                continue;
            }
            try {
                Class<?> beanClass = Class.forName(beanClassName);
                Class<?>[] interfaces = beanClass.getInterfaces();
                if (interfaces.length == 0) {
                    continue;
                }
                Class<?> subRemoteInterface = null;
                for (Class<?> subRemote : interfaces) {
                    if (IRemote.class.isAssignableFrom(subRemote)) {
                        subRemoteInterface = subRemote;
                    }
                }
                if (subRemoteInterface == null) {
                    continue;
                }

                String beanName = buildBeanName(subRemoteInterface);
                if (registry.containsBeanDefinition(beanName)) {
                    continue;
                }

                GenericBeanDefinition genericBeanDefinition = new GenericBeanDefinition();
                genericBeanDefinition.setPrimary(true);
                genericBeanDefinition.setBeanClass(subRemoteInterface);
                Class<?> finalSubRemoteInterface = subRemoteInterface;
                genericBeanDefinition.setInstanceSupplier(()->{
                    RemoteProxy remoteProxy = new RemoteProxy(finalSubRemoteInterface, applicationContext);
                    return Proxy.newProxyInstance(
                            finalSubRemoteInterface.getClassLoader(),
                            new Class<?>[]{finalSubRemoteInterface},
                            remoteProxy
                    );
                });

                registry.registerBeanDefinition(beanName, genericBeanDefinition);

            } catch (ClassNotFoundException e) {
                throw new RuntimeException("remote class not found" + beanClassName);
            }
        }
    }

    private String buildBeanName(Class<?> finalSubRemoteInterface){
        return BEAN_NAME_PREFIX + finalSubRemoteInterface.getName();
    }

    @Override
    public void postProcessBeanFactory(ConfigurableListableBeanFactory beanFactory) throws BeansException {

    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }
}
