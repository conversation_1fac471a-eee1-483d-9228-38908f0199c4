package fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.mapper;

import fm.lizhi.common.datastore.core.annotation.DataStore;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.entity.CountPlayerEntity;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.po.CountPlayerPo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 */
@DataStore(namespace = "mysql_ocean_wavecenter")
public interface WcDataPlayerRoomDayExtMapper {

    @Select({
            "<script>"
            , "select count(distinct player_id) from wavecenter_data_player_room_day"
            , "where app_id=#{param.appId} and family_id=#{param.familyId} and stat_date_value=#{dayValue}"
            , "<if test='null != param.roomIds and param.roomIds.size > 0'>"
            , "and room_id in "
            , "<foreach collection='param.roomIds' item='roomId' open='(' separator=',' close=')'>"
            , "#{roomId}"
            , "</foreach>"
            , "</if>"
            , "<if test='null != param.upGuestDurMin'>"
            , "and up_guest_dur &gt; #{param.upGuestDurMin}"
            , "</if>"
            , "<if test='null != param.charmMin'>"
            , "and charm &gt; #{param.charmMin}"
            , "</if>"
            , "</script>"
    })
    int countPlayer(@Param("param") CountPlayerEntity param, @Param("dayValue") int dayValue);

    @Select({
            "<script>"
            , "select stat_date_value,count(distinct player_id) as cnt from wavecenter_data_player_room_day"
            , "where app_id=#{param.appId} and family_id=#{param.familyId} and stat_date_value in "

            , "<foreach collection='dayValues' item='dayValue' open='(' separator=',' close=')'>"
            , "#{dayValue}"
            , "</foreach>"

            , "<if test='null != param.roomIds and param.roomIds.size > 0'>"
            , "and room_id in "
            , "<foreach collection='param.roomIds' item='roomId' open='(' separator=',' close=')'>"
            , "#{roomId}"
            , "</foreach>"
            , "</if>"

            , "<if test='null != param.upGuestDurMin'>"
            , "and up_guest_dur &gt; #{param.upGuestDurMin}"
            , "</if>"
            , "<if test='null != param.charmMin'>"
            , "and charm &gt; #{param.charmMin}"
            , "</if>"
            , "group by stat_date_value"
            , "</script>"
    })
    List<CountPlayerPo> countPlayerDays(@Param("param") CountPlayerEntity param, @Param("dayValues") List<Integer> dayValues);

}
