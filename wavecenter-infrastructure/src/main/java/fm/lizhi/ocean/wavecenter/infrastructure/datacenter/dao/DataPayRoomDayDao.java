package fm.lizhi.ocean.wavecenter.infrastructure.datacenter.dao;

import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.ocean.wavecenter.common.utils.MyDateUtil;
import fm.lizhi.ocean.wavecenter.infrastructure.common.constants.PaySettleConfigCodeEnum;
import fm.lizhi.ocean.wavecenter.infrastructure.common.util.ColumnUtils;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.entity.WcDataPayRoomDay;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.entity.WcDataPayRoomDayExample;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.mapper.WcDataPayRoomDayMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.mapper.ext.WcDataPayRoomDayExtMapper;
import fm.lizhi.ocean.wavecenter.service.common.manager.IdManager;
import fm.lizhi.ocean.wavecenter.service.datacenter.constants.AccountOpEnum;
import fm.lizhi.ocean.wavecenter.service.datacenter.dto.RoomFlowChangeDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2025/4/24 14:39
 */
@Slf4j
@Repository
public class DataPayRoomDayDao {

    @Autowired
    private WcDataPayRoomDayMapper wcDataPayRoomDayMapper;
    @Autowired
    private WcDataPayRoomDayExtMapper wcDataPayRoomDayExtMapper;
    @Autowired
    private IdManager idManager;

    /**
     * 规则和字段映射关系
     * key=规则名称 configCode
     * value=字段名称
     */
    private Map<String, String> configCodeColumnMap = new HashMap<>();

    public DataPayRoomDayDao() {
        configCodeColumnMap.put(PaySettleConfigCodeEnum.HALL_FEEDBACK_PERFORMANCE_TOTAL_AMOUNT.getConfigCode(), ColumnUtils.getColumnName(WcDataPayRoomDay.class, WcDataPayRoomDay.Fields.income));
        configCodeColumnMap.put(PaySettleConfigCodeEnum.HALL_INCOME_TOTAL_AMOUNT.getConfigCode(), ColumnUtils.getColumnName(WcDataPayRoomDay.class, WcDataPayRoomDay.Fields.allIncome));
        configCodeColumnMap.put(PaySettleConfigCodeEnum.HALL_HALL_INCOME_TOTAL_AMOUNT.getConfigCode(), ColumnUtils.getColumnName(WcDataPayRoomDay.class, WcDataPayRoomDay.Fields.signHallIncome));
        configCodeColumnMap.put(PaySettleConfigCodeEnum.HALL_OFFICIAL_HALL_INCOME_TOTAL_AMOUNT.getConfigCode(), ColumnUtils.getColumnName(WcDataPayRoomDay.class, WcDataPayRoomDay.Fields.officialHallIncome));
        configCodeColumnMap.put(PaySettleConfigCodeEnum.HALL_INDIVIDUAL_INCOME_TOTAL_AMOUNT.getConfigCode(), ColumnUtils.getColumnName(WcDataPayRoomDay.class, WcDataPayRoomDay.Fields.personalHallIncome));
        configCodeColumnMap.put(PaySettleConfigCodeEnum.HALL_NOBILITY_ROYALTY_INCOME_TOTAL_AMOUNT.getConfigCode(), ColumnUtils.getColumnName(WcDataPayRoomDay.class, WcDataPayRoomDay.Fields.nobleIncome));
        configCodeColumnMap.put(PaySettleConfigCodeEnum.HALL_NOBILITY_PERSONAL_ROYALTY_INCOME_TOTAL_AMOUNT.getConfigCode(), ColumnUtils.getColumnName(WcDataPayRoomDay.class, WcDataPayRoomDay.Fields.personalNobleIncome));
        configCodeColumnMap.put(PaySettleConfigCodeEnum.HALL_NOBILITY_HALL_ROYALTY_INCOME_TOTAL_AMOUNT.getConfigCode(), ColumnUtils.getColumnName(WcDataPayRoomDay.class, WcDataPayRoomDay.Fields.roomNobleIncome));
    }

    public boolean isRoomDayFlowExist(Integer appId, Long roomId, Integer statDateValue){
        WcDataPayRoomDayExample example = new WcDataPayRoomDayExample();
        example.createCriteria()
                .andRoomIdEqualTo(roomId)
                .andStatDateValueEqualTo(statDateValue)
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name())
                .andAppIdEqualTo(appId);
        return wcDataPayRoomDayMapper.countByExample(example) > 0;
    }

    /**
     * 保存厅日流水变更
     * @param dto
     * @param familyId
     * @return
     */
    public int addRoomDayFlow(RoomFlowChangeDTO dto, Long familyId){
        if (CollectionUtils.isEmpty(dto.getRuleCodes())) {
            return 0;
        }

        Integer statDateValue = MyDateUtil.getDateDayValue(dto.getTradeDate());

        WcDataPayRoomDay entity = new WcDataPayRoomDay();
        entity.setId(idManager.genId());
        entity.setAppId(dto.getAppId());
        entity.setStatDate(dto.getTradeDate());
        entity.setStatDateValue(statDateValue);
        entity.setRoomId(dto.getRoomId());
        entity.setFamilyId(familyId);
        entity.setCreateTime(new Date());
        entity.setModifyTime(new Date());
        entity.setDeployEnv(ConfigUtils.getEnvRequired().name());

        List<String> columns = new ArrayList<>();
        for (String ruleCode : dto.getRuleCodes()) {
            String column = configCodeColumnMap.get(ruleCode);
            if (column != null) {
                columns.add(column);
            }
        }
        log.info("columns={}", JsonUtil.dumps(columns));

        if (CollectionUtils.isEmpty(columns)) {
            log.warn("columns is empty");
            return 0;
        }

        String amount = dto.getAccountOpType() == AccountOpEnum.MINUS
                ? "-"+dto.getAmount()
                : dto.getAmount();

        return wcDataPayRoomDayExtMapper.insertForColumn(entity, columns, amount);
    }

    /**
     * 更新流水
     * @param dto
     * @return
     */
    public int updateRoomDayFlow(RoomFlowChangeDTO dto){
        if (CollectionUtils.isEmpty(dto.getRuleCodes())) {
            return 0;
        }

        Integer statDateValue = MyDateUtil.getDateDayValue(dto.getTradeDate());

        WcDataPayRoomDay entity = new WcDataPayRoomDay();
        entity.setAppId(dto.getAppId());
        entity.setStatDateValue(statDateValue);
        entity.setRoomId(dto.getRoomId());
        entity.setModifyTime(new Date());
        entity.setDeployEnv(ConfigUtils.getEnvRequired().name());

        List<String> columns = new ArrayList<>();
        for (String ruleCode : dto.getRuleCodes()) {
            String column = configCodeColumnMap.get(ruleCode);
            if (column != null) {
                columns.add(column);
            }
        }
        log.info("columns={}", JsonUtil.dumps(columns));

        if (CollectionUtils.isEmpty(columns)) {
            log.warn("columns is empty");
            return 0;
        }

        String amount = dto.getAccountOpType() == AccountOpEnum.MINUS
                ? "-"+dto.getAmount()
                : "+"+dto.getAmount();

        return wcDataPayRoomDayExtMapper.updateForColumn(entity, columns, amount);
    }

}
