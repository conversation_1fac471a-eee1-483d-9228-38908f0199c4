package fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.mapper;

import fm.lizhi.common.datastore.core.annotation.DataStore;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.entity.WcDataRoomFamilyDay;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 */
@DataStore(namespace = "mysql_ocean_wavecenter")
public interface WcDataRoomFamilyDayExtMapper {

    @Select({
            "<script>"
            , "select "
            , "<foreach collection='metrics' item='m' separator=','>"
            , "sum(${m}) as ${m}"
            , "</foreach>"
            , "from wavecenter_data_room_family_day where app_id=#{appId}"
            , "and family_id=#{familyId}"
            , "and room_id in "
            , "<foreach collection='roomIds' item='roomId' open='(' close=')' separator=','>"
            , "#{roomId}"
            , "</foreach>"
            , "and stat_date_value=#{dayValue}"
            , "group by stat_date_value"
            , "</script>"
    })
    List<WcDataRoomFamilyDay> sum(@Param("metrics")List<String> metrics
            , @Param("appId") Integer appId
            , @Param("familyId") Long familyId
            , @Param("roomIds") List<Long> roomIds
            , @Param("dayValue") Integer dayValue
    );

    @Select({
            "<script>"
            , "select "
            , "stat_date_value,"
            , "<foreach collection='metrics' item='m' separator=','>"
            , "sum(${m}) as ${m}"
            , "</foreach>"
            , "from wavecenter_data_room_family_day where app_id=#{appId}"
            , "and family_id=#{familyId}"
            , "and room_id in "
            , "<foreach collection='roomIds' item='roomId' open='(' close=')' separator=','>"
            , "#{roomId}"
            , "</foreach>"
            , "and stat_date_value in "
            , "<foreach collection='dayValues' item='dayValue' open='(' close=')' separator=','>"
            , "#{dayValue}"
            , "</foreach>"
            , "group by stat_date_value"
            , "</script>"
    })
    List<WcDataRoomFamilyDay> sumDays(@Param("metrics")List<String> metrics
            , @Param("appId") Integer appId
            , @Param("familyId") Long familyId
            , @Param("roomIds") List<Long> roomIds
            , @Param("dayValues") List<Integer> dayValues
    );

}
