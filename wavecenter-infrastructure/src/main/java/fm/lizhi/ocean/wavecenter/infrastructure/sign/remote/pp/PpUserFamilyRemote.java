package fm.lizhi.ocean.wavecenter.infrastructure.sign.remote.pp;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.wavecenter.api.user.bean.FamilyBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserInFamilyBean;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.common.config.CommonConfig;
import fm.lizhi.ocean.wavecenter.common.utils.UrlUtils;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.constants.FamilyTypeMapping;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.convert.FamilyConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.sign.remote.IUserFamilyRemote;
import fm.pp.family.api.FamilyService;
import fm.pp.family.api.NewHallService;
import fm.pp.family.api.PlayerSignService;
import fm.pp.family.bean.FamilyInfo;
import fm.pp.family.protocol.BaseProto;
import fm.pp.family.protocol.FamilyServiceProto;
import fm.pp.family.protocol.NewHallServiceProto;
import fm.pp.family.protocol.PlayerSignServiceProto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2024/4/10 18:00
 */
@Slf4j
@Component
public class PpUserFamilyRemote implements IUserFamilyRemote {

    @Autowired
    private FamilyService familyService;
    @Autowired
    private CommonConfig commonConfig;
    @Autowired
    private NewHallService newHallService;
    @Autowired
    private PlayerSignService playerSignService;

    private final LoadingCache<Long, Optional<FamilyBean>> FAMILY_INFO_CACHE = CacheBuilder.newBuilder()
            .maximumSize(1000)
            .expireAfterAccess(10, TimeUnit.MINUTES)
            .build(new CacheLoader<Long, Optional<FamilyBean>>() {
                @Override
                public Optional<FamilyBean> load(Long familyId) {
                    return getFamily(familyId);
                }
            });

    @Override
    public BusinessEvnEnum getBusinessEvnEnum() {
        return BusinessEvnEnum.PP;
    }

    @Override
    public UserInFamilyBean getUserInFamily(long userId) {
        Result<FamilyServiceProto.ResponseGetUserFamily> res = familyService.getUserFamily(userId, true);
        if (RpcResult.isFail(res)) {
            log.warn("pp getUserInFamily fail userId={},rCode={}", userId, res.rCode());
            return UserInFamilyBean.builder().build();
        }

        FamilyServiceProto.UserFamilyInfo userFamilyInfo = res.target().getUserFamilyInfo();
        return UserInFamilyBean.builder()
                .familyId(userFamilyInfo.getFamilyId())
                .njId(userFamilyInfo.getNjId())
                .isFamily(userFamilyInfo.getIsFamily())
                .isRoom(userFamilyInfo.getIsRoom())
                .isPlayer(userFamilyInfo.getIsPlayer())
                .build();
    }

    @Override
    public Optional<FamilyBean> getUserFamily(long userId) {
        Result<FamilyServiceProto.ResponseGetUserFamily> result = familyService.getUserFamily(userId, true);
        if (RpcResult.isFail(result)) {
            log.warn("pp getUserFamily fail userId={},rCode={}", userId, result.rCode());
            return Optional.empty();
        }
        FamilyServiceProto.UserFamilyInfo userFamilyInfo = result.target().getUserFamilyInfo();
        return Optional.of(new FamilyBean()
                .setUserId(userFamilyInfo.getUserId())
                .setId(userFamilyInfo.getFamilyId())
                .setFamilyType(FamilyTypeMapping.bizValue2WaveType(userFamilyInfo.getFamilyType()))
                .setFamilyName(userFamilyInfo.getFamilyName()));
    }

    @Override
    public Optional<FamilyBean> getFamily(long familyId) {
        Result<FamilyServiceProto.ResponseGetFamilyInfo> familyInfoRes = familyService.getFamilyInfo(FamilyServiceProto.FamilyParam.newBuilder()
                .setFamilyId(familyId)
                .build());
        if (RpcResult.isFail(familyInfoRes)) {
            log.warn("pp,getFamily,familyId={},rCode={}", familyId, familyInfoRes.rCode());
            return Optional.empty();
        }
        String familyJsonStr = familyInfoRes.target().getFamily();
        if (StringUtils.isBlank(familyJsonStr)) {
            return Optional.empty();
        }
        FamilyInfo familyInfo = JsonUtil.loads(familyJsonStr, FamilyInfo.class);
        FamilyBean bean = new FamilyBean();
        bean.setId(familyId);
        bean.setFamilyName(familyInfo.getFamilyName());
        bean.setUserId(familyInfo.getUserId());
        bean.setFamilyNote(familyInfo.getFamilyNote());
        bean.setFamilyIconUrl(UrlUtils.getImageUrl(commonConfig.getPp().getCdnHost(), familyInfo.getPortrait()));
        bean.setCreateTime(familyInfo.getCreateTime());
        bean.setFamilyType(FamilyTypeMapping.bizValue2WaveType(familyInfo.getFamilyType()));
        return Optional.of(bean);
    }

    @Override
    public Optional<FamilyBean> getFamilyByCache(long familyId) {
        try {
            return FAMILY_INFO_CACHE.get(familyId);
        } catch (ExecutionException e) {
            log.warn("pp getFamilyByCache error familyId={}", familyId, e);
            return Optional.empty();
        }
    }

    @Override
    public Optional<Long> playerCurSignNj(long userId) {
        Result<PlayerSignServiceProto.ResponsePlayerCurSignNj> res = playerSignService.playerCurSignNj(userId);
        if (res.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("pp playerCurSignNj fail, userId={}`rCode={}", userId, res.rCode());
            return Optional.empty();
        }
        return Optional.of(res.target().getNjId());
    }

    @Override
    public Integer countCanOpenRoomNum(long familyId) {
        //PP查询新厅数
        Result<NewHallServiceProto.ResponseGetNewHallConfigs> result = newHallService.getNewHallConfigs(NewHallServiceProto.GetNewHallReq.newBuilder()
                .setFamilyId(familyId).setPageNum(1).setPageSize(1)
                .build());
        if (RpcResult.isFail(result)) {
            log.error("pp getNewHallConfigs fail. familyId={},rCode={}", familyId, result.rCode());
            return 0;
        }

        List<NewHallServiceProto.NewHallConfig> configsList = result.target().getConfigsList();
        if (CollectionUtils.isEmpty(configsList)) {
            LogContext.addResLog("configsList is empty");
            return 0;
        }

        return configsList.get(0).getLimit();
    }

    @Override
    public Optional<FamilyBean> getFamilyByUserId(long userId) {
        Result<FamilyServiceProto.ResponseGetFamily> result = familyService.getFamily(FamilyServiceProto.FamilyParam.newBuilder()
                .setUserId(userId)
                .build());
        if (RpcResult.isFail(result)) {
            log.error("pp getFamilyByUserId fail. userId={},rCode={}", userId, result.rCode());
            return Optional.empty();
        }
        BaseProto.FamilyProto family = result.target().getFamily();
        return Optional.ofNullable(FamilyConvert.I.familyPb2Dto(family));
    }
}
