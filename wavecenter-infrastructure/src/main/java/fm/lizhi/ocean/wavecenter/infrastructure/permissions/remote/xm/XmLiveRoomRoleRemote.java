package fm.lizhi.ocean.wavecenter.infrastructure.permissions.remote.xm;

import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.live.room.xm.dto.req.BatchGetLiveRoomRoleReq;
import fm.lizhi.live.room.xm.dto.resp.BatchGetLiveRoomRoleResp;
import fm.lizhi.live.room.xm.services.LiveRoomRoleSpringService;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.infrastructure.permissions.remote.LiveRoomRoleRemote;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/10 16:01
 */
@Slf4j
@Component
public class XmLiveRoomRoleRemote implements LiveRoomRoleRemote {

    @Autowired
    private LiveRoomRoleSpringService liveRoomRoleSpringService;

    @Override
    public BusinessEvnEnum getBusinessEvnEnum() {
        return BusinessEvnEnum.XIMI;
    }

    @Override
    public List<Long> getSuperAdminUserIds(List<Long> njIds) {
        Result<BatchGetLiveRoomRoleResp> result = liveRoomRoleSpringService.batchGetLiveRoomRoleUser(new BatchGetLiveRoomRoleReq()
                .setNjIds(njIds)
                .setPageNo(1)
                .setPageSize(100)
                .setRole(10));
        if (RpcResult.isFail(result)) {
            log.warn("batchGetLiveRoomRoleUser fail. njIds={}, rCode={}", JsonUtil.dumps(njIds), result.rCode());
            return Collections.emptyList();
        }

        return result.target().getUserIds();
    }
}
