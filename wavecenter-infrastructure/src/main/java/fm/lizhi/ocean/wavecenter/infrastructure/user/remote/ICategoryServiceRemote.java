package fm.lizhi.ocean.wavecenter.infrastructure.user.remote;

import fm.lizhi.ocean.wavecenter.infrastructure.common.remote.IRemote;

import java.util.Date;

/**
 * 厅分类服务远程接口
 * <AUTHOR>
 */
public interface ICategoryServiceRemote extends IRemote {

    /**
     * 获取用户分类信息
     * @param userId 用户id
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 用户分类id
     */
    Long getUserCategoryByTime(Long userId, Date startTime, Date endTime);
}
