package fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.datastore.mapper.ext;

import fm.lizhi.common.datastore.core.annotation.DataStore;
import fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.datastore.entity.ActivityTemplateHighlight;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

@DataStore(namespace = "mysql_ocean_wavecenter")
public interface ActivityTemplateHighlightExtMapper {

    @Select("SELECT * FROM `activity_template_highlight` WHERE `template_id` = #{templateId} ORDER BY `index` ASC")
    List<ActivityTemplateHighlight> getHighlightsByTemplateId(@Param("templateId") long templateId);

    @Select("<script>\n" +
            "  SELECT * FROM `activity_template_highlight` \n" +
            "  WHERE `template_id` IN \n" +
            "  <foreach collection='templateIds' item='templateId' open='(' separator=',' close=')'> \n" +
            "    #{templateId} \n" +
            "  </foreach> \n" +
            "  ORDER BY `template_id` ASC, `index` ASC \n" +
            "</script>")
    List<ActivityTemplateHighlight> getHighlightsByTemplateIds(@Param("templateIds") List<Long> templateIds);

    @Update("DELETE FROM `activity_template_highlight` WHERE `template_id` = #{templateId}")
    int deleteHighlightsByTemplateId(@Param("templateId") long templateId);
}
