package fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.remote.pp;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.base.util.JsonUtils;
import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.convert.DecorateConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.remote.IDecorateServiceRemote;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.DecorateInfoDTO;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.SendDecorateParamDTO;
import fm.lizhi.pp.vip.api.DecorateUserService;
import fm.lizhi.pp.vip.bean.enums.DecorateTipEnum;
import fm.lizhi.pp.vip.bean.req.SendDecorateReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class PpDecorateServiceRemote implements IDecorateServiceRemote {

    @Autowired
    private DecorateUserService decorateUserService;

    @Override
    public Result<Void> sendDecorate(SendDecorateParamDTO param) {
        SendDecorateReq sendDecorateReq = DecorateConvert.I.convertPpSendDecorateReq(param);
        sendDecorateReq.setOwnerIds(String.valueOf(param.getOwnerId()));
        Result<Void> result = decorateUserService.sendDecorate(JsonUtils.toJsonStringLegacy(sendDecorateReq));
        if (RpcResult.isFail(result)) {
            log.warn("pp.sendDecorate fail, req:{}, code:{}", sendDecorateReq, result.rCode());
            if (result.rCode() == DecorateTipEnum.ILLEGAL_PARAMS.getCode()) {
                return RpcResult.fail(SEND_DECORATED_PARAM_ERROR, "参数异常");
            } else if (result.rCode() == DecorateTipEnum.NOT_DATA.getCode()) {
                return RpcResult.fail(SEND_DECORATED_DECORATE_NO_EXIST, "装扮不存在");
            }
            return RpcResult.fail(SEND_DECORATED_FAIL);
        }
        return RpcResult.success();
    }

    @Override
    public Result<DecorateInfoDTO> getDecorateInfo(long decorateId) {
        return null;
    }

    @Override
    public BusinessEvnEnum getBusinessEvnEnum() {
        return BusinessEvnEnum.PP;
    }
}
