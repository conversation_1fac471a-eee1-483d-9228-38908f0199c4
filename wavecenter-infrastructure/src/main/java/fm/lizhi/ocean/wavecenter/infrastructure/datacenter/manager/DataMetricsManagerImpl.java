package fm.lizhi.ocean.wavecenter.infrastructure.datacenter.manager;

import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.constants.MetricsConstants;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.constants.MetricsMeta;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.constants.MetricsNs;
import fm.lizhi.ocean.wavecenter.service.datacenter.dto.DataFamilyDayDTO;
import fm.lizhi.ocean.wavecenter.service.datacenter.manager.DataMetricsManager;
import org.springframework.stereotype.Component;
import org.springframework.util.ReflectionUtils;

import java.util.Optional;
import java.util.concurrent.atomic.AtomicReference;

/**
 * <AUTHOR>
 * @date 2024/12/17 15:36
 */
@Component
public class DataMetricsManagerImpl implements DataMetricsManager {

    @Override
    public String formatMetricsValue(Object dataObject, String metricsName) {
        boolean forGuild = dataObject instanceof DataFamilyDayDTO;
        AtomicReference<String> result = new AtomicReference<>("0");
        ReflectionUtils.doWithLocalFields(dataObject.getClass(), field -> {
            Optional<MetricsMeta> metricsMeta = forGuild
                    ? MetricsConstants.getMetricsMeta(MetricsNs.FAMILY, metricsName)
                    : MetricsConstants.getMetricsMeta(metricsName);
            if (!metricsMeta.isPresent()) {
                return;
            }

            String name = field.getName();
            if (!name.equals(metricsMeta.get().getAliasName())) {
                return;
            }

            ReflectionUtils.makeAccessible(field);
            Object value = ReflectionUtils.getField(field, dataObject);

            MetricsMeta.ValueFactory valueFactory = metricsMeta.get().getValueFactory();
            result.set(valueFactory.calculateValue(metricsMeta.get(), name, value == null ? "0" : String.valueOf(value)));
        });

        return result.get();
    }
}
