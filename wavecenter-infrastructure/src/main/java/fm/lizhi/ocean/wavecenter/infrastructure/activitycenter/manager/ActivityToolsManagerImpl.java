package fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.manager;

import cn.hutool.core.collection.CollUtil;
import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityToolBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityToolStatusEnum;
import fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.convert.ActivityToolsInfoConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.datastore.entity.ActivityToolsInfo;
import fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.datastore.entity.ActivityToolsInfoExample;
import fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.datastore.mapper.ActivityToolsInfoMapper;
import fm.lizhi.ocean.wavecenter.service.activitycenter.manager.ActivityToolsManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class ActivityToolsManagerImpl implements ActivityToolsManager {

    @Autowired
    private ActivityToolsInfoMapper activityToolsInfoMapper;

    /**
     * 根据值获取玩法工具
     */
    @Override
    public ActivityToolBean getActivityTool(Integer toolValue, int appId) {

        ActivityToolsInfo toolsInfo = activityToolsInfoMapper.selectOne(ActivityToolsInfo.builder()
                        .appId(appId)
                        .toolValue(toolValue)
                        .deployEnv(ConfigUtils.getEnvRequired().name())
                        .status(ActivityToolStatusEnum.ENABLE.getStatus())
                .build());

        return ActivityToolsInfoConvert.I.convertActivityToolBean(toolsInfo);
    }

    /**
     * 获取所有的玩法工具
     * @param appId
     * @return
     */
    @Override
    public List<ActivityToolBean> getAllActivityTool(int appId) {

        ActivityToolsInfoExample example = new ActivityToolsInfoExample();
        example.createCriteria()
                .andAppIdEqualTo(appId)
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name())
                .andStatusEqualTo(ActivityToolStatusEnum.ENABLE.getStatus())
        ;

        List<ActivityToolsInfo> activityToolsInfoList = activityToolsInfoMapper.selectByExample(example);

        if (CollUtil.isEmpty(activityToolsInfoList)) {
            return new ArrayList<>();
        }

        return ActivityToolsInfoConvert.I.convertActivityToolBeans(activityToolsInfoList);
    }

    /**
     * 根据toolValue批量获取玩法工具
     */
    @Override
    public List<ActivityToolBean> batchGetTools(int appId, List<Integer> toolValues) {
        if (CollUtil.isEmpty(toolValues)) {
            return Collections.emptyList();
        }
        ActivityToolsInfoExample example = new ActivityToolsInfoExample();
        example.createCriteria()
                .andAppIdEqualTo(appId)
                .andToolValueIn(toolValues)
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name())
                .andStatusEqualTo(ActivityToolStatusEnum.ENABLE.getStatus())
        ;

        List<ActivityToolsInfo> activityToolsInfoList = activityToolsInfoMapper.selectByExample(example);
        return ActivityToolsInfoConvert.I.convertActivityToolBeans(activityToolsInfoList);
    }
}
