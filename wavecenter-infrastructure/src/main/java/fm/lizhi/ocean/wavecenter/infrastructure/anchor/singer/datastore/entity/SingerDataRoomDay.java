package fm.lizhi.ocean.wavecenter.infrastructure.anchor.singer.datastore.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 厅歌手汇总-天
 *
 * @date 2025-03-27 03:27:23
 */
@Table(name = "`singer_data_room_day`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SingerDataRoomDay {
    /**
     * ID
     */
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 业务
     */
    @Column(name= "`app_id`")
    private Integer appId;

    /**
     * 日期 格式 YYYY-MM-DD
     */
    @Column(name= "`stat_date`")
    private Date statDate;

    /**
     * 日期 格式 YYYYMMDD
     */
    @Column(name= "`stat_date_value`")
    private Integer statDateValue;

    /**
     * 厅主ID
     */
    @Column(name= "`nj_id`")
    private Long njId;

    /**
     * 家族ID
     */
    @Column(name= "`family_id`")
    private Long familyId;

    /**
     * 优质音乐人数量
     */
    @Column(name= "`senior_singer_auth_cnt`")
    private Integer seniorSingerAuthCnt;

    /**
     * 认证歌手数量
     */
    @Column(name= "`singer_auth_cnt`")
    private Integer singerAuthCnt;

    /**
     * 上一自然周营收（钻石）
     */
    @Column(name= "`last_week_income`")
    private Long lastWeekIncome;

    /**
     * 创建时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name= "`modify_time`")
    private Date modifyTime;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", appId=").append(appId);
        sb.append(", statDate=").append(statDate);
        sb.append(", statDateValue=").append(statDateValue);
        sb.append(", njId=").append(njId);
        sb.append(", familyId=").append(familyId);
        sb.append(", seniorSingerAuthCnt=").append(seniorSingerAuthCnt);
        sb.append(", singerAuthCnt=").append(singerAuthCnt);
        sb.append(", lastWeekIncome=").append(lastWeekIncome);
        sb.append(", createTime=").append(createTime);
        sb.append(", modifyTime=").append(modifyTime);
        sb.append("]");
        return sb.toString();
    }
}