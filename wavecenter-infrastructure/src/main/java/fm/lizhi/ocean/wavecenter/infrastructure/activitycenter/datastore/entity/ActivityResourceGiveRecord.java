package fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.datastore.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 活动资源配置记录
 *
 * @date 2024-10-18 05:59:36
 */
@Table(name = "`activity_resource_give_record`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ActivityResourceGiveRecord {
    /**
     * 主键
     */
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 要发放的资源ID
     */
    @Column(name= "`resource_id`")
    private Long resourceId;

    /**
     * 应用ID
     */
    @Column(name= "`app_id`")
    private Integer appId;

    /**
     * 活动名称，冗余，会大量使用
     */
    @Column(name= "`activity_name`")
    private String activityName;

    /**
     * 活动ID
     */
    @Column(name= "`activity_id`")
    private Long activityId;

    /**
     * 活动开始时间
     */
    @Column(name= "`start_time`")
    private Date startTime;

    /**
     * 活动结束时间
     */
    @Column(name= "`end_time`")
    private Date endTime;

    /**
     * 状态，0：待发放，1：发放失败，2：成功
     */
    @Column(name= "`status`")
    private Integer status;

    /**
     * 错误码
     */
    @Column(name= "`error_code`")
    private Integer errorCode;

    /**
     * 错误描述
     */
    @Column(name= "`error_msg`")
    private String errorMsg;

    /**
     * 重试次数
     */
    @Column(name= "`try_count`")
    private Integer tryCount;

    /**
     * 资源类型，1：装扮，2：流量资源
     */
    @Column(name= "`type`")
    private Integer type;

    /**
     * 可选值  TEST/PRE/PRO
     */
    @Column(name= "`deploy_env`")
    private String deployEnv;

    /**
     * 修改时间
     */
    @Column(name= "`modify_time`")
    private Date modifyTime;

    /**
     * 创建时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", resourceId=").append(resourceId);
        sb.append(", appId=").append(appId);
        sb.append(", activityName=").append(activityName);
        sb.append(", activityId=").append(activityId);
        sb.append(", startTime=").append(startTime);
        sb.append(", endTime=").append(endTime);
        sb.append(", status=").append(status);
        sb.append(", errorCode=").append(errorCode);
        sb.append(", errorMsg=").append(errorMsg);
        sb.append(", tryCount=").append(tryCount);
        sb.append(", type=").append(type);
        sb.append(", deployEnv=").append(deployEnv);
        sb.append(", modifyTime=").append(modifyTime);
        sb.append(", createTime=").append(createTime);
        sb.append("]");
        return sb.toString();
    }
}