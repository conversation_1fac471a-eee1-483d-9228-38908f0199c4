package fm.lizhi.ocean.wavecenter.infrastructure.user.remote.pp;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.infrastructure.user.remote.ICategoryServiceRemote;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import pp.fm.lizhi.live.pp.whitelist.api.PpWhiteListCategoryService;
import pp.fm.lizhi.live.pp.whitelist.protocol.PpWhiteListCategoryProto;

import java.util.Date;

/**
 * 厅分类服务远程接口实现类
 * <AUTHOR>
 */
@Slf4j
@Component
public class PpCategoryServiceRemote implements ICategoryServiceRemote {

    @Autowired
    private PpWhiteListCategoryService ppWhiteListCategoryService;

    @Override
    public Long getUserCategoryByTime(Long userId, Date startTime, Date endTime) {
        Result<PpWhiteListCategoryProto.ResponseGetUserCategoryByTime> result =
                ppWhiteListCategoryService.getUserCategoryByTime(userId, startTime.getTime(), endTime.getTime());
        if (RpcResult.isFail(result)) {
            log.warn("pp getUserCategoryByTime fail, userId={}`startTime={}`endTime={}`rCode={}",
                    userId, startTime, endTime, result.rCode());
            return null;
        }

        return result.target().getCategoryId();
    }

    @Override
    public BusinessEvnEnum getBusinessEvnEnum() {
        return BusinessEvnEnum.PP;
    }
}
