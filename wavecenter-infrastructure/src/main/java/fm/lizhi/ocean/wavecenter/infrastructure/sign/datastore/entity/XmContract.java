package fm.lizhi.ocean.wavecenter.infrastructure.sign.datastore.entity;

import lombok.*;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 合同
 *
 * @date 2024-05-07 04:17:18
 */
@Table(name = "`contract`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class XmContract {
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 签约dc的template_id
     */
    @Column(name= "`template_id`")
    private String templateId;

    /**
     * 签约dc的contract_id
     */
    @Column(name= "`sign_id`")
    private String signId;

    /**
     * 合同编号
     */
    @Column(name= "`contract_no`")
    private String contractNo;

    /**
     * 家族ID
     */
    @Column(name= "`family_id`")
    private Long familyId;

    /**
     * 主播ID
     */
    @Column(name= "`nj_id`")
    private Long njId;

    /**
     * 待签署:WAIT_SIGN  签署中:SIGNING  签署成功:SIGN_SUCCEED 签署失败:SIGN_FAILED 拒签:REJECT 逾期未签:OVERDUE  终止合同:STOP_CONTRACT    合同到期:OVERDUE_CONTRACT  已主变更CHANGE_OBJECT
     */
    @Column(name= "`status`")
    private String status;

    /**
     * 解约:STOP_CONTRACT 合同到期:OVERDUE_CONTRACT  已主变更CHANGE_OBJECT
     */
    @Column(name= "`change_status`")
    private String changeStatus;

    /**
     * SIGN签约 CANCEL解约 SUBJECT_CHANGE主体变更
     */
    @Column(name= "`type`")
    private String type;

    /**
     * 主播签约状态
     */
    @Column(name= "`nj_sign_status`")
    private String njSignStatus;

    /**
     * 家族签约状态
     */
    @Column(name= "`family_sign_status`")
    private String familySignStatus;

    /**
     * 合同地址
     */
    @Column(name= "`url`")
    private String url;

    /**
     * 状态原因
     */
    @Column(name= "`status_reason`")
    private String statusReason;

    /**
     * 签署截止时间
     */
    @Column(name= "`sign_deadline`")
    private Date signDeadline;

    /**
     * 签约完成时间
     */
    @Column(name= "`sign_finish_time`")
    private Date signFinishTime;

    /**
     * 签约时长 月
     */
    @Column(name= "`duration`")
    private Integer duration;

    /**
     * 合同开始时间
     */
    @Column(name= "`begin_time`")
    private Date beginTime;

    /**
     * 合同到期时间
     */
    @Column(name= "`expire_time`")
    private Date expireTime;

    /**
     * 创建时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name= "`modify_time`")
    private Date modifyTime;

    /**
     * 终止/解约合同时间
     */
    @Column(name= "`stop_time`")
    private Date stopTime;

    /**
     * 'FAMILY:公会侧 ADMIN:厅管侧'
     */
    @Column(name= "`user_type`")
    private String userType;


    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", templateId=").append(templateId);
        sb.append(", signId=").append(signId);
        sb.append(", contractNo=").append(contractNo);
        sb.append(", familyId=").append(familyId);
        sb.append(", njId=").append(njId);
        sb.append(", status=").append(status);
        sb.append(", changeStatus=").append(changeStatus);
        sb.append(", type=").append(type);
        sb.append(", njSignStatus=").append(njSignStatus);
        sb.append(", familySignStatus=").append(familySignStatus);
        sb.append(", url=").append(url);
        sb.append(", statusReason=").append(statusReason);
        sb.append(", signDeadline=").append(signDeadline);
        sb.append(", signFinishTime=").append(signFinishTime);
        sb.append(", duration=").append(duration);
        sb.append(", userType=").append(userType);
        sb.append(", beginTime=").append(beginTime);
        sb.append(", expireTime=").append(expireTime);
        sb.append(", createTime=").append(createTime);
        sb.append(", modifyTime=").append(modifyTime);
        sb.append(", stopTime=").append(stopTime);
        sb.append("]");
        return sb.toString();
    }
}
