package fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.convert;

import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.SendDecorateParamDTO;
import fm.lizhi.xm.vip.bean.decorate.req.SendDecorateReq;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface DecorateConvert {

    DecorateConvert I = Mappers.getMapper(DecorateConvert.class);

    SendDecorateReq convertXmSendDecorateReq (SendDecorateParamDTO param);

    @Mapping(target = "actionId", source = "decorateRecordId")
    fm.lizhi.pp.vip.bean.req.SendDecorateReq convertPpSendDecorateReq (SendDecorateParamDTO param);

}
