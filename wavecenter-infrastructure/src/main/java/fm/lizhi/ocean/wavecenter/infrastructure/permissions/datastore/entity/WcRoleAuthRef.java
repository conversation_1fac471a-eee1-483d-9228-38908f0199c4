package fm.lizhi.ocean.wavecenter.infrastructure.permissions.datastore.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 角色授权配置表
 *
 * @date 2024-12-03 02:47:02
 */
@Table(name = "`wavecenter_role_auth_ref`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WcRoleAuthRef {
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 业务线
     */
    @Column(name= "`app_id`")
    private Integer appId;

    /**
     * 被授权用户ID
     */
    @Column(name= "`user_id`")
    private Long userId;

    /**
     * 角色编码
     */
    @Column(name= "`role_code`")
    private String roleCode;

    /**
     * 授权范围主体ID, role_code=家族长, 该字段为family_id; role_code=厅长, 该字段为厅主ID
     */
    @Column(name= "`subject_id`")
    private Long subjectId;

    /**
     * 授权范围主体用户ID, role_code=家族长, 该字段为家族长用户ID; role_code=厅长, 该字段为厅主ID
     */
    @Column(name= "`subject_user_id`")
    private Long subjectUserId;

    /**
     * 0=禁用, 1=启用
     */
    @Column(name= "`status`")
    private Integer status;

    /**
     * 创建时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name= "`modify_time`")
    private Date modifyTime;

    /**
     * 0=未删除，null表示已删除
     */
    @Column(name= "`deleted`")
    private Integer deleted;

    /**
     * 创建用户ID
     */
    @Column(name= "`create_user_id`")
    private Long createUserId;

    /**
     * 家族ID
     */
    @Column(name= "`family_id`")
    private Long familyId;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", appId=").append(appId);
        sb.append(", userId=").append(userId);
        sb.append(", roleCode=").append(roleCode);
        sb.append(", subjectId=").append(subjectId);
        sb.append(", subjectUserId=").append(subjectUserId);
        sb.append(", status=").append(status);
        sb.append(", createTime=").append(createTime);
        sb.append(", modifyTime=").append(modifyTime);
        sb.append(", deleted=").append(deleted);
        sb.append(", createUserId=").append(createUserId);
        sb.append(", familyId=").append(familyId);
        sb.append("]");
        return sb.toString();
    }
}