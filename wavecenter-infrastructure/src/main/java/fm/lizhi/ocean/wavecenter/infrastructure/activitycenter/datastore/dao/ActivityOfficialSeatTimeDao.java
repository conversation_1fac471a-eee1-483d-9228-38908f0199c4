package fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.datastore.dao;

import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.datastore.entity.ActivityOfficialSeatTime;
import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.datastore.entity.ActivityOfficialSeatTimeExample;
import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.datastore.mapper.ActivityOfficialSeatTimeExtraMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.datastore.mapper.ActivityOfficialSeatTimeMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public class ActivityOfficialSeatTimeDao {

    @Autowired
    private ActivityOfficialSeatTimeMapper activityOfficialSeatTimeMapper;

    @Autowired
    private ActivityOfficialSeatTimeExtraMapper activityOfficialSeatTimeExtraMapper;

    public boolean batchSaveOfficialSeatTime(List<ActivityOfficialSeatTime> seatTimes) {
        if (CollectionUtils.isEmpty(seatTimes)) {
            return true;
        }
        return activityOfficialSeatTimeMapper.batchInsert(seatTimes) == seatTimes.size();
    }

    /**
     * 更新官方活动时间表
     *
     * @param seatTimeList 官频位表列表
     * @param maxCount     最大购买数量
     * @return 结果
     */
    public Pair<Boolean, ActivityOfficialSeatTime> updateOfficialSeatTime(List<ActivityOfficialSeatTime> seatTimeList, int maxCount) {
        if (CollectionUtils.isEmpty(seatTimeList)) {
            return Pair.of(true, null);
        }

        for (ActivityOfficialSeatTime seatTime : seatTimeList) {
            int row = activityOfficialSeatTimeExtraMapper.updateCount(seatTime.getAppId(), seatTime.getStartTime(),
                    seatTime.getEndTime(), seatTime.getSeat(), maxCount, ConfigUtils.getEnvRequired().name());
            if (row == 0) {
                return Pair.of(false, seatTime);
            }
        }
        return Pair.of(true, null);
    }

    /**
     * 减少官频位档期的数量
     *
     * @param seatTimeList 官频位表列表
     * @return 结果
     */
    public Pair<Boolean, ActivityOfficialSeatTime> decreasedCount(List<ActivityOfficialSeatTime> seatTimeList) {
        if (CollectionUtils.isEmpty(seatTimeList)) {
            return Pair.of(true, null);
        }

        for (ActivityOfficialSeatTime seatTime : seatTimeList) {
            int row = activityOfficialSeatTimeExtraMapper.decreasedCount(seatTime.getAppId(), seatTime.getStartTime(),
                    seatTime.getEndTime(), seatTime.getSeat(), ConfigUtils.getEnvRequired().name());
            if (row == 0) {
                return Pair.of(false, seatTime);
            }
        }
        return Pair.of(true, null);
    }

    /**
     * 根据appId和开始时间结束时间以及座位号查询出官频位时间列表
     *
     * @param appId      appId
     * @param startTime  开始时间
     * @param endTime    结束时间
     * @param seat       座位号
     * @return 官频位时间列表
     */
    public List<ActivityOfficialSeatTime> getOfficialSeatTimeList(Integer appId, Date startTime, Date endTime, Integer seat) {
        ActivityOfficialSeatTimeExample example = new ActivityOfficialSeatTimeExample();
        example.createCriteria().andAppIdEqualTo(appId).andSeatEqualTo(seat).andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name())
                        .andStartTimeGreaterThanOrEqualTo(startTime).andEndTimeLessThanOrEqualTo(endTime);
        return activityOfficialSeatTimeMapper.selectByExample(example);
    }
}
