package fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.singleton;

import fm.lizhi.common.dubbo.DubboClientBuilder;
import fm.lizhi.hy.amusement.api.DressUpGoodsService;
import fm.lizhi.hy.content.api.LiveActivityResourceService;
import fm.lizhi.live.pp.services.HomeSeatManualService;
import fm.lizhi.live.room.recommendation.api.PpRecommendationCardManageService;
import fm.lizhi.live.room.xm.services.RecommendationCardSpringService;
import fm.lizhi.ocean.lamp.common.generic.annotation.ScanBusinessProviderAPI;
import fm.lizhi.ocean.wavecenter.infrastructure.common.constants.ServiceProviderConstants;
import fm.lizhi.pp.content.artrcmd.api.OfficialActivityService;
import fm.lizhi.pp.vip.api.DecorateUserService;
import fm.lizhi.xm.content.api.BannerManagementService;
import fm.lizhi.xm.vip.services.DecorateStockService;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@ScanBusinessProviderAPI(values = {
        @ScanBusinessProviderAPI.RegisterProvider(interfaceClass = hy.fm.lizhi.live.pp.live.api.TopRecommendService.class),
        @ScanBusinessProviderAPI.RegisterProvider(interfaceClass = xm.fm.lizhi.live.pp.services.OfficialSeatService.class),
        @ScanBusinessProviderAPI.RegisterProvider(interfaceClass = hy.fm.lizhi.live.pp.copywriting.api.PpCopyWritingService.class),
})
@Configuration
public class ActivityServiceProvider {

    @Bean
    public PpRecommendationCardManageService ppRecommendationCardManageService() {
        return new DubboClientBuilder<>(PpRecommendationCardManageService.class)
                .connections(ServiceProviderConstants.connections)
                .timeoutInMillis(ServiceProviderConstants.timeout)
                .retries(ServiceProviderConstants.readRetries)
                .build();
    }

    @Bean
    public fm.lizhi.hy.content.api.BannerManagementService hyBannerManagementService() {
        return new DubboClientBuilder<>(fm.lizhi.hy.content.api.BannerManagementService.class)
                .connections(ServiceProviderConstants.connections)
                .timeoutInMillis(ServiceProviderConstants.timeout)
                .retries(ServiceProviderConstants.readRetries)
                .build();
    }

    @Bean
    public fm.lizhi.pp.content.assistant.api.BannerManagementService ppBannerManagementService() {
        return new DubboClientBuilder<>(fm.lizhi.pp.content.assistant.api.BannerManagementService.class)
                .connections(ServiceProviderConstants.connections)
                .timeoutInMillis(ServiceProviderConstants.timeout)
                .retries(ServiceProviderConstants.readRetries)
                .build();
    }

    @Bean
    public BannerManagementService xmBannerManagementService() {
        return new DubboClientBuilder<>(BannerManagementService.class)
                .connections(ServiceProviderConstants.connections)
                .timeoutInMillis(ServiceProviderConstants.timeout)
                .retries(ServiceProviderConstants.readRetries)
                .build();
    }

    @Bean
    public DressUpGoodsService hyDressUpGoodsService() {
        return new DubboClientBuilder<>(DressUpGoodsService.class)
                .connections(ServiceProviderConstants.connections)
                .timeoutInMillis(ServiceProviderConstants.timeout)
                .retries(ServiceProviderConstants.readRetries)
                .build();
    }

    @Bean
    public DecorateUserService ppDecorateUserService() {
        return new DubboClientBuilder<>(DecorateUserService.class)
                .connections(ServiceProviderConstants.connections)
                .timeoutInMillis(ServiceProviderConstants.timeout)
                .retries(ServiceProviderConstants.readRetries)
                .build();
    }

    @Bean
    public DecorateStockService xmDecorateStockService() {
        return new DubboClientBuilder<>(DecorateStockService.class)
                .connections(ServiceProviderConstants.connections)
                .timeoutInMillis(ServiceProviderConstants.timeout)
                .retries(ServiceProviderConstants.readRetries)
                .build();
    }

    @Bean
    public OfficialActivityService officialActivityService() {
        return new DubboClientBuilder<>(OfficialActivityService.class)
                .connections(ServiceProviderConstants.connections)
                .timeoutInMillis(ServiceProviderConstants.timeout)
                .retries(ServiceProviderConstants.readRetries)
                .build();
    }

    @Bean
    public LiveActivityResourceService liveActivityResourceService() {
        return new DubboClientBuilder<>(LiveActivityResourceService.class)
                .connections(ServiceProviderConstants.connections)
                .timeoutInMillis(ServiceProviderConstants.timeout)
                .retries(ServiceProviderConstants.readRetries)
                .build();
    }

    @Bean
    public HomeSeatManualService homeSeatManualService() {
        return new DubboClientBuilder<>(HomeSeatManualService.class)
                .connections(ServiceProviderConstants.connections)
                .timeoutInMillis(ServiceProviderConstants.timeout)
                .retries(ServiceProviderConstants.readRetries)
                .build();
    }
}
