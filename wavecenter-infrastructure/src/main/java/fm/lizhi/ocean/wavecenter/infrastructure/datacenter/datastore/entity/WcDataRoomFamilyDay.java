package fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.entity;

import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.FieldNameConstants;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 大数据厅日统计表-厅-公会维度
 *
 * @date 2025-03-24 05:23:38
 */
@Table(name = "`wavecenter_data_room_family_day`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
@FieldNameConstants
public class WcDataRoomFamilyDay {
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 业务
     */
    @Column(name= "`app_id`")
    private Integer appId;

    /**
     * 日期 格式 YYYY-MM-DD
     */
    @Column(name= "`stat_date`")
    private Date statDate;

    /**
     * 日期 格式  YYYYMMDD
     */
    @Column(name= "`stat_date_value`")
    private Integer statDateValue;

    /**
     * 厅主ID
     */
    @Column(name= "`room_id`")
    private Long roomId;

    /**
     * 公会ID
     */
    @Column(name= "`family_id`")
    private Long familyId;

    /**
     * 厅收入
     */
    @Column(name= "`income`")
    private BigDecimal income;

    /**
     * 厅魅力值
     */
    @Column(name= "`charm`")
    private Integer charm;

    /**
     * 开播时长(分钟)
     */
    @Column(name= "`open_duration`")
    private BigDecimal openDuration;

    /**
     * 厅签约主播数
     */
    @Column(name= "`sign_player_cnt`")
    private Integer signPlayerCnt;

    /**
     * 有收入主播人数
     */
    @Column(name= "`income_player_cnt`")
    private Integer incomePlayerCnt;

    /**
     * 有收入主播占比
     */
    @Column(name= "`income_player_rate`")
    private BigDecimal incomePlayerRate;

    /**
     * 人均收入
     */
    @Column(name= "`player_avg_income`")
    private BigDecimal playerAvgIncome;

    /**
     * 人均魅力值
     */
    @Column(name= "`player_avg_charm`")
    private BigDecimal playerAvgCharm;

    /**
     * 送礼人数
     */
    @Column(name= "`gift_user_cnt`")
    private Integer giftUserCnt;

    /**
     * 送礼客单价
     */
    @Column(name= "`gift_user_price`")
    private BigDecimal giftUserPrice;

    /**
     * 私信人数
     */
    @Column(name= "`chat_user_cnt`")
    private Integer chatUserCnt;

    /**
     * 私信回复人数
     */
    @Column(name= "`reply_chat_user_cnt`")
    private Integer replyChatUserCnt;

    /**
     * 私信进房人数
     */
    @Column(name= "`chat_enter_room_user_cnt`")
    private Integer chatEnterRoomUserCnt;

    /**
     * 私信付费人数
     */
    @Column(name= "`chat_gift_user_cnt`")
    private Integer chatGiftUserCnt;

    /**
     * 私信回复率
     */
    @Column(name= "`reply_chat_rate`")
    private BigDecimal replyChatRate;

    /**
     * 私信进房率
     */
    @Column(name= "`chat_enter_room_rate`")
    private BigDecimal chatEnterRoomRate;

    /**
     * 私信付费率
     */
    @Column(name= "`chat_gift_rate`")
    private BigDecimal chatGiftRate;

    /**
     * 邀请人数
     */
    @Column(name= "`invite_user_cnt`")
    private Integer inviteUserCnt;

    /**
     * 邀请进房人数
     */
    @Column(name= "`invite_enter_room_user_cnt`")
    private Integer inviteEnterRoomUserCnt;

    /**
     * 邀请付费人数
     */
    @Column(name= "`invite_gift_user_cnt`")
    private Integer inviteGiftUserCnt;

    /**
     * 邀请进房率
     */
    @Column(name= "`invite_enter_room_rate`")
    private BigDecimal inviteEnterRoomRate;

    /**
     * 邀请付费率
     */
    @Column(name= "`invite_gift_rate`")
    private BigDecimal inviteGiftRate;

    /**
     * 厅粉丝数
     */
    @Column(name= "`fans_user_cnt`")
    private Integer fansUserCnt;

    /**
     * 厅新增粉丝
     */
    @Column(name= "`new_fans_user_cnt`")
    private Integer newFansUserCnt;

    /**
     * 主播粉丝
     */
    @Column(name= "`player_fans_user_cnt`")
    private Integer playerFansUserCnt;

    /**
     * 主播新增粉丝
     */
    @Column(name= "`player_new_fans_user_cnt`")
    private Integer playerNewFansUserCnt;

    /**
     * 主播粉丝进房数
     */
    @Column(name= "`player_fans_enter_user_cnt`")
    private Integer playerFansEnterUserCnt;

    /**
     * 主播粉丝送礼收入
     */
    @Column(name= "`player_fans_gift_income`")
    private BigDecimal playerFansGiftIncome;

    /**
     * 主播粉丝送礼人数
     */
    @Column(name= "`player_fans_gift_user_cnt`")
    private Integer playerFansGiftUserCnt;

    /**
     * 主播粉丝送礼客单价
     */
    @Column(name= "`player_fans_gift_user_price`")
    private BigDecimal playerFansGiftUserPrice;

    /**
     * 进房人数
     */
    @Column(name= "`enter_room_user_cnt`")
    private Integer enterRoomUserCnt;

    /**
     * 上麦人数
     */
    @Column(name= "`up_guest_player_cnt`")
    private Integer upGuestPlayerCnt;

    /**
     * 直播间签约主播上麦人数
     */
    @Column(name= "`sign_up_guest_player_cnt`")
    private Integer signUpGuestPlayerCnt;

    /**
     * 评论人数
     */
    @Column(name= "`comment_user_cnt`")
    private Integer commentUserCnt;

    /**
     * 上麦率
     */
    @Column(name= "`up_guest_rate`")
    private BigDecimal upGuestRate;

    /**
     * 评论互动率
     */
    @Column(name= "`comment_rate`")
    private BigDecimal commentRate;

    /**
     * 人均逗留时长(分钟)
     */
    @Column(name= "`avg_user_stay_duration`")
    private BigDecimal avgUserStayDuration;

    /**
     * 用户逗留人数(满1分钟)
     */
    @Column(name= "`user_full_one_min`")
    private BigDecimal userFullOneMin;

    /**
     * 用户逗留人数(满3分钟)
     */
    @Column(name= "`user_full_three_min`")
    private BigDecimal userFullThreeMin;

    /**
     * 用户逗留人数(满5分钟)
     */
    @Column(name= "`user_full_five_min`")
    private BigDecimal userFullFiveMin;

    /**
     * 收入在公会的排名
     */
    @Column(name= "`income_rank`")
    private Integer incomeRank;

    /**
     * 魅力值在公会的排名
     */
    @Column(name= "`charm_rank`")
    private Integer charmRank;

    /**
     * 角色创建时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    /**
     * 角色修改时间
     */
    @Column(name= "`modify_time`")
    private Date modifyTime;

    /**
     * 总收入
     */
    @Column(name= "`all_income`")
    private BigDecimal allIncome;

    /**
     * 签约厅收礼收入
     */
    @Column(name= "`sign_hall_income`")
    private BigDecimal signHallIncome;

    /**
     * 官方厅收礼收入
     */
    @Column(name= "`official_hall_income`")
    private BigDecimal officialHallIncome;

    /**
     * 个播收礼收入
     */
    @Column(name= "`personal_hall_income`")
    private BigDecimal personalHallIncome;

    /**
     * 贵族提成收入
     */
    @Column(name= "`noble_income`")
    private BigDecimal nobleIncome;

    /**
     * 个播贵族提成收入
     */
    @Column(name= "`personal_noble_income`")
    private BigDecimal personalNobleIncome;

    /**
     * 主播上麦率
     */
    @Column(name= "`up_player_rate`")
    private BigDecimal upPlayerRate;

    /**
     * 厅评论互动率=评论人数/房间逗留人数(满1分钟)
     */
    @Column(name= "`comment_one_min_rate`")
    private BigDecimal commentOneMinRate;

    /**
     * 厅嘉宾上麦率=上麦嘉宾数/房间逗留人数(满1分钟)
     */
    @Column(name= "`up_guest_one_min_rate`")
    private BigDecimal upGuestOneMinRate;

    /**
     * 厅付费转化率(1min)
     */
    @Column(name= "`gift_one_min_rate`")
    private BigDecimal giftOneMinRate;

    /**
     * 厅付费转化率(3min)
     */
    @Column(name= "`gift_three_min_rate`")
    private BigDecimal giftThreeMinRate;

    /**
     * 厅付费转化率(5min)
     */
    @Column(name= "`gift_five_min_rate`")
    private BigDecimal giftFiveMinRate;

    /**
     * 私信主播数 本厅签约主播中有私信行为的人数
     */
    @Column(name= "`chat_player_cnt`")
    private Integer chatPlayerCnt;

    /**
     * 厅周累计收入（自然周）
     */
    @Column(name= "`week_all_income`")
    private BigDecimal weekAllIncome;

    /**
     * 私信用户数同行表现
     */
    @Column(name= "`chat_user_performance`")
    private BigDecimal chatUserPerformance;

    /**
     * 私信回复数同行表现
     */
    @Column(name= "`reply_chat_user_performance`")
    private BigDecimal replyChatUserPerformance;

    /**
     * 私信回复进房数同行表现
     */
    @Column(name= "`chat_enter_room_user_performance`")
    private BigDecimal chatEnterRoomUserPerformance;

    /**
     * 私信付费数同行表现
     */
    @Column(name= "`chat_gift_user_performance`")
    private BigDecimal chatGiftUserPerformance;

    /**
     * 有收入歌手数量
     */
    @Column(name= "`income_singer_cnt`")
    private Integer incomeSingerCnt;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", appId=").append(appId);
        sb.append(", statDate=").append(statDate);
        sb.append(", statDateValue=").append(statDateValue);
        sb.append(", roomId=").append(roomId);
        sb.append(", familyId=").append(familyId);
        sb.append(", income=").append(income);
        sb.append(", charm=").append(charm);
        sb.append(", openDuration=").append(openDuration);
        sb.append(", signPlayerCnt=").append(signPlayerCnt);
        sb.append(", incomePlayerCnt=").append(incomePlayerCnt);
        sb.append(", incomePlayerRate=").append(incomePlayerRate);
        sb.append(", playerAvgIncome=").append(playerAvgIncome);
        sb.append(", playerAvgCharm=").append(playerAvgCharm);
        sb.append(", giftUserCnt=").append(giftUserCnt);
        sb.append(", giftUserPrice=").append(giftUserPrice);
        sb.append(", chatUserCnt=").append(chatUserCnt);
        sb.append(", replyChatUserCnt=").append(replyChatUserCnt);
        sb.append(", chatEnterRoomUserCnt=").append(chatEnterRoomUserCnt);
        sb.append(", chatGiftUserCnt=").append(chatGiftUserCnt);
        sb.append(", replyChatRate=").append(replyChatRate);
        sb.append(", chatEnterRoomRate=").append(chatEnterRoomRate);
        sb.append(", chatGiftRate=").append(chatGiftRate);
        sb.append(", inviteUserCnt=").append(inviteUserCnt);
        sb.append(", inviteEnterRoomUserCnt=").append(inviteEnterRoomUserCnt);
        sb.append(", inviteGiftUserCnt=").append(inviteGiftUserCnt);
        sb.append(", inviteEnterRoomRate=").append(inviteEnterRoomRate);
        sb.append(", inviteGiftRate=").append(inviteGiftRate);
        sb.append(", fansUserCnt=").append(fansUserCnt);
        sb.append(", newFansUserCnt=").append(newFansUserCnt);
        sb.append(", playerFansUserCnt=").append(playerFansUserCnt);
        sb.append(", playerNewFansUserCnt=").append(playerNewFansUserCnt);
        sb.append(", playerFansEnterUserCnt=").append(playerFansEnterUserCnt);
        sb.append(", playerFansGiftIncome=").append(playerFansGiftIncome);
        sb.append(", playerFansGiftUserCnt=").append(playerFansGiftUserCnt);
        sb.append(", playerFansGiftUserPrice=").append(playerFansGiftUserPrice);
        sb.append(", enterRoomUserCnt=").append(enterRoomUserCnt);
        sb.append(", upGuestPlayerCnt=").append(upGuestPlayerCnt);
        sb.append(", signUpGuestPlayerCnt=").append(signUpGuestPlayerCnt);
        sb.append(", commentUserCnt=").append(commentUserCnt);
        sb.append(", upGuestRate=").append(upGuestRate);
        sb.append(", commentRate=").append(commentRate);
        sb.append(", avgUserStayDuration=").append(avgUserStayDuration);
        sb.append(", userFullOneMin=").append(userFullOneMin);
        sb.append(", userFullThreeMin=").append(userFullThreeMin);
        sb.append(", userFullFiveMin=").append(userFullFiveMin);
        sb.append(", incomeRank=").append(incomeRank);
        sb.append(", charmRank=").append(charmRank);
        sb.append(", createTime=").append(createTime);
        sb.append(", modifyTime=").append(modifyTime);
        sb.append(", allIncome=").append(allIncome);
        sb.append(", signHallIncome=").append(signHallIncome);
        sb.append(", officialHallIncome=").append(officialHallIncome);
        sb.append(", personalHallIncome=").append(personalHallIncome);
        sb.append(", nobleIncome=").append(nobleIncome);
        sb.append(", personalNobleIncome=").append(personalNobleIncome);
        sb.append(", upPlayerRate=").append(upPlayerRate);
        sb.append(", commentOneMinRate=").append(commentOneMinRate);
        sb.append(", upGuestOneMinRate=").append(upGuestOneMinRate);
        sb.append(", giftOneMinRate=").append(giftOneMinRate);
        sb.append(", giftThreeMinRate=").append(giftThreeMinRate);
        sb.append(", giftFiveMinRate=").append(giftFiveMinRate);
        sb.append(", chatPlayerCnt=").append(chatPlayerCnt);
        sb.append(", weekAllIncome=").append(weekAllIncome);
        sb.append(", chatUserPerformance=").append(chatUserPerformance);
        sb.append(", replyChatUserPerformance=").append(replyChatUserPerformance);
        sb.append(", chatEnterRoomUserPerformance=").append(chatEnterRoomUserPerformance);
        sb.append(", chatGiftUserPerformance=").append(chatGiftUserPerformance);
        sb.append(", incomeSingerCnt=").append(incomeSingerCnt);
        sb.append("]");
        return sb.toString();
    }
}