package fm.lizhi.ocean.wavecenter.infrastructure.grow.manager;

import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wavecenter.api.grow.level.bean.FamilyLevelConfigAwardBean;
import fm.lizhi.ocean.wavecenter.api.grow.level.bean.FamilyLevelConfigBean;
import fm.lizhi.ocean.wavecenter.api.grow.level.request.RequestDeleteFamilyLevelConfig;
import fm.lizhi.ocean.wavecenter.api.grow.level.request.RequestGetFamilyLevelConfigList;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.convert.FamilyLevelConfigConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.datastore.entity.WcFamilyLevelAward;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.datastore.entity.WcFamilyLevelAwardExample;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.datastore.entity.WcFamilyLevelConfig;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.datastore.entity.WcFamilyLevelConfigExample;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.datastore.mapper.WcFamilyLevelAwardMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.grow.datastore.mapper.WcFamilyLevelConfigMapper;
import fm.lizhi.ocean.wavecenter.service.grow.manager.FamilyLevelConfigManager;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/3/18 18:02
 */
@Component
public class FamilyLevelConfigManagerImpl implements FamilyLevelConfigManager {

    @Autowired
    private WcFamilyLevelConfigMapper familyLevelConfigMapper;
    @Autowired
    private WcFamilyLevelAwardMapper familyLevelAwardMapper;
    @Autowired
    private FamilyLevelConfigConvert familyLevelConfigConvert;

    @Override
    public List<FamilyLevelConfigBean> getList(RequestGetFamilyLevelConfigList request) {
        WcFamilyLevelConfigExample example = new WcFamilyLevelConfigExample();
        example.setOrderByClause("create_time desc");
        WcFamilyLevelConfigExample.Criteria criteria = example.createCriteria();
        criteria.andDeletedEqualTo(0);
        criteria.andDeployEnvEqualTo(ConfigUtils.getEnv().name());
        if (request.getAppId() != null) {
            criteria.andAppIdEqualTo(request.getAppId());
        }
        if (StringUtils.isNotBlank(request.getName())) {
            criteria.andLevelNameEqualTo(request.getName());
        }
        List<WcFamilyLevelConfig> poList = familyLevelConfigMapper.selectByExample(example);

        return familyLevelConfigConvert.pos2Beans(poList);
    }

    @Override
    public Optional<FamilyLevelConfigBean> getDeletedByName(String levelName) {
        if (StringUtils.isBlank(levelName)) {
            return Optional.empty();
        }
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();

        WcFamilyLevelConfigExample example = new WcFamilyLevelConfigExample();
        example.createCriteria()
                .andLevelNameEqualTo(levelName)
                .andDeployEnvEqualTo(ConfigUtils.getEnv().name())
                .andDeletedEqualTo(1)
                .andAppIdEqualTo(appId);

        List<WcFamilyLevelConfig> list = familyLevelConfigMapper.selectByExample(example);
        if (CollectionUtils.isNotEmpty(list)) {
            return Optional.of(familyLevelConfigConvert.po2Bean(list.get(0)));
        }
        return Optional.empty();
    }

    @Override
    public void delete(RequestDeleteFamilyLevelConfig request) {
        WcFamilyLevelConfigExample example = new WcFamilyLevelConfigExample();
        example.createCriteria()
                .andIdEqualTo(request.getId())
                .andAppIdEqualTo(request.getAppId());
        WcFamilyLevelConfig entity = new WcFamilyLevelConfig();
        entity.setDeleted(1);
        familyLevelConfigMapper.updateByExample(entity, example);
    }

    @Override
    public List<FamilyLevelConfigAwardBean> getAwardlist(RequestGetFamilyLevelConfigList request) {
        List<FamilyLevelConfigBean> list = getList(request);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }

        List<Long> ids = list.stream().map(FamilyLevelConfigBean::getId).collect(Collectors.toList());
        WcFamilyLevelAwardExample example = new WcFamilyLevelAwardExample();
        example.createCriteria().andLevelIdIn(ids).andAppIdEqualTo(request.getAppId());

        List<WcFamilyLevelAward> awardList = familyLevelAwardMapper.selectByExample(example);
        Map<Long, List<WcFamilyLevelAward>> awardMap = awardList.stream()
                .collect(Collectors.groupingBy(WcFamilyLevelAward::getLevelId));

        return familyLevelConfigConvert.beans2AwardBeans(list, awardMap);
    }

    @Override
    public FamilyLevelConfigBean getLevelConfig(Long levelId) {
        if (levelId == null) {
            return null;
        }
        WcFamilyLevelConfig getById = new WcFamilyLevelConfig();
        getById.setId(levelId);
        WcFamilyLevelConfig po = familyLevelConfigMapper.selectByPrimaryKey(getById);
        return familyLevelConfigConvert.po2Bean(po);
    }

    @Override
    public Map<Long, FamilyLevelConfigBean> getLevelConfigMap(List<Long> levelIds) {
        if (CollectionUtils.isEmpty(levelIds)) {
            return Collections.emptyMap();
        }
        WcFamilyLevelConfigExample example = new WcFamilyLevelConfigExample();
        WcFamilyLevelConfigExample.Criteria criteria = example.createCriteria();
        criteria.andIdIn(levelIds);
        List<WcFamilyLevelConfig> poList = familyLevelConfigMapper.selectByExample(example);
        HashMap<Long, FamilyLevelConfigBean> map = new HashMap<>();
        for (WcFamilyLevelConfig po : poList) {
            FamilyLevelConfigBean bean = familyLevelConfigConvert.po2Bean(po);
            map.put(po.getId(), bean);
        }
        return map;
    }
}
