package fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.remote;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.infrastructure.common.remote.IRemote;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.DeleteBannerParamDTO;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.EditBannerParamDTO;

public interface IBannerServiceRemote extends IRemote {

    Result<Long> editBanner(EditBannerParamDTO param);

    /**
     * 修改banner图
     *
     * @param param 参数
     * @return 结果
     */
    Result<Void> deleteBanner(DeleteBannerParamDTO param);

    int EDIT_BANNER_FAIL = 1;

    int DELETE_BANNER_FAIL = 1;

}
