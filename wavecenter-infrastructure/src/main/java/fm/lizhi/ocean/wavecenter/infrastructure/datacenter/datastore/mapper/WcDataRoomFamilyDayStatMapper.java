package fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.mapper;

import fm.lizhi.common.datastore.core.annotation.DataStore;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.entity.WcDataRoomFamilyDayPo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 */
@DataStore(namespace = "mysql_ocean_wavecenter")
public interface WcDataRoomFamilyDayStatMapper {

    @Select({
            "<script>"
            , "select s.room_id, income "
            , " from wavecenter_data_room_family_day s "
            , " where s.family_id=#{familyId} and s.stat_date = #{statDate} and app_id = #{appId} and income &gt; 0 "

            , "<if test='null != roomIds and roomIds.size > 0'>"
            , "and s.room_id in "
            , "<foreach collection='roomIds' item='rId' open='(' separator=',' close=')'>"
            , "#{rId}"
            , "</foreach>"
            , "</if>"

            , " order by income ${rankType} limit ${count} "
            , "</script>"
    })
    List<WcDataRoomFamilyDayPo> selectRoomFamilyDayIncomeStat(@Param("appId") int appId,
                                                              @Param("familyId") long familyId,
                                                              @Param("roomIds") List<Long> roomIds,
                                                              @Param("statDate") String statDate,
                                                              @Param("rankType") String rankType,
                                                              @Param("count") int count);
}
