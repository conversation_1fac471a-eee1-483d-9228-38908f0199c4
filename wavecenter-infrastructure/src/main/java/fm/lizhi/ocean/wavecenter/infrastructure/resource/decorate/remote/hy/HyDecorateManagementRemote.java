package fm.lizhi.ocean.wavecenter.infrastructure.resource.decorate.remote.hy;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.hy.amusement.api.DressUpInfoService;
import fm.lizhi.hy.amusement.constant.DressUpSourceEnum;
import fm.lizhi.hy.amusement.protocol.DressUpInfoProto;
import fm.lizhi.hy.vip.bo.DressUpExtInfoBO;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.api.common.service.CommonService;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.bean.hy.HyCreateAvatarWidgetBean;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.bean.hy.HyCreateRoomBackgroundBean;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.constants.DecorateTimeTypeEnum;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.request.RequestCreateAvatarWidget;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.request.RequestCreateRoomBackground;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.response.ResponseCreateAvatarWidget;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.response.ResponseCreateRoomBackground;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.base.util.JsonUtils;
import fm.lizhi.ocean.wavecenter.common.utils.UrlUtils;
import fm.lizhi.ocean.wavecenter.common.validation.BeanValidator;
import fm.lizhi.ocean.wavecenter.common.validation.ValidateResult;
import fm.lizhi.ocean.wavecenter.infrastructure.resource.decorate.convert.hy.HyDecorateManagementConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.resource.decorate.remote.DecorateManagementRemote;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Objects;

@Component
@Slf4j
public class HyDecorateManagementRemote implements DecorateManagementRemote {

    private final HyDecorateManagementConvert hyDecorateManagementConvert = HyDecorateManagementConvert.I;

    @Autowired
    private BeanValidator beanValidator;

    @Autowired
    private DressUpInfoService dressUpInfoService;

    @Override
    public BusinessEvnEnum getBusinessEvnEnum() {
        return BusinessEvnEnum.HEI_YE;
    }

    @Override
    public Result<ResponseCreateRoomBackground> createRoomBackground(RequestCreateRoomBackground request) {
        try {
            HyCreateRoomBackgroundBean bean = hyDecorateManagementConvert.toHyCreateRoomBackgroundBean(request);
            ValidateResult validateResult = beanValidator.validate(bean);
            if (validateResult.isInvalid()) {
                log.info("createRoomBackground request invalid, request={}, validateResult={}", request, validateResult);
                return RpcResult.fail(CommonService.PARAM_ERROR, validateResult.getMessage());
            }
            DressUpInfoProto.DressUpInfo roomBackgroundProto = buildCreateRoomBackgroundProto(bean);
            Result<DressUpInfoProto.ResponseAddOrEditDressUp> result = dressUpInfoService.addOrEditDressUp(roomBackgroundProto);
            int rCode = result.rCode();
            if (rCode == DressUpInfoService.ADD_OR_EDIT_DRESS_UP_ILLEGAL_PARAMS) {
                log.info("addOrEditDressUp params invalid, roomBackgroundProto={}", roomBackgroundProto);
                return RpcResult.fail(CommonService.PARAM_ERROR, "参数错误");
            }
            if (rCode != GeneralRCode.GENERAL_RCODE_SUCCESS) {
                log.error("addOrEditDressUp fail, rCode={}, roomBackgroundProto={}", rCode, roomBackgroundProto);
                return RpcResult.fail(CommonService.BUSINESS_ERROR, "保存失败");
            }
            DressUpInfoProto.ResponseAddOrEditDressUp target = result.target();
            log.info("addOrEditDressUp success, roomBackgroundProto={}, target={}", roomBackgroundProto, target);
            ResponseCreateRoomBackground response = new ResponseCreateRoomBackground();
            response.setId(target.getId());
            response.setPreviewUrl(bean.getMaterialUrl());
            return RpcResult.success(response);
        } catch (RuntimeException e) {
            log.error("createRoomBackground error, request={}", request, e);
            return RpcResult.fail(CommonService.INTERNAL_ERROR, "内部错误");
        }
    }

    private DressUpInfoProto.DressUpInfo buildCreateRoomBackgroundProto(HyCreateRoomBackgroundBean bean) {
        int dressUpType = DressUpInfoProto.DressUpTypeEnums.BACKGROUD_VALUE;
        DressUpInfoProto.DressUpInfo.Builder builder = DressUpInfoProto.DressUpInfo.newBuilder();
        builder.setSource(DressUpSourceEnum.PC.getValue());
        builder.setDressUpType(dressUpType);
        builder.setDressUpName(bean.getName());
        // 各种URL字段去掉host和开头的斜杠, 保持和互娱后台前端提交的一致
        builder.setThumbUrl(UrlUtils.removeHostAndStartSlashOrEmpty(bean.getThumbUrl()));
        builder.setMaterialUrl(UrlUtils.removeHostAndStartSlashOrEmpty(bean.getMaterialUrl()));
        builder.setSvgaMaterialUrl(UrlUtils.removeHostAndStartSlashOrEmpty(bean.getSvgaMaterialUrl()));
        builder.setTimeType(bean.getTimeType());
        builder.setTimeLimit(Objects.equals(bean.getTimeType(), DecorateTimeTypeEnum.LIMIT_TIME.getValue()) ? bean.getValidMin() : 0);
        builder.setVisibility(bean.getVisibility());
        builder.setDressUpCoin(bean.getDressUpCoin());
        builder.setRemark(StringUtils.defaultString(bean.getRemark()));
        // 扩展信息
        ArrayList<DressUpExtInfoBO> dressUpExtInfoBOS = new ArrayList<>();
        if (StringUtils.isNotBlank(bean.getBackgroundColor())) {
            DressUpExtInfoBO backgroundColorBO = buildBackgroundColorExtBO(dressUpType, bean.getBackgroundColor());
            dressUpExtInfoBOS.add(backgroundColorBO);
        }
        // 如果扩展信息列表为空则设置为空字符串, 从web-hy-amusement-manager中抄的逻辑
        String extInfo = CollectionUtils.isNotEmpty(dressUpExtInfoBOS) ? JsonUtils.toJsonString(dressUpExtInfoBOS) : StringUtils.EMPTY;
        builder.setExtInfo(extInfo);
        return builder.build();
    }

    private DressUpExtInfoBO buildBackgroundColorExtBO(int dressUpType, String backgroundColor) {
        // key和name是根据数据库值抄的, lz-hy-vip代码没看到相关常量
        DressUpExtInfoBO dressUpExtInfoBO = new DressUpExtInfoBO();
        dressUpExtInfoBO.setType(dressUpType);
        dressUpExtInfoBO.setKey("backgroundColor");
        dressUpExtInfoBO.setName("背景特效颜色");
        dressUpExtInfoBO.setValue(backgroundColor);
        return dressUpExtInfoBO;
    }

    @Override
    public Result<ResponseCreateAvatarWidget> createAvatarWidget(RequestCreateAvatarWidget request) {
        try {
            HyCreateAvatarWidgetBean bean = hyDecorateManagementConvert.toHyCreateAvatarWidgetBean(request);
            ValidateResult validateResult = beanValidator.validate(bean);
            if (validateResult.isInvalid()) {
                log.info("createAvatarWidget request invalid, request={}, validateResult={}", request, validateResult);
                return RpcResult.fail(CommonService.PARAM_ERROR, validateResult.getMessage());
            }
            DressUpInfoProto.DressUpInfo avatarWidgetProto = buildAvatarWidgetProto(bean);
            Result<DressUpInfoProto.ResponseAddOrEditDressUp> result = dressUpInfoService.addOrEditDressUp(avatarWidgetProto);
            int rCode = result.rCode();
            if (rCode == DressUpInfoService.ADD_OR_EDIT_DRESS_UP_ILLEGAL_PARAMS) {
                log.info("addOrEditDressUp params invalid, avatarWidgetProto={}", avatarWidgetProto);
                return RpcResult.fail(CommonService.PARAM_ERROR, "参数错误");
            }
            if (rCode != GeneralRCode.GENERAL_RCODE_SUCCESS) {
                log.error("addOrEditDressUp fail, rCode={}, avatarWidgetProto={}", rCode, avatarWidgetProto);
                return RpcResult.fail(CommonService.BUSINESS_ERROR, "保存失败");
            }
            DressUpInfoProto.ResponseAddOrEditDressUp target = result.target();
            log.info("addOrEditDressUp success, avatarWidgetProto={}, target={}", avatarWidgetProto, target);
            ResponseCreateAvatarWidget response = new ResponseCreateAvatarWidget();
            response.setId(target.getId());
            response.setPreviewUrl(bean.getMaterialUrl());
            return RpcResult.success(response);
        } catch (RuntimeException e) {
            log.error("createAvatarWidget error, request={}", request, e);
            return RpcResult.fail(CommonService.INTERNAL_ERROR, "内部错误");
        }
    }

    private DressUpInfoProto.DressUpInfo buildAvatarWidgetProto(HyCreateAvatarWidgetBean bean) {
        int dressUpType = DressUpInfoProto.DressUpTypeEnums.AVATAR_WIDGET_VALUE;
        DressUpInfoProto.DressUpInfo.Builder builder = DressUpInfoProto.DressUpInfo.newBuilder();
        builder.setSource(DressUpSourceEnum.PC.getValue());
        builder.setDressUpType(dressUpType);
        builder.setDressUpName(bean.getName());
        // 各种URL字段去掉host和开头的斜杠, 保持和互娱后台前端提交的一致
        builder.setThumbUrl(UrlUtils.removeHostAndStartSlashOrEmpty(bean.getThumbUrl()));
        builder.setMaterialUrl(UrlUtils.removeHostAndStartSlashOrEmpty(bean.getMaterialUrl()));
        builder.setSvgaMaterialUrl(UrlUtils.removeHostAndStartSlashOrEmpty(bean.getSvgaMaterialUrl()));
        builder.setTimeType(bean.getTimeType());
        builder.setTimeLimit(Objects.equals(bean.getTimeType(), DecorateTimeTypeEnum.LIMIT_TIME.getValue()) ? bean.getValidMin() : 0);
        builder.setVisibility(bean.getVisibility());
        builder.setDressUpCoin(bean.getDressUpCoin());
        builder.setRemark(StringUtils.defaultString(bean.getRemark()));
        // 扩展信息, 头像框扩展信息为空, 直接设为空字符串, 从web-hy-amusement-manager中抄的逻辑
        builder.setExtInfo(StringUtils.EMPTY);
        return builder.build();
    }
}
