package fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.remote.hy;

import com.google.common.collect.Lists;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.hy.amusement.api.DressUpGoodsService;
import fm.lizhi.hy.amusement.api.DressUpInfoService;
import fm.lizhi.hy.amusement.protocol.DressUpGoodsProto;
import fm.lizhi.hy.amusement.protocol.DressUpInfoProto;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.remote.IDecorateServiceRemote;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.DecorateInfoDTO;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.SendDecorateParamDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class HyDecorateServiceRemote implements IDecorateServiceRemote {

    @Autowired
    private DressUpGoodsService dressUpGoodsService;

    @Autowired
    private DressUpInfoService dressUpInfoService;

    @Override
    public Result<Void> sendDecorate(SendDecorateParamDTO param) {
        Result<DressUpGoodsProto.ResponseAddUserRights> result = dressUpGoodsService.addUserRights(param.getOwnerId(), param.getDecorateId(), param.getCount());
        if (RpcResult.isFail(result)) {
            return RpcResult.fail(SEND_DECORATED_FAIL);
        }
        return RpcResult.success();
    }

    @Override
    public Result<DecorateInfoDTO> getDecorateInfo(long decorateId) {
        Result<DressUpInfoProto.ResponseGetDressUpListByIds> result = dressUpInfoService.getDressUpListByIds(Lists.newArrayList(decorateId));
        if (RpcResult.isFail(result)) {
            return RpcResult.fail(GET_DECORATED_FAIL);
        }

        DressUpInfoProto.DressUpInfo dressUpInfo = result.target().getDressUpInfo(0);
        DecorateInfoDTO infoDTO = new DecorateInfoDTO().setId(decorateId)
                .setName(dressUpInfo.getDressUpName())
                .setType(dressUpInfo.getDressUpType())
                .setThumbUrl(dressUpInfo.getThumbUrl())
                .setCoin(dressUpInfo.getDressUpCoin())
                .setVailMin(dressUpInfo.getTimeLimit());
        return RpcResult.success(infoDTO);
    }

    @Override
    public BusinessEvnEnum getBusinessEvnEnum() {
        return BusinessEvnEnum.HEI_YE;
    }
}
