package fm.lizhi.ocean.wavecenter.infrastructure.award.family.manager.deliver.level;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.award.family.constants.FamilyAwardResourceDeliverTypeEnum;
import fm.lizhi.ocean.wavecenter.api.award.family.constants.FamilyAwardTypeEnum;
import fm.lizhi.ocean.wavecenter.api.common.service.CommonService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.infrastructure.award.family.manager.deliver.DeliverResourceParam;
import fm.lizhi.ocean.wavecenter.infrastructure.award.family.manager.deliver.FamilyAwardDeliver;
import fm.lizhi.ocean.wavecenter.infrastructure.award.family.manager.deliver.GetResourceInfoResult;
import fm.lizhi.ocean.wavecenter.service.resource.shortnumber.dto.ShortNumberDTO;
import fm.lizhi.ocean.wavecenter.service.resource.shortnumber.manager.ShortNumberManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 公会等级奖励短号发放实现
 */
@Component
@Slf4j
public class FamilyLevelAwardShortNumberDeliver implements FamilyAwardDeliver {

    @Autowired
    private ShortNumberManager shortNumberManager;

    @Override
    public boolean supports(FamilyAwardTypeEnum awardType, FamilyAwardResourceDeliverTypeEnum deliverType) {
        return awardType == FamilyAwardTypeEnum.LEVEL && deliverType == FamilyAwardResourceDeliverTypeEnum.SHORT_NUMBER;
    }

    @Override
    public Result<GetResourceInfoResult> getResourceInfo(DeliverResourceParam param) {
        try {
            ShortNumberDTO shortNumber = shortNumberManager.getShortNumber(param.getAppId(), param.getResourceId());
            if (shortNumber == null) {
                return RpcResult.fail(CommonService.PARAM_ERROR, StringUtils.EMPTY);
            }
            return RpcResult.success(GetResourceInfoResult.of(shortNumber.getName(), StringUtils.EMPTY));
        } catch (RuntimeException e) {
            log.info("Failed to get short number info, resourceId={}", param.getResourceId(), e);
            return RpcResult.fail(CommonService.INTERNAL_ERROR, e.getMessage());
        }
    }

    @Override
    public Result<Void> deliverResource(DeliverResourceParam param) {
        // 短号发放仅做记录, 由运营手动发放
        return RpcResult.success();
    }
}
