package fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.datastore.mapper;

import fm.lizhi.common.datastore.core.annotation.DataStore;
import fm.lizhi.common.datastore.mysql.constant.ParamContants;
import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.datastore.entity.ActivityApplyInfo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.Date;
import java.util.List;

@DataStore(namespace = "mysql_ocean_wavecenter")
public interface ActivityApplyInfoExtraMapper {

    /**
     * 查询时间范围内是待审批和审核通过的活动ID
     *
     * @param startTime   活动开始时间
     * @param endTime     活动结束时间
     * @param njId        厅主ID
     * @param auditStatusList 状态
     * @return 活动ID
     */
    @Select("<script>" +
            "select count(*) " +
            "from activity_apply_info " +
            "where ((start_time &lt; #{endTime} AND end_time &gt; #{startTime}) " +
            "      or (start_time = #{startTime} AND end_time = #{endTime})) " +
            "      and nj_id = #{njId} " +
            "      and audit_status in " +
            "      <foreach collection='auditStatusList' item='status' open='(' separator=',' close=')'>" +
            "          #{status}" +
            "      </foreach>" +
            "      and deleted = 0" +
            "</script>")
    Integer getValidActivityCount(@Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("njId") Long njId, @Param("auditStatusList") List<Integer> auditStatusList);

    @Select("<script>" +
            "select id " +
            "from activity_apply_info " +
            "where ((start_time &lt; #{endTime} AND end_time &gt; #{startTime}) " +
            "      or (start_time = #{startTime} AND end_time = #{endTime})) " +
            "      and nj_id = #{njId} " +
            "      and audit_status in " +
            "      <foreach collection='auditStatusList' item='status' open='(' separator=',' close=')'>" +
            "          #{status}" +
            "      </foreach>" +
            "      and deleted = 0" +
            "</script>")
    List<Long> getValidActivityIds(@Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("njId") Long njId, @Param("auditStatusList") List<Integer> auditStatusList);

    @Select("select c.level_id from activity_apply_info i left join activity_class_config c on i.class_id = c.id where i.id = #{activityId}")
    Long getActivityLevelById(Long activityId);

    /**
     * 查询指定时间范围内的活动
     * @param deployEnv 部署环境
     * @param appId 应用ID
     * @param deleted 删除状态
     * @param auditStatus 审核状态
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 活动列表
     */
    @Select("select * from activity_apply_info " +
            "where deploy_env = #{deployEnv}" +
            " and app_id = #{appId}" +
            " and deleted = #{deleted}" +
            " and audit_status = #{auditStatus}" +
            " and start_time >= #{startTime} and start_time <= #{endTime}" +
            " order by start_time asc")
    List<ActivityApplyInfo> selectForApp(@Param("deployEnv") String deployEnv, @Param("appId") Integer appId,
                                         @Param("deleted") Integer deleted, @Param("auditStatus") Integer auditStatus,
                                         @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 根据ID和版本号更新活动信息
     * @param applyInfo 活动信息
     * @return 影响行数
     */
    @Update("<script>" +
            "update activity_apply_info " +
            "set version = version + 1" +
            "<if test='contact != null'>, contact = #{contact}</if>" +
            "<if test='applicantUid != null'>, applicant_uid = #{applicantUid}</if>" +
            "<if test='name != null'>, name = #{name}</if>" +
            "<if test='njId != null'>, nj_id = #{njId}</if>" +
            "<if test='familyId != null'>, family_id = #{familyId}</if>" +
            "<if test='classId != null'>, class_id = #{classId}</if>" +
            "<if test='startTime != null'>, start_time = #{startTime}</if>" +
            "<if test='endTime != null'>, end_time = #{endTime}</if>" +
            "<if test='accompanyNjIds != null'>, accompany_nj_ids = #{accompanyNjIds}</if>" +
            "<if test='activityTool != null'>, activity_tool = #{activityTool}</if>" +
            "<if test='introduction != null'>, introduction = #{introduction}</if>" +
            "<if test='goal != null'>, goal = #{goal}</if>" +
            "<if test='hostId != null'>, host_id = #{hostId}</if>" +
            "<if test='avatarWidgetId != null'>, avatar_widget_id = #{avatarWidgetId}</if>" +
            "<if test='contactNumber != null'>, contact_number = #{contactNumber}</if>" +
            "<if test='roomAnnouncement != null'>, room_announcement = #{roomAnnouncement}</if>" +
            "<if test='roomAnnouncementImgUrl != null'>, room_announcement_img_url = #{roomAnnouncementImgUrl}</if>" +
            "<if test='roomBackgroundId != null'>, room_background_id = #{roomBackgroundId}</if>" +
            "<if test='posterUrl != null'>, poster_url = #{posterUrl}</if>" +
            "<if test='auxiliaryPropUrl != null'>, auxiliary_prop_url = #{auxiliaryPropUrl}</if>" +
            "where id = #{id} and version = #{version}" +
            "</script>")
    Integer updateActivityApplyInfoByIdAndVersion(ActivityApplyInfo applyInfo);

    /**
     * 根据ID和版本号更新审核状态
     * @param applyInfo 活动信息
     * @param originalStatus 原状态
     * @return 影响行数
     */
    @Update("<script>" +
            "update activity_apply_info " +
            "set version = version + 1" +
            ", audit_status = #{entity.auditStatus}" +
            "<if test='entity.auditReason != null'>, audit_reason = #{entity.auditReason}</if>" +
            "<if test='entity.auditOperator != null'>, audit_operator = #{entity.auditOperator}</if>" +
            "where id = #{entity.id} and version = #{entity.version}" +
            "<if test='originalStatus != null'> and audit_status = #{originalStatus}</if>" +
            "</script>")
    Integer updateActivityStatusByIdAndVersion(@Param(ParamContants.ENTITY) ActivityApplyInfo applyInfo, @Param("originalStatus") Integer originalStatus);
}
