package fm.lizhi.ocean.wavecenter.infrastructure.anchor.singer.kafka;

import fm.lizhi.common.kafka.ioc.api.KafkaTemplate;
import fm.lizhi.common.kafka.ioc.spring.EnableKafkaClients;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Description:
 *
 * <AUTHOR>
 * Created in  2023/5/19
 */
@Configuration
@EnableKafkaClients(basePackages = "fm.lizhi.ocean.wavecenter.infrastructure.anchor.singer.kafka.consumer")
public class SingerKafkaClientConfiguration {

}
