package fm.lizhi.ocean.wavecenter.infrastructure.resource.newroom.remote.pp;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.infrastructure.resource.newroom.remote.NewRoomRemote;
import fm.lizhi.ocean.wavecenter.infrastructure.resource.newroom.remote.request.RequestSetNewRoomConfig;
import fm.pp.family.api.NewHallService;
import fm.pp.family.protocol.NewHallServiceProto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Slf4j
public class PpNewRoomRemote implements NewRoomRemote {

    @Autowired
    private NewHallService newHallService;

    @Override
    public BusinessEvnEnum getBusinessEvnEnum() {
        return BusinessEvnEnum.PP;
    }

    @Override
    public Result<Void> setNewRoomConfig(RequestSetNewRoomConfig request) {
        Long familyId = request.getFamilyId();
        Integer limit = request.getLimit();
        NewHallServiceProto.NewHallConfig.Builder saveRequestBuilder = NewHallServiceProto.NewHallConfig.newBuilder()
                .setFamilyId(familyId)
                .setLimit(limit);
        // 先查出是否已有配置
        NewHallServiceProto.GetNewHallReq getNewHallReq = NewHallServiceProto.GetNewHallReq.newBuilder()
                .setFamilyId(familyId).setPageNum(1).setPageSize(1)
                .build();
        Result<NewHallServiceProto.ResponseGetNewHallConfigs> getResult = newHallService.getNewHallConfigs(getNewHallReq);
        if (RpcResult.isSuccess(getResult)) {
            List<NewHallServiceProto.NewHallConfig> configsList = getResult.target().getConfigsList();
            if (CollectionUtils.isNotEmpty(configsList)) {
                // 如果已有配置, 将id作为保存请求的参数
                saveRequestBuilder.setId(configsList.get(0).getId());
            }
        }
        // 如果没有已存在的配置, 则会自动创建新的配置
        NewHallServiceProto.NewHallConfig saveRequest = saveRequestBuilder.build();
        return newHallService.saveNewHallConfig(saveRequest);
    }
}
