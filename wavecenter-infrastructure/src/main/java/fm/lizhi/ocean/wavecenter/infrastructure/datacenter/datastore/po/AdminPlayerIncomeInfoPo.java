package fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.po;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 厅管侧签约陪玩收入数据信息
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class AdminPlayerIncomeInfoPo {
    /**
     * 厅主id
     */
    private long njId;
    /**
     * 总魅力值
     */
    private long totalCharmValue;
    /**
     * 厅签约主播收入汇总列表
     */
    private List<PlayerIncomeStatPo> playerIncomeStatList;
}
