package fm.lizhi.ocean.wavecenter.infrastructure.income.manager;

import fm.lizhi.common.datastore.redis.client.RedisClient;
import fm.lizhi.commons.util.DateUtil;
import fm.lizhi.commons.util.GuidGenerator;
import fm.lizhi.ocean.wavecenter.common.config.CommonConfig;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wavecenter.common.utils.DateTimeUtils;
import fm.lizhi.ocean.wavecenter.infrastructure.income.datastore.entity.WcPlayerSignCharmStat;
import fm.lizhi.ocean.wavecenter.infrastructure.income.datastore.mapper.WcPlayerSignCharmStatMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.kafka.message.GiftMsg;
import fm.lizhi.ocean.wavecenter.infrastructure.user.constants.IncomeRedisKey;
import fm.lizhi.pp.util.utils.EnvUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import redis.clients.jedis.params.SetParams;

import javax.annotation.Resource;
import java.util.Date;

@Component
public class WcPlayerSignCharmStatManager {

    @Autowired
    private GuidGenerator guidGenerator;

    @Resource(name = "redis_ocean_wavecenter")
    private RedisClient redisClient;

    @Autowired
    private WcPlayerSignCharmStatMapper wcPlayerSignCharmStatMapper;

    @Autowired
    private CommonConfig commonConfig;

    /**
     * 保存礼物收入信息
     *
     * @param hasInit 是否已经初始化
     * @param date    日期
     * @param giftMsg 礼物数据
     * @return true：成功，false: 失败
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean saveGiftIncome(boolean hasInit, String date, GiftMsg giftMsg, long familyId) {
        //保存初始化记录数据
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        Date statDate = DateTimeUtils.formatTimeToDate(new Date(giftMsg.getCreateTime()), DateUtil.date_2);
        if (!hasInit) {
            WcPlayerSignCharmStat charmStat = WcPlayerSignCharmStat.builder()
                    .statDay(statDate)
                    .appId((long) ContextUtils.getBusinessEvnEnum().getAppId())
                    .id(guidGenerator.genId())
                    .familyId(familyId)
                    .value(0L)
                    .userId(giftMsg.getRecTargetUserId())
                    .roomId(giftMsg.isOfficialRoom() ? giftMsg.getFamilyNjId() : giftMsg.getRecUserId())
                    .build();
            wcPlayerSignCharmStatMapper.insertIgnore(charmStat);
            //设置初始化记录标记，失败了也不用管
            setInitCharmStatRecord(appId, giftMsg.getRecUserId(), giftMsg.getRecTargetUserId(), date);
        }

        //累加数据 魅力值取绝对值, 出现负数是因为玩法, 礼物收入为正数
        wcPlayerSignCharmStatMapper.incrementValue(appId, familyId, giftMsg.isOfficialRoom() ? giftMsg.getFamilyNjId() : giftMsg.getRecUserId(),
                giftMsg.getRecTargetUserId(), statDate, Math.abs(giftMsg.getValue()));

        //设置幂等
        boolean res = setConsumeIdempotent(appId, giftMsg.getTransactionId());
        if (!res) {
            throw new RuntimeException("setConsumeIdempotent error.");
        }
        return true;
    }

    private boolean setInitCharmStatRecord(int appId, long njId, long userId, String date) {
        if (EnvUtils.isPre()) {
            String key = IncomeRedisKey.INIT_CHARM_STAT_HASH.getKey(appId, njId, date, commonConfig.getConsumeIdempotentTag());
            Long value = redisClient.hincrBy(key, String.valueOf(userId), 1);
            redisClient.expire(key, 60 * 60); //缓存1个小时
            return value != null && value > 0;
        }

        String key = IncomeRedisKey.INIT_CHARM_STAT_HASH.getKey(appId, njId, date);
        Long value = redisClient.hincrBy(key, String.valueOf(userId), 1);
        redisClient.expire(key, 60 * 60); //缓存1个小时
        return value != null && value > 0;
    }

    private boolean setConsumeIdempotent(int appId, long transactionId) {
        if (EnvUtils.isPre()) {
            String key = IncomeRedisKey.GIFT_MSG_IDEMPOTENT_INCOME_STR.getKey(appId, transactionId, commonConfig.getConsumeIdempotentTag());
            SetParams params = SetParams.setParams().ex(commonConfig.getConsumeIdempotent());
            String value = redisClient.set(key, String.valueOf(transactionId), params);
            return StringUtils.isNotBlank(value);
        }
        String key = IncomeRedisKey.GIFT_MSG_IDEMPOTENT_INCOME_STR.getKey(appId, transactionId);
        SetParams params = SetParams.setParams().ex(commonConfig.getConsumeIdempotent());
        String value = redisClient.set(key, String.valueOf(transactionId), params);
        return StringUtils.isNotBlank(value);
    }

}
