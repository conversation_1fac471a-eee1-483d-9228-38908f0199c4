package fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.manager;

import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityFlowResourceAuditBean;
import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.datastore.dao.ActivityApplyFlowResourceDao;
import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.datastore.entity.ActivityApplyFlowResource;
import fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.convert.ActivityApplyFlowResourceConvert;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.manager.ActivityApplyFlowResourceManager;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class ActivityApplyFlowResourceManagerImpl implements ActivityApplyFlowResourceManager {

    @Autowired
    private ActivityApplyFlowResourceDao flowResourceDao;

    @Override
    public boolean batchUpdateFlowResource(Long activityId, List<ActivityFlowResourceAuditBean> flowResources) {
        if (CollectionUtils.isEmpty(flowResources)) {
            return true;
        }
        //数据转换
        List<ActivityApplyFlowResource> activityFlowResourceList = ActivityApplyFlowResourceConvert.I.toActivityFlowResourceList(flowResources);
        return flowResourceDao.batchUpdateRecord(activityId, activityFlowResourceList);
    }
}
