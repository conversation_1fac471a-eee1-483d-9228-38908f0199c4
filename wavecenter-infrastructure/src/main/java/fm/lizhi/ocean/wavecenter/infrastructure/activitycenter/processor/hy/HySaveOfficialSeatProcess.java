package fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.processor.hy;

import com.google.common.collect.Lists;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.processor.ISaveOfficialSeatProcess;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.DeleteOfficialSeatParamDTO;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.HyOfficialSeatDetailDTO;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.SaveOfficialSeatParamDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

@Component
public class HySaveOfficialSeatProcess implements ISaveOfficialSeatProcess<HyOfficialSeatDetailDTO> {

    @Override
    public HyOfficialSeatDetailDTO buildSaveDTO(SaveOfficialSeatParamDTO param, HyOfficialSeatDetailDTO sameData) {
        HyOfficialSeatDetailDTO detailDto = new HyOfficialSeatDetailDTO();
        detailDto.setPosition(param.getPosition());
        detailDto.setUserIds(StringUtils.join(param.getUserIds(), ","));
        detailDto.setOperator("creator");
        detailDto.setStartTime(param.getStartTime().getTime());
        detailDto.setEndTime(param.getEndTime().getTime());
        detailDto.setBgImageUrl(param.getBackgroundUrl());
        detailDto.setTabId(param.getTabId());
        detailDto.setType(param.getExtra().getActivityType());
        detailDto.setRemark(param.getExtra().getNote());
        return detailDto;
    }

    @Override
    public HyOfficialSeatDetailDTO buildDeleteDTO(DeleteOfficialSeatParamDTO param, HyOfficialSeatDetailDTO sameData) {
        String userIds = sameData.getUserIds();
        List<Long> userIdList = Lists.newArrayList(userIds.split("\n")).stream().map(Long::parseLong).collect(Collectors.toList());
        //删除userIdList中和njId相等的元素
        userIdList.removeIf(userId -> userId.equals(param.getNjId()));
        sameData.setUserIds(StringUtils.join(userIdList, "\n"));
        return sameData;
    }

    @Override
    public Pair<Boolean, String> checkDataValid(SaveOfficialSeatParamDTO param, HyOfficialSeatDetailDTO sameData) {
        return Pair.of(Boolean.TRUE, "");
    }

    @Override
    public boolean isDirectDeleteSeatRecord(DeleteOfficialSeatParamDTO param, HyOfficialSeatDetailDTO seatData) {
        String userIds = seatData.getUserIds();
        boolean hasGroupId = seatData.getUserGroupId() != null && seatData.getUserGroupId() > 0;
        List<Long> userIdList = Lists.newArrayList(userIds.split("\n")).stream().map(Long::parseLong).collect(Collectors.toList());
        //如果只配置了userId列表，且只有一个userId，则直接删除
        if (CollectionUtils.isEmpty(userIdList) && !hasGroupId) {
            return true;
        }
        return userIdList.size() == 1 && userIdList.get(0).equals(param.getNjId());
    }

    @Override
    public BusinessEvnEnum getBusinessEnv() {
        return BusinessEvnEnum.HEI_YE;
    }
}
