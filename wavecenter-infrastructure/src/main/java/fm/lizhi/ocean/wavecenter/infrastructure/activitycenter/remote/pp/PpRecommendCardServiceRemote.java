package fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.remote.pp;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.live.room.recommendation.api.PpRecommendationCardManageService;
import fm.lizhi.live.room.recommendation.protocol.PpRecommendationCardManageServiceProto;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.remote.IRecommendCardServiceRemote;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.SendRecommendCardParamDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class PpRecommendCardServiceRemote implements IRecommendCardServiceRemote {

    @Autowired
    private PpRecommendationCardManageService recommendationCardManageService;

    @Override
    public Result<Void> sendRecommendCard(SendRecommendCardParamDTO param) {
        PpRecommendationCardManageServiceProto.SendUserRecommendationCardRequest cardOperationParam = PpRecommendationCardManageServiceProto.SendUserRecommendationCardRequest.newBuilder()
                .setReason(param.getReason())
                .setUserId(param.getUserId())
                .setNums(param.getCount())
                .setOperator("ADMIN")
                .build();

        Result<PpRecommendationCardManageServiceProto.ResponseSendUserRecommendationCard> result = recommendationCardManageService.sendUserRecommendationCard(cardOperationParam);
        if (RpcResult.isFail(result)) {
            log.warn("pp.sendRecommendCard fail. rCode={}, userId={}, count={}", result.rCode(), param.getUserId(), param.getCount());
            return RpcResult.fail(SEND_RECOMMEND_CARD_FAIL);
        }
        return RpcResult.success();
    }

    @Override
    public BusinessEvnEnum getBusinessEvnEnum() {
        return BusinessEvnEnum.PP;
    }
}
