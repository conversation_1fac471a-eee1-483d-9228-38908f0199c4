package fm.lizhi.ocean.wavecenter.infrastructure.award.family.convert;

import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.ocean.wavecenter.common.convert.CommonConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.award.family.manager.deliver.DeliverResourceParam;
import fm.lizhi.ocean.wavecenter.infrastructure.resource.newroom.remote.request.RequestSetNewRoomConfig;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.Date;

@Mapper(unmappedTargetPolicy = ReportingPolicy.ERROR,
        uses = {
                CommonConvert.class,
        },
        imports = {
                ConfigUtils.class,
                Date.class,
        }
)
public interface FamilyAwardNewRoomConvert {

    FamilyAwardNewRoomConvert I = Mappers.getMapper(FamilyAwardNewRoomConvert.class);

    @Mapping(target = "limit", source = "resourceNumber", defaultValue = "0")
    RequestSetNewRoomConfig toRequestSetNewRoomConfig(DeliverResourceParam param);
}
