package fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.convert;

import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.datastore.entity.ActivityApplyDecorate;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.ActivityApplyDecorateDTO;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/16 17:28
 */
@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ActivityApplyDecorateConvert {

    ActivityApplyDecorateConvert I = Mappers.getMapper(ActivityApplyDecorateConvert.class);

    ActivityApplyDecorateDTO entityToDTO(ActivityApplyDecorate entity);

    List<ActivityApplyDecorateDTO> entitysToDTOs(List<ActivityApplyDecorate> entitys);

}
