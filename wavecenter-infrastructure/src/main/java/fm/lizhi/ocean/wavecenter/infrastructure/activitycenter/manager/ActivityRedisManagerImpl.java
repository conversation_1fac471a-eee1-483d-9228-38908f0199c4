package fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.manager;

import fm.lizhi.common.datastore.redis.client.RedisClient;
import fm.lizhi.ocean.wavecenter.common.constants.TimeConstant;
import fm.lizhi.ocean.wavecenter.common.utils.DateTimeUtils;
import fm.lizhi.ocean.wavecenter.common.utils.RedisLock;
import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.constants.ActivityRedisKey;
import fm.lizhi.ocean.wavecenter.service.activitycenter.manager.ActivityRedisManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;

@Slf4j
@Component
public class ActivityRedisManagerImpl implements ActivityRedisManager {

    @Resource(name = "redis_ocean_wavecenter")
    private RedisClient redisClient;

    @Override
    public RedisLock getApplyLock(Integer appId, Long userId) {
        int timeout = TimeConstant.ONE_MINUTE * 1000;
        return new RedisLock(redisClient, ActivityRedisKey.APPLY_LOCK_STR.getKey(appId, userId), timeout / 3, timeout);
    }

    @Override
    public boolean applyCountIncrementByWeekly(Integer appId, Long njId, Date startTime) {
        String key = getApplyCountKey(appId, njId, startTime);
        Long count = redisClient.incr(key);
        if (count != null) {
            redisClient.expire(key, TimeConstant.ONE_WEEK);
        }
        return count != null;
    }

    public boolean applyCountDecrementByWeekly(Integer appId, Long njId, Date startTime) {
        try {
            String key = getApplyCountKey(appId, njId, startTime);
            Long count = redisClient.decr(key);
            if (count != null) {
                redisClient.expire(key, TimeConstant.ONE_WEEK);
            }
            return count != null;
        } catch (Exception e) {
            log.warn("applyCountDecrementByWeekly error", e);
            return false;
        }
    }

    @Override
    public Integer getApplyCountByWeekly(Integer appId, Long njId, Date startTime) {
        String value = redisClient.get(getApplyCountKey(appId, njId, startTime));
        return value == null ? 0 : Integer.parseInt(value);
    }

    @Override
    public RedisLock getResourceGiveLock(Integer appId, Long resourceId) {
        int timeout = TimeConstant.ONE_MINUTE * 1000;
        return new RedisLock(redisClient, getResourceGiveLockKey(appId, resourceId), timeout / 3, timeout);
    }

    @Override
    public RedisLock getActivityOperateLock(Integer appId, Long activityId) {
        int timeout = TimeConstant.ONE_MINUTE;
        return new RedisLock(redisClient, getActivityAgreeLockKey(appId, activityId), timeout / 3, timeout);
    }

    /**
     * 获取申请次数key
     *
     * @param appId     应用ID
     * @param njId      主播ID
     * @param startTime 活动开始时间
     * @return 结果
     */
    private String getApplyCountKey(Integer appId, Long njId, Date startTime) {
        int weekOfYear = DateTimeUtils.getWeekOfYear(startTime);
        return ActivityRedisKey.HALL_ACTIVITY_APPLY_COUNT_STR.getKey(appId, njId, weekOfYear);
    }

    private String getResourceGiveLockKey(Integer appId, Long resourceId) {
        return ActivityRedisKey.RESOURCE_GIVE_LOCK_STR.getKey(appId, resourceId);
    }

    private String getActivityAgreeLockKey(Integer appId, Long activityId) {
        return ActivityRedisKey.ACTIVITY_AGREE_LOCK_STR.getKey(appId, activityId);
    }

    @Override
    public RedisLock getModifyLock(Integer appId, Long activityId) {
        int timeout = TimeConstant.ONE_MINUTE;
        return new RedisLock(redisClient, getModifyLockKey(appId, activityId), timeout / 3, timeout);
    }

    private String getModifyLockKey(Integer appId, Long activityId) {
        return ActivityRedisKey.ACTIVITY_MODIFY_LOCK_STR.getKey(appId, activityId);
    }
}
