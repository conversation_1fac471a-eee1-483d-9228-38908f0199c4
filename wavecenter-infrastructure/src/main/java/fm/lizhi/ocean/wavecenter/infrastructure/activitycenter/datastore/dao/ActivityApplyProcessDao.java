package fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.datastore.dao;

import cn.hutool.core.collection.CollectionUtil;
import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.datastore.entity.ActivityApplyProcess;
import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.datastore.entity.ActivityApplyProcessExample;
import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.datastore.mapper.ActivityApplyProcessMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;

@Repository
public class ActivityApplyProcessDao {


    @Autowired
    private ActivityApplyProcessMapper processMapper;

    /**
     * 批量保存申请流程
     *
     * @param processes 流程列表
     * @return 结果
     */
    public boolean saveActivityApplyProcess(List<ActivityApplyProcess> processes) {
        if (CollectionUtil.isEmpty(processes)) {
            return true;
        }
        return processMapper.batchInsert(processes) == processes.size();
    }

    /**
     * 获取活动流程环节
     */
    public List<ActivityApplyProcess> getActivityProcessByActivityId(Long activityId) {
        if (activityId == null) {
            return Collections.emptyList();
        }

        ActivityApplyProcessExample example = new ActivityApplyProcessExample();
        example.createCriteria().andActivityIdEqualTo(activityId);
        example.setOrderByClause("`index` asc");

        return processMapper.selectByExample(example);
    }

    public long batchDeleteRecord(Long id) {
        ActivityApplyProcessExample example = new ActivityApplyProcessExample();
        example.createCriteria().andActivityIdEqualTo(id);
        return processMapper.deleteByExample(example);
    }
}
