package fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.datastore.ext;

import fm.lizhi.common.datastore.core.annotation.DataStore;
import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.datastore.entity.ActivityResourceTransfer;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 */
@DataStore(namespace = "mysql_ocean_wavecenter")
public interface ActivityResourceTransferExtMapper {

    /**
     * 根据实体对象的字段值查询多条记录，查询条件会跳过NULL值的字段。
     *
     * @param records  实体对象
     * @return  实体对象列表
     */
    @Insert({
            "<script>",
            "INSERT IGNORE INTO activity_resource_transfer (id, app_id, source_uri, target_uri, status, create_time, update_time) VALUES",
            "<foreach collection='records' item='record' separator=','>",
            "(#{record.id}, #{record.appId}, #{record.sourceUri}, #{record.targetUri}, #{record.status}, #{record.createTime}, #{record.updateTime})",
            "</foreach>",
            "</script>"
    })
    int batchInsertIgnore(@Param("records") List<ActivityResourceTransfer> records);

    @Update({
            "<script>",
            "INSERT INTO activity_resource_transfer (id, app_id, source_uri, target_uri, status,try_count, create_time, update_time) VALUES",
            "(#{record.id}, #{record.appId}, #{record.sourceUri}, #{record.targetUri}, #{record.status}, #{record.tryCount}, #{record.createTime}, #{record.updateTime})",
            "ON DUPLICATE KEY UPDATE " +
            "status = #{record.status}, " +
            "update_time = #{record.updateTime}, " +
            "target_uri = #{record.targetUri}, " +
            "try_count = CASE WHEN status = 0 THEN try_count + 1 ELSE try_count END ",
            "</script>"
    })
    int insertOrUpdate(@Param("record") ActivityResourceTransfer record);

}
