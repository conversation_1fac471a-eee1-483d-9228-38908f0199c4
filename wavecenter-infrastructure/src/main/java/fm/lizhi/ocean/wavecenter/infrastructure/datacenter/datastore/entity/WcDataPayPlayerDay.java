package fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.entity;

import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.FieldNameConstants;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 支付流水主播日收入数据
 *
 * @date 2025-04-24 02:52:57
 */
@FieldNameConstants
@Table(name = "`wavecenter_data_pay_player_day`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WcDataPayPlayerDay {
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 业务
     */
    @Column(name= "`app_id`")
    private Integer appId;

    /**
     * 日期 格式 YYYY-MM-DD
     */
    @Column(name= "`stat_date`")
    private Date statDate;

    /**
     * 日期 格式  YYYYMMDD
     */
    @Column(name= "`stat_date_value`")
    private Integer statDateValue;

    /**
     * 主播ID
     */
    @Column(name= "`player_id`")
    private Long playerId;

    /**
     * 厅ID
     */
    @Column(name= "`room_id`")
    private Long roomId;

    /**
     * 公会ID
     */
    @Column(name= "`family_id`")
    private Long familyId;

    /**
     * 收入,考核期间总收入，单位：钻（结算币)
     */
    @Column(name= "`income`")
    private BigDecimal income;

    /**
     * 签约厅收礼收入
     */
    @Column(name= "`sign_hall_income`")
    private BigDecimal signHallIncome;

    /**
     * 官方厅收礼收入
     */
    @Column(name= "`official_hall_income`")
    private BigDecimal officialHallIncome;

    /**
     * 个播收礼收入
     */
    @Column(name= "`personal_hall_income`")
    private BigDecimal personalHallIncome;

    /**
     * 个人收入
     */
    @Column(name= "`personal_income`")
    private BigDecimal personalIncome;

    /**
     * 个播贵族收入
     */
    @Column(name= "`personal_noble_income`")
    private BigDecimal personalNobleIncome;

    /**
     * 创建时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name= "`modify_time`")
    private Date modifyTime;

    /**
     * 服务部署环境, TEST/PRE/PRO
     */
    @Column(name= "`deploy_env`")
    private String deployEnv;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", appId=").append(appId);
        sb.append(", statDate=").append(statDate);
        sb.append(", statDateValue=").append(statDateValue);
        sb.append(", playerId=").append(playerId);
        sb.append(", roomId=").append(roomId);
        sb.append(", familyId=").append(familyId);
        sb.append(", income=").append(income);
        sb.append(", signHallIncome=").append(signHallIncome);
        sb.append(", officialHallIncome=").append(officialHallIncome);
        sb.append(", personalHallIncome=").append(personalHallIncome);
        sb.append(", personalIncome=").append(personalIncome);
        sb.append(", personalNobleIncome=").append(personalNobleIncome);
        sb.append(", createTime=").append(createTime);
        sb.append(", modifyTime=").append(modifyTime);
        sb.append(", deployEnv=").append(deployEnv);
        sb.append("]");
        return sb.toString();
    }
}