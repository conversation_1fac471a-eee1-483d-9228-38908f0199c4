package fm.lizhi.ocean.wavecenter.infrastructure.permissions.convert;

import fm.lizhi.ocean.wavecenter.infrastructure.permissions.datastore.entity.WcRoleComponent;
import fm.lizhi.ocean.wavecenter.service.permissions.constants.PermissionTypeEnum;
import fm.lizhi.ocean.wavecenter.service.permissions.dto.ComponentDto;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/27 18:22
 */
@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ComponentConvert {

    ComponentConvert I = Mappers.getMapper(ComponentConvert.class);

    @Mappings({
            @Mapping(source = "po", target = "permissionType", qualifiedByName = "convertPermissionType")
    })
    ComponentDto roleRefPo2Dto(WcRoleComponent po);

    List<ComponentDto> roleRefPos2Dtos(List<WcRoleComponent> pos);

    @Named("convertPermissionType")
    default PermissionTypeEnum convertPermissionType(WcRoleComponent po){
        if (po == null) {
            return null;
        }
        if (po.getPermissionType() == null) {
            return null;
        }
        return PermissionTypeEnum.from(po.getPermissionType());
    }

}
