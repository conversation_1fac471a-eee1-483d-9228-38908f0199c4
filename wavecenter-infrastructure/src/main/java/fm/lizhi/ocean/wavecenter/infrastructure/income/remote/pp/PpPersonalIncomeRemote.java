package fm.lizhi.ocean.wavecenter.infrastructure.income.remote.pp;


import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.income.bean.*;
import fm.lizhi.ocean.wavecenter.infrastructure.common.constants.PaySettleConfigCodeEnum;
import fm.lizhi.ocean.wavecenter.infrastructure.income.manager.PaymentManager;
import fm.lizhi.ocean.wavecenter.infrastructure.income.remote.PersonalIncomeRemote;
import fm.lizhi.pay.settle.settleenum.creativecenter.PeriodTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class PpPersonalIncomeRemote implements PersonalIncomeRemote {

    @Autowired
    private PaymentManager paymentManager;

    @Override
    public PlayerSumDataBean getPlayerSumData(String tenantCode, long familyId, long userId) {
        PlayerSumDataBean personalHall = new PlayerSumDataBean();

        long todayPersonalRoyaltyBalance = paymentManager.queryTradeStatisticsValue(familyId, userId, PaySettleConfigCodeEnum.PERSONAL_ANCHOR_INDIVIDUAL_INCOME_TOTAL_AMOUNT, PeriodTypeEnum.TODAY);
        PlayerSumTimeBean todayPersonalHall = new PlayerSumTimeBean();
        todayPersonalHall.setSumIncome(String.valueOf(todayPersonalRoyaltyBalance));
        personalHall.setDay(todayPersonalHall);

        long weekPersonalRoyaltyBalance = paymentManager.queryTradeStatisticsValue(familyId, userId, PaySettleConfigCodeEnum.PERSONAL_ANCHOR_INDIVIDUAL_INCOME_TOTAL_AMOUNT, PeriodTypeEnum.CURRENT_WEEK);
        PlayerSumTimeBean weekPersonalHall = new PlayerSumTimeBean();
        weekPersonalHall.setSumIncome(String.valueOf(weekPersonalRoyaltyBalance));
        personalHall.setWeek(weekPersonalHall);

        long monthPersonalRoyaltyBalance = paymentManager.queryTradeStatisticsValue(familyId, userId, PaySettleConfigCodeEnum.PERSONAL_ANCHOR_INDIVIDUAL_INCOME_TOTAL_AMOUNT, PeriodTypeEnum.CURRENT_MONTH);
        PlayerSumTimeBean monthPersonalHall = new PlayerSumTimeBean();
        monthPersonalHall.setSumIncome(String.valueOf(monthPersonalRoyaltyBalance));
        personalHall.setMonth(monthPersonalHall);
        return personalHall;
    }


    @Override
    public PlayerRevenueSumDataBean getPlayerRevenueSumData(String tenantCode, long familyId, long userId) {
        return null;
    }

    @Override
    public PersonalIncomeDetailSumBean getRevenueIncomeDetailSum(GetPersonalRevenueIncomeDetailParamBean paramBean) {
        return new PersonalIncomeDetailSumBean();
    }

    @Override
    public PageBean<PersonalIncomeDetailBean> getRevenueIncomeDetail(GetPersonalRevenueIncomeDetailParamBean paramBean) {
        return PageBean.empty();
    }

    @Override
    public String getIncomeRatio(long njId) {
        return null;
    }

    @Override
    public BusinessEvnEnum getBusinessEvnEnum() {
        return BusinessEvnEnum.PP;
    }
}
