package fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.remote.xm;


import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.convert.XmActivityApplyConvert;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.DeleteWaveActivityToApplyRecordDTO;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.SyncWaveActivityToApplyRecordDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import xm.fm.lizhi.live.pp.dto.officialseat.DeleteApplyRecordReq;
import xm.fm.lizhi.live.pp.dto.officialseat.DeleteApplyRecordResp;
import xm.fm.lizhi.live.pp.dto.officialseat.SyncWaveActivityToApplyRecordReq;
import xm.fm.lizhi.live.pp.dto.officialseat.SyncWaveActivityToApplyRecordResp;
import xm.fm.lizhi.live.pp.services.OfficialSeatService;

/**
 * 西米节目单
 * 仅有西米会有，其他业务后续是读创作者的接口
 */
@Slf4j
@Component
public class XmActivityServiceRemote {

    @Autowired
    private OfficialSeatService officialSeatService;

    /**
     * 同步平台活动到西米节目单记录中
     *
     * @param dto 参数
     * @return 结果
     */
    public Result<Long> syncWaveActivityToApplyRecord(SyncWaveActivityToApplyRecordDTO dto) {
        SyncWaveActivityToApplyRecordReq req = XmActivityApplyConvert.I.buildSyncReq(dto);
        req.setSource(1);
        Result<SyncWaveActivityToApplyRecordResp> result = officialSeatService.syncWaveActivityToApplyRecord(req);
        if (RpcResult.isFail(result)) {
            log.warn("syncWaveActivityToApplyRecord fail, rCode={}, dto={}", result.rCode(), JsonUtil.dumps(dto));
            //失败了直接让
            return RpcResult.fail(SYNC_WAVE_ACTIVITY_TO_APPLY_RECORD_FAIL);
        }
        SyncWaveActivityToApplyRecordResp target = result.target();
        if (target != null && target.getCode() != 0) {
            log.warn("syncWaveActivityToApplyRecord fail, rCode={},errorMsg={}, activityTheme={}",
                    result.target().getCode(), target.getMessage(), dto.getActivityTheme());
            //失败了直接让
            return RpcResult.fail(SYNC_WAVE_ACTIVITY_TO_APPLY_RECORD_FAIL, target.getMessage());
        }
        return RpcResult.success(target == null ? 0 : target.getId());
    }

    /**
     * 删除同步活动到报名记录
     *
     * @param dto 同步参数
     * @return 结果
     */
    public Result<Void> deleteWaveActivityToApplyRecord(DeleteWaveActivityToApplyRecordDTO dto) {
        DeleteApplyRecordReq req = new DeleteApplyRecordReq();
        req.setId(dto.getId());
        req.setOperator("creator");
        Result<DeleteApplyRecordResp> result = officialSeatService.deleteApplyRecord(req);
        if (RpcResult.isFail(result)) {
            log.warn("deleteWaveActivityToApplyRecord fail, rCode={}, dto={}", result.rCode(), JsonUtil.dumps(dto));
            return RpcResult.fail(DELETE_WAVE_ACTIVITY_TO_APPLY_RECORD_FAIL);
        }
        DeleteApplyRecordResp resp = result.target();
        if (resp.getCode() == 11) {
            //不存在，也算成功
            return RpcResult.success();
        }
        if (resp.getCode() != 0) {
            log.warn("deleteWaveActivityToApplyRecord fail, rCode={},errorMsg={}, id={}",
                    resp.getCode(), resp.getMessage(), dto.getId());
            //失败了直接让
            return RpcResult.fail(DELETE_WAVE_ACTIVITY_TO_APPLY_RECORD_FAIL, resp.getMessage());
        }
        return RpcResult.success();
    }

    int SYNC_WAVE_ACTIVITY_TO_APPLY_RECORD_FAIL = 1;
    int DELETE_WAVE_ACTIVITY_TO_APPLY_RECORD_FAIL = 2;

}
