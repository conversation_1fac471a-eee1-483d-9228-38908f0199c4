package fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.convert;

import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.datastore.entity.ActivityAnnouncementDeployRecord;
import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.datastore.entity.ActivityDressUpGiveRecord;
import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.datastore.entity.ActivityFlowResourceGiveRecord;
import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.datastore.entity.ActivityResourceGiveRecord;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.ActivityDressUpGiveDTO;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.ActivityFlowResourceGiveDTO;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.ActivityResourceGiveDTO;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.ActivityRoomAnnouncementDeployDTO;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ActivityMaterielGiveConvert {

    ActivityMaterielGiveConvert I = Mappers.getMapper(ActivityMaterielGiveConvert.class);

    List<ActivityResourceGiveDTO> convertResourceBeans2DTOs(List<ActivityResourceGiveRecord> recordList);
    List<ActivityDressUpGiveDTO> convertDressUpBean2DTO(List<ActivityDressUpGiveRecord> recordList);

    List<ActivityFlowResourceGiveDTO> convertFlowResourceBean2DTO(List<ActivityFlowResourceGiveRecord> recordList);

    List<ActivityRoomAnnouncementDeployDTO> convertAnnouncementDeployBean2DTO(List<ActivityAnnouncementDeployRecord> recordList);

    ActivityResourceGiveDTO convertResourceBean2DTO(ActivityResourceGiveRecord activityResourceGiveRecord);

}
