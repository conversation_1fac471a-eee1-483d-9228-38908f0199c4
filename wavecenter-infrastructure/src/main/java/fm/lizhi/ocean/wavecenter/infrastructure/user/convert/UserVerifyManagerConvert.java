package fm.lizhi.ocean.wavecenter.infrastructure.user.convert;

import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import fm.lizhi.ocean.wavecenter.infrastructure.user.remote.req.GetUserVerifyResultReq;
import fm.lizhi.ocean.wavecenter.infrastructure.user.remote.req.SearchUserVerifyResultReq;
import fm.lizhi.ocean.wavecenter.infrastructure.user.remote.res.GetUserVerifyResultRes;
import fm.lizhi.ocean.wavecenter.infrastructure.user.remote.res.SearchUserVerifyResultRes;
import fm.lizhi.ocean.wavecenter.service.user.dto.GetUserVerifyResultDTO;
import fm.lizhi.ocean.wavecenter.service.user.dto.GetUserVerifyResultParamDTO;
import fm.lizhi.ocean.wavecenter.service.user.dto.SearchUserVerifyResultDTO;
import fm.lizhi.ocean.wavecenter.service.user.dto.SearchUserVerifyResultParamDTO;

@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.ERROR,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface UserVerifyManagerConvert {

    UserVerifyManagerConvert I = Mappers.getMapper(UserVerifyManagerConvert.class);

    /**
     * 查询认证结果参数转换
     */

    GetUserVerifyResultReq getUserVerifyResultParamDTO2Pb(GetUserVerifyResultParamDTO param);

    SearchUserVerifyResultReq searchUserVerifyResultParamDTO2Pb(SearchUserVerifyResultParamDTO param);

    GetUserVerifyResultDTO verifyResultRes2DTO(GetUserVerifyResultRes res);

    SearchUserVerifyResultDTO searchVerifyResultRes2DTO(SearchUserVerifyResultRes res);

}
