package fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.convert;

import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.SyncWaveActivityToApplyRecordDTO;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;
import xm.fm.lizhi.live.pp.dto.officialseat.SyncWaveActivityToApplyRecordReq;

@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface XmActivityApplyConvert {

    XmActivityApplyConvert I = Mappers.getMapper(XmActivityApplyConvert.class);

    /**
     * 构建同步参数
     * @param dto
     * @return
     */
    @Mappings({
            @Mapping(target = "id", ignore = true)
    })
    SyncWaveActivityToApplyRecordReq buildSyncReq(SyncWaveActivityToApplyRecordDTO dto);

}
