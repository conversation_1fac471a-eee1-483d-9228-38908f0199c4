package fm.lizhi.ocean.wavecenter.infrastructure.permissions.remote.pp;

import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.infrastructure.permissions.remote.LiveRoomRoleRemote;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/10 16:01
 */
@Component
public class PpLiveRoomRoleRemote implements LiveRoomRoleRemote {
    @Override
    public BusinessEvnEnum getBusinessEvnEnum() {
        return BusinessEvnEnum.PP;
    }

    @Override
    public List<Long> getSuperAdminUserIds(List<Long> njIds) {
        return Collections.emptyList();
    }
}
