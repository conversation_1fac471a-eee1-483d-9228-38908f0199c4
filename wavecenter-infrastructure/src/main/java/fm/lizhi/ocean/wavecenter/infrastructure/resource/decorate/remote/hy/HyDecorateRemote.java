package fm.lizhi.ocean.wavecenter.infrastructure.resource.decorate.remote.hy;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.hy.amusement.api.DressUpGoodsService;
import fm.lizhi.hy.amusement.api.DressUpInfoService;
import fm.lizhi.hy.amusement.protocol.DressUpGoodsProto;
import fm.lizhi.hy.amusement.protocol.DressUpGoodsProto.QueryDressUpGoodsParam;
import fm.lizhi.hy.amusement.protocol.DressUpGoodsProto.ResponseQueryDressUpGoods;
import fm.lizhi.hy.amusement.protocol.DressUpInfoProto.DressUpInfo;
import fm.lizhi.hy.amusement.protocol.DressUpInfoProto.ResponseGetDressUpListByIds;
import fm.lizhi.hy.vip.api.HyMedalInfoV2Service;
import fm.lizhi.hy.vip.api.HyMedalSendScheduledV2Service;
import fm.lizhi.hy.vip.protocol.HyMedalBaseV2Proto;
import fm.lizhi.hy.vip.protocol.HyMedalBaseV2Proto.DelMedalParams;
import fm.lizhi.hy.vip.protocol.HyMedalBaseV2Proto.MedalInfoV2;
import fm.lizhi.hy.vip.protocol.HyMedalInfoV2Proto.ResponseMedalInfoList;
import fm.lizhi.hy.vip.protocol.HyMedalSendScheduledV2Proto;
import fm.lizhi.live.pp.idl.officialCertifiedTag.api.OfficialCertifiedTagIDLService;
import fm.lizhi.live.pp.idl.officialCertifiedTag.api.OfficialCertifiedTagRecordIDLService;
import fm.lizhi.live.pp.idl.officialCertifiedTag.constant.OfficialCertifiedTagOperationChannelEnum;
import fm.lizhi.live.pp.idl.officialCertifiedTag.constant.OfficialCertifiedTagOperationTypeEnum;
import fm.lizhi.live.pp.idl.officialCertifiedTag.dto.record.OperateOfficialCertifiedTagRequest;
import fm.lizhi.live.pp.idl.officialCertifiedTag.dto.record.OperateOfficialCertifiedTagResponse;
import fm.lizhi.live.pp.idl.officialCertifiedTag.dto.tag.GetOfficialCertifiedTagDetailRequest;
import fm.lizhi.live.pp.idl.officialCertifiedTag.dto.tag.GetOfficialCertifiedTagDetailResponse;
import fm.lizhi.live.pp.idl.officialCertifiedTag.dto.tag.ListOfficialCertifiedTagRequest;
import fm.lizhi.live.pp.idl.officialCertifiedTag.dto.tag.ListOfficialCertifiedTagResponse;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.common.service.CommonService;
import fm.lizhi.ocean.wavecenter.api.resource.constants.PlatformDecorateTypeEnum;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.bean.DecorateInfoBean;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.common.config.CommonConfig;
import fm.lizhi.ocean.wavecenter.infrastructure.resource.decorate.constants.HyDecorateTypeEnum;
import fm.lizhi.ocean.wavecenter.infrastructure.resource.decorate.convert.hy.HyDecorateInfoConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.resource.decorate.remote.DecorateRemote;
import fm.lizhi.ocean.wavecenter.infrastructure.resource.decorate.remote.request.RequestRecoverDecorate;
import fm.lizhi.ocean.wavecenter.infrastructure.resource.decorate.remote.request.RequestSendDecorate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component
public class HyDecorateRemote implements DecorateRemote {

    @Autowired
    private DressUpGoodsService dressUpGoodsService;

    @Autowired
    private HyMedalInfoV2Service hyMedalInfoV2Service;

    @Autowired
    private DressUpInfoService dressUpInfoService;

    @Autowired
    private HyMedalSendScheduledV2Service hyMedalSendScheduledV2Service;

    @Autowired
    private CommonConfig commonConfig;

    @Autowired
    private OfficialCertifiedTagRecordIDLService officialCertifiedTagRecordIdlService;

    @Autowired
    private OfficialCertifiedTagIDLService officialCertifiedTagIdlService;

    @Override
    public BusinessEvnEnum getBusinessEvnEnum() {
        return BusinessEvnEnum.HEI_YE;
    }

    @Override
    public DecorateInfoBean getDecorateInfo(PlatformDecorateTypeEnum decorateType, Long decorateId) {
        HyDecorateTypeEnum hyDecorateTypeEnum = HyDecorateTypeEnum.getByType(decorateType);
        if (hyDecorateTypeEnum == null) {
            log.warn("getDecorateInfo fail. not support type, decorateType={},decorateId={}", decorateType, decorateId);
            return null;
        }

        if (hyDecorateTypeEnum.getType() == HyDecorateTypeEnum.USER_GLORY.getType()) {
            return getOfficialCertifiedTag(decorateId);
        }
        
        if (hyDecorateTypeEnum.getType() == HyDecorateTypeEnum.MEDAL.getType()) {
            return getMedalDecorateInfo(decorateId);
        }

        return getDecorateInfo(hyDecorateTypeEnum, decorateId);
    }

    @Override
    public PageBean<DecorateInfoBean> getDecorateInfoList(PlatformDecorateTypeEnum decorateType, Long decorateId, String decorateName,
            int pageNum, int pageSize) {
        HyDecorateTypeEnum hyDecorateTypeEnum = HyDecorateTypeEnum.getByType(decorateType);
        if (hyDecorateTypeEnum == null) {
            log.warn("getDecorateInfoList fail. not support type, decorateType={},decorateName={}", decorateType, decorateName);
            return new PageBean<>(0, Collections.emptyList());
        }

        if (hyDecorateTypeEnum.getType() == HyDecorateTypeEnum.MEDAL.getType()) {
            return getMedalDecorateInfoList(decorateName, decorateId, pageNum, pageSize);

        }

        if (hyDecorateTypeEnum.getType() == HyDecorateTypeEnum.USER_GLORY.getType()) {
            return listOfficialCertifiedTags(decorateName, decorateId, pageNum, pageSize);
        }

        return getDecorateList(hyDecorateTypeEnum, decorateId, decorateName, pageNum, pageSize);
    }

    @Override
    public List<DecorateInfoBean> batchGetDecorateInfo(PlatformDecorateTypeEnum decorateType, List<Long> decorateIds) {
        HyDecorateTypeEnum hyDecorateTypeEnum = HyDecorateTypeEnum.getByType(decorateType);
        if (hyDecorateTypeEnum == null) {
            log.warn("batchGetDecorateInfo fail. not support type, decorateType={},decorateIds={}", decorateType, decorateIds);
            return Collections.emptyList();
        }
        
        if (hyDecorateTypeEnum.getType() == HyDecorateTypeEnum.MEDAL.getType()) {
            throw new IllegalArgumentException("hy.batchGetDecorateInfo not support medal");
        }

        if (hyDecorateTypeEnum.getType() == HyDecorateTypeEnum.USER_GLORY.getType()) {
            throw new IllegalArgumentException("hy.batchGetDecorateInfo not support user glory");
        }
        
        Result<ResponseGetDressUpListByIds> result = dressUpInfoService.getDressUpListByIds(decorateIds);
        if (RpcResult.isFail(result)) { 
            log.warn("hy.batchGetDecorateInfo fail. getDressUpListByIds fail, decorateIds={}", decorateIds);
            return Collections.emptyList();
        }

        List<DressUpInfo> dressUpInfoList = result.target().getDressUpInfoList();
        return dressUpInfoList.stream()
                .map(x -> HyDecorateInfoConvert.convertToDecorateInfoBean(x, commonConfig.getBizConfig().getCdnHost(), decorateType))
                    .collect(Collectors.toList());
    }

    @Override
    public Result<Void> sendDecorate(RequestSendDecorate request) {
        PlatformDecorateTypeEnum decorateType = request.getDecorateType();
        switch (decorateType) {
            case VEHICLE:
            case AVATAR:
                return sendDecorateManager(request);
            case MEDAL:
                return sendMedal(request);
            case USER_GLORY:
                return sendUserGlory(request);
            default:
                return RpcResult.fail(CommonService.PARAM_ERROR, "hy暂未实现该装扮类型发放: " + decorateType);
        }
    }


    @Override
    public Result<Void> recoverDecorate(RequestRecoverDecorate request) {
        PlatformDecorateTypeEnum decorateType = request.getDecorateType();
        switch (decorateType) {
            case VEHICLE:
            case AVATAR:
                return recoverDecorateManager(request);
            case MEDAL:
                return recoverMedal(request);
            case USER_GLORY:
                return recoverUserGlory(request);
            default:
                return RpcResult.fail(CommonService.PARAM_ERROR, "hy暂未实现该装扮类型回收: " + decorateType);
        }
    }


    private DecorateInfoBean getMedalDecorateInfo(Long decorateId) {
        HyMedalBaseV2Proto.QueryParams param = HyMedalBaseV2Proto.QueryParams.newBuilder()
                .setMedalInfoId(decorateId)
                .setPageNo(1)
                .setPageSize(1)
                .build();
        Result<ResponseMedalInfoList> result = hyMedalInfoV2Service.medalInfoList(param);
        if (RpcResult.isFail(result)) {
            log.warn("hy.getMedalDecorateInfo fail. medalInfoList fail, decorateId={}", decorateId);
            return null;
        }
        if (CollectionUtils.isEmpty(result.target().getMedalInfosList())) {
            return null;
        }

        MedalInfoV2 medalInfo = result.target().getMedalInfosList().get(0);
        return HyDecorateInfoConvert.convertMedalToDecorateInfoBean(medalInfo, commonConfig.getBizConfig().getCdnHost(), PlatformDecorateTypeEnum.MEDAL);
    }

    private DecorateInfoBean getDecorateInfo(HyDecorateTypeEnum hyDecorateTypeEnum, Long decorateId) {
        QueryDressUpGoodsParam param = QueryDressUpGoodsParam.newBuilder()
                .setDressUpType(hyDecorateTypeEnum.getType())
                .setDressUpId(decorateId)
                .setPageNo(1)
                .setPageSize(1)
                .build();
        Result<ResponseQueryDressUpGoods> result = dressUpGoodsService.queryDressUpGoods(param);
        if (RpcResult.isFail(result)) {
            log.warn("hy.getDecorateInfo fail. queryDressUpGoods fail, decorateId={}, decorateType={}", decorateId, hyDecorateTypeEnum.getDecorateTypeEnum().getName());
            return null;
        }
        if (CollectionUtils.isEmpty(result.target().getDressUpGoodsList())) {
            return null;
        }
        DressUpGoodsProto.DressUpGoods dressUpGood = result.target().getDressUpGoodsList().get(0);

        return HyDecorateInfoConvert.convertToDecorateInfoBean(dressUpGood, commonConfig.getBizConfig().getCdnHost(), hyDecorateTypeEnum.getDecorateTypeEnum());
    }

    private PageBean<DecorateInfoBean> getDecorateList(HyDecorateTypeEnum hyDecorateTypeEnum, Long decorateId, String decorateName, int pageNum, int pageSize) {
        QueryDressUpGoodsParam.Builder builder = QueryDressUpGoodsParam.newBuilder()
                .setDressUpType(hyDecorateTypeEnum.getType())
                .setPageNo(pageNum)
                .setPageSize(pageSize);
        if (decorateId != null && decorateId > 0) {
            builder.setDressUpId(decorateId);
        }

        if (StringUtils.isNotBlank(decorateName)) {
            builder.setDressUpName(decorateName);
        }

        Result<ResponseQueryDressUpGoods> result = dressUpGoodsService.queryDressUpGoods(builder.build());
        if (RpcResult.isFail(result)) {
            log.warn("hy.getDecorateInfoList fail. queryDressUpGoods fail, decorateName={},pageNum={},pageSize={}", decorateName, pageNum, pageSize);
            return new PageBean<DecorateInfoBean>(0, Collections.emptyList());
        }
        List<DressUpGoodsProto.DressUpGoods> dressUpGoodsList = result.target().getDressUpGoodsList();
        List<DecorateInfoBean> decorateInfoBeanList = dressUpGoodsList.stream()
                .map(x -> HyDecorateInfoConvert.convertToDecorateInfoBean(x, commonConfig.getBizConfig().getCdnHost(), hyDecorateTypeEnum.getDecorateTypeEnum()))
                .collect(Collectors.toList());
        return new PageBean<DecorateInfoBean>((int)result.target().getTotal(), decorateInfoBeanList);
    }

    private PageBean<DecorateInfoBean> getMedalDecorateInfoList(String decorateName, Long decorateId, int pageNum, int pageSize) {
        HyMedalBaseV2Proto.QueryParams.Builder builder = HyMedalBaseV2Proto.QueryParams.newBuilder()
                .setStatus(1) //只查询已上架，确认工会等级需求是否可以这个条件
                .setPageNo(pageNum)
                .setPageSize(pageSize);

        if (decorateId != null && decorateId > 0) {
            builder.setMedalInfoId(decorateId);
        }
        if (StringUtils.isNotBlank(decorateName)) {
            builder.setMedalInfoName(decorateName);
        }
        Result<ResponseMedalInfoList> result = hyMedalInfoV2Service.medalInfoList(builder.build());
        if (RpcResult.isFail(result)) {
            log.warn("hy.getMedalDecorateInfoList fail. medalInfoList fail, decorateName={},pageNum={},pageSize={}", decorateName, pageNum, pageSize);
            return new PageBean<DecorateInfoBean>(0, Collections.emptyList());
        }
        List<MedalInfoV2> medalInfoList = result.target().getMedalInfosList();
        List<DecorateInfoBean> decorateInfoBeanList = medalInfoList.stream()
                .map(x -> HyDecorateInfoConvert.convertMedalToDecorateInfoBean(x, commonConfig.getBizConfig().getCdnHost(), PlatformDecorateTypeEnum.MEDAL))
                .collect(Collectors.toList());
        return new PageBean<DecorateInfoBean>(result.target().getTotalCount(), decorateInfoBeanList);
    }

    private Result<Void> recoverDecorateManager(RequestRecoverDecorate request) {
        Long userId = request.getUserId();
        Long decorateId = request.getDecorateId();
        Result<DressUpGoodsProto.ResponseDelUserRights> result = dressUpGoodsService.delUserRights(userId, decorateId);
        if (RpcResult.isFail(result)) {
            log.warn("hy.recoverDecorateManager fail. delUserRights fail, userId={},decorateId={},rCode={},msg={}",
                    userId, decorateId, result.rCode(), result.getMessage());
            return RpcResult.fail(result.rCode(), result.getMessage());
        }
        return RpcResult.success();
    }

    private Result<Void> recoverMedal(RequestRecoverDecorate request) {
        DelMedalParams delMedalParams = DelMedalParams.newBuilder()
                .setMedalInfoId(request.getDecorateId())
                .setUserId(request.getUserId())
                .build();
        Result<HyMedalSendScheduledV2Proto.ResponseDelUserMedal> result = hyMedalSendScheduledV2Service.delUserMedal(delMedalParams);
        if (RpcResult.isFail(result)) {
            log.warn("hy.recoverMedal fail. delUserMedal fail, userId={},decorateId={},rCode={},msg={}",
                    request.getUserId(), request.getDecorateId(), result.rCode(), result.getMessage());
            return RpcResult.fail(result.rCode(), result.getMessage());
        }
        return RpcResult.success();
    }

    private Result<Void> sendDecorateManager(RequestSendDecorate request) {
        Long userId = request.getUserId();
        Long decorateId = request.getDecorateId();
        Integer decorateNumber = request.getDecorateNumber();
        if (decorateNumber == null || decorateNumber <= 0) {
            // 黑叶座驾发放至少需要为1, 否则会发放失败
            decorateNumber = 1;
        }
        Result<DressUpGoodsProto.ResponseAddUserRights> result = dressUpGoodsService.addUserRights(userId, decorateId, decorateNumber);
        if (RpcResult.isFail(result)) {
            log.warn("hy.sendVehicle fail. addUserRights fail, userId={},decorateId={},decorateNumber={},rCode={},msg={}",
                    userId, decorateId, decorateNumber, result.rCode(), result.getMessage());
            return RpcResult.fail(result.rCode(), result.getMessage());
        }
        return RpcResult.success();
    }

    private Result<Void> sendMedal(RequestSendDecorate request) {
        int sendTypeImmediately = NumberUtils.INTEGER_ONE;
        HyMedalBaseV2Proto.SendMedalParams sendMedalParams = HyMedalBaseV2Proto.SendMedalParams.newBuilder()
                .setMedalInfoId(request.getDecorateId())
                .setSendType(sendTypeImmediately)
                .setSendMinute(request.getDecorateExpireTime())
                .setUserIds(String.valueOf(request.getUserId()))
                .build();
        Result<HyMedalSendScheduledV2Proto.ResponseSendUserMedal> result = hyMedalSendScheduledV2Service.sendUserMedal(sendMedalParams);
        if (RpcResult.isFail(result)) {
            log.warn("hy.sendMedal fail. sendUserMedal fail, rCode={},msg={},request={},sendMedalParams={}",
                    result.rCode(), result.getMessage(), request, sendMedalParams);
            return RpcResult.fail(result.rCode(), result.getMessage());
        }
        return RpcResult.success();
    }


    /**
     * 发送用户认证勋章
     */
    private Result<Void> sendUserGlory(RequestSendDecorate param) {
        OperateOfficialCertifiedTagRequest request = new OperateOfficialCertifiedTagRequest();
        request.setUserList(String.valueOf(param.getUserId()));
        request.setOperationType(OfficialCertifiedTagOperationTypeEnum.ISSUE.getType());
        request.setTagId(param.getDecorateId());
        request.setOperationChannel(OfficialCertifiedTagOperationChannelEnum.CREATOR_CENTER.getType());
        request.setOperator(OfficialCertifiedTagOperationChannelEnum.CREATOR_CENTER.getDesc());


        Result<OperateOfficialCertifiedTagResponse> result = officialCertifiedTagRecordIdlService.operateOfficialCertifiedTag(request);
        if (RpcResult.isFail(result)) {
            log.warn("hy.sendUserGlory fail. operateOfficialCertifiedTag fail, rCode={},msg={},request={}",
                    result.rCode(), result.getMessage(), request);
            return RpcResult.fail(result.rCode(), result.getMessage());
        }

        if (result.target().getCode() != 0) {
            log.warn("hy.sendUserGlory fail. operateOfficialCertifiedTag fail, bizCode={}, bizMsg={},request={}",
                    result.target().getCode(), result.target().getMessage(), request);
            return RpcResult.fail(result.target().getCode(), result.getMessage());
        }

        return RpcResult.success();
    }

    /**
     * 回收用户认证勋章
     */
    private Result<Void> recoverUserGlory(RequestRecoverDecorate param) {
        OperateOfficialCertifiedTagRequest request = new OperateOfficialCertifiedTagRequest();
        request.setUserList(String.valueOf(param.getUserId()));
        request.setOperationType(OfficialCertifiedTagOperationTypeEnum.REVOKE.getType());
        request.setTagId(param.getDecorateId());
        request.setOperationChannel(OfficialCertifiedTagOperationChannelEnum.CREATOR_CENTER.getType());
        request.setOperator(OfficialCertifiedTagOperationChannelEnum.CREATOR_CENTER.getDesc());


        Result<OperateOfficialCertifiedTagResponse> result = officialCertifiedTagRecordIdlService.operateOfficialCertifiedTag(request);
        if (RpcResult.isFail(result)) {
            log.warn("hy.recoverUserGlory fail. operateOfficialCertifiedTag fail, rCode={},msg={},request={}",
                    result.rCode(), result.getMessage(), request);
            return RpcResult.fail(result.rCode(), result.getMessage());
        }

        if (result.target().getCode() != 0) {
            log.warn("hy.recoverUserGlory fail. operateOfficialCertifiedTag fail, bizCode={}, bizMsg={},request={}",
                    result.target().getCode(), result.target().getMessage(), request);
            return RpcResult.fail(result.target().getCode(), result.getMessage());
        }

        return RpcResult.success();
    }


    /**
     * 获取官方认证标签
     */
    private PageBean<DecorateInfoBean> listOfficialCertifiedTags(String decorateName, Long decorateId, int pageNum, int pageSize) {
        ListOfficialCertifiedTagRequest request = new ListOfficialCertifiedTagRequest();
        request.setName(decorateName);
        request.setPageNum(pageNum);
        request.setPageSize(pageSize);
        request.setId(decorateId);

        Result<ListOfficialCertifiedTagResponse> result = officialCertifiedTagIdlService.listOfficialCertifiedTags(request);

        if (RpcResult.isFail(result)) {
            log.warn("hy listOfficialCertifiedTags fail., name:{}, pageNo:{}, pageSize:{}",
                    decorateName, pageNum, pageSize);
            return PageBean.empty();
        }
        ListOfficialCertifiedTagResponse target = result.target();
        List<DecorateInfoBean> list = HyDecorateInfoConvert.hyResponseOfficialCertifiedList2DecorateDTO(target.getList(),
                commonConfig.getBizConfig().getCdnHost(), PlatformDecorateTypeEnum.USER_GLORY);
        return PageBean.of(Math.toIntExact(result.target().getTotal()), list);
    }

    /**
     * 获取官方认证标签
     */
    private DecorateInfoBean getOfficialCertifiedTag(Long decorateId) {
        GetOfficialCertifiedTagDetailRequest request = new GetOfficialCertifiedTagDetailRequest();
        request.setId(decorateId);
        Result<GetOfficialCertifiedTagDetailResponse> result = officialCertifiedTagIdlService.getOfficialCertifiedTagDetail(request);
        if (RpcResult.isFail(result)) {
            log.warn("hy.getOfficialCertifiedTag fail. getOfficialCertifiedTagDetail fail, decorateId={}", decorateId);
            return null;
        }
        GetOfficialCertifiedTagDetailResponse target = result.target();
        return HyDecorateInfoConvert.convertUserGloryToDecorateInfoBean(target.getData(), commonConfig.getBizConfig().getCdnHost(), PlatformDecorateTypeEnum.USER_GLORY);
    }
}
