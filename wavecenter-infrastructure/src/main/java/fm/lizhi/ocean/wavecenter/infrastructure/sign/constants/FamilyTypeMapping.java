package fm.lizhi.ocean.wavecenter.infrastructure.sign.constants;

import fm.lizhi.ocean.wavecenter.api.sign.constant.FamilyTypeEnum;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.xm.family.constants.FamilyConstant;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.EnumMap;
import java.util.Map;

/**
 * <AUTHOR>
 */

@Getter
@AllArgsConstructor
public enum FamilyTypeMapping {

    /**
     * UGC
     */
    P_FAMILY(FamilyTypeEnum.P_FAMILY.getCode(), new EnumMap<BusinessEvnEnum, String>(BusinessEvnEnum.class) {
        {
            put(BusinessEvnEnum.PP, "P_FAMILY");
            put(BusinessEvnEnum.XIMI, FamilyConstant.FamilyType.UGC.getCode());
            put(BusinessEvnEnum.HEI_YE, fm.hy.family.constants.FamilyConstant.FamilyType.P_FAMILY.name());
        }
    }),

    /**
     * PGC
     */
    C_FAMILY(FamilyTypeEnum.C_FAMILY.getCode(), new EnumMap<BusinessEvnEnum, String>(BusinessEvnEnum.class) {
        {
            put(BusinessEvnEnum.PP, "C_FAMILY");
            put(BusinessEvnEnum.XIMI, FamilyConstant.FamilyType.PGC.getCode());
            put(BusinessEvnEnum.HEI_YE, fm.hy.family.constants.FamilyConstant.FamilyType.C_FAMILY.name());
        }
    }),

    ;

    private final String waveType;

    private final EnumMap<BusinessEvnEnum, String> bizValueMap;


    public static String bizValue2WaveType(String bizType) {
        BusinessEvnEnum businessEvnEnum = ContextUtils.getBusinessEvnEnum();
        FamilyTypeMapping[] values = values();
        for (FamilyTypeMapping value : values) {
            Map<BusinessEvnEnum, String> bizMap = value.getBizValueMap();
            String mBizValue = bizMap.get(businessEvnEnum);
            if (mBizValue != null && mBizValue.equals(bizType)) {
                return value.waveType;
            }
        }
        return null;
    }
}
