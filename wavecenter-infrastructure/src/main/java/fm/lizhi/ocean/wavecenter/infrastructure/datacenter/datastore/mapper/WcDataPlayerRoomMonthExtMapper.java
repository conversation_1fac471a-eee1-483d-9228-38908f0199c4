package fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.mapper;

import fm.lizhi.common.datastore.core.annotation.DataStore;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.entity.CountPlayerEntity;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 */
@DataStore(namespace = "mysql_ocean_wavecenter")
public interface WcDataPlayerRoomMonthExtMapper {

    @Select({
            "<script>"
            , "select count(distinct player_id) from wavecenter_data_player_room_month"
            , "where app_id=#{param.appId} and family_id=#{param.familyId} and stat_month=#{monthValue}"
            , "<if test='null != param.roomIds and param.roomIds.size > 0'>"
            , "and room_id in "
            , "<foreach collection='param.roomIds' item='roomId' open='(' separator=',' close=')'>"
            , "#{roomId}"
            , "</foreach>"
            , "</if>"
            , "<if test='null != param.upGuestDurMin'>"
            , "and up_guest_dur &gt; #{param.upGuestDurMin}"
            , "</if>"
            , "<if test='null != param.charmMin'>"
            , "and charm &gt; #{param.charmMin}"
            , "</if>"
            , "</script>"
    })
    int countPlayer(@Param("param") CountPlayerEntity param, @Param("monthValue") int monthValue);


}
