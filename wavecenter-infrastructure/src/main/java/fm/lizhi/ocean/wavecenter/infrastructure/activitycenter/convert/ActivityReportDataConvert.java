package fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.convert;

import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityReportDataDetailBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityReportDataGiftBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityReportDataPlayerBean;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityReportDataSummaryBean;
import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.datastore.entity.ActivityReportDataDetail;
import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.datastore.entity.ActivityReportDataGift;
import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.datastore.entity.ActivityReportDataPlayer;
import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.datastore.entity.ActivityReportDataSummary;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.SendDecorateParamDTO;
import fm.lizhi.xm.vip.bean.decorate.req.SendDecorateReq;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ActivityReportDataConvert {

    ActivityReportDataConvert I = Mappers.getMapper(ActivityReportDataConvert.class);

    ActivityReportDataSummaryBean convertActivityReportDataSummary(ActivityReportDataSummary summary);

    List<ActivityReportDataDetailBean> convertActivityReportDataDetailList(List<ActivityReportDataDetail> list);

    List<ActivityReportDataPlayerBean> convertActivityReportDataPlayerList(PageList<ActivityReportDataPlayer> pageList);

    List<ActivityReportDataGiftBean> convertActivityReportDataGiftList(PageList<ActivityReportDataGift> pageList);

    List<ActivityReportDataSummaryBean> convertActivityReportDataSummaryList(List<ActivityReportDataSummary> list);

    default Long dateToLong(Date date) {
        return date.getTime();
    }
}
