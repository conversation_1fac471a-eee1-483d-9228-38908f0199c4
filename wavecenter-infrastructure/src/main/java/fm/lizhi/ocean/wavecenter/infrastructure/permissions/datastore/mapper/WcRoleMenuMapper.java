package fm.lizhi.ocean.wavecenter.infrastructure.permissions.datastore.mapper;

import fm.lizhi.common.datastore.core.annotation.DataStore;
import fm.lizhi.common.datastore.mysql.constant.ParamContants;
import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.common.datastore.mysql.mybatis.sqlprovider.BaseDeleteProvider;
import fm.lizhi.common.datastore.mysql.mybatis.sqlprovider.BaseInsertProvider;
import fm.lizhi.common.datastore.mysql.mybatis.sqlprovider.BaseSelectProvider;
import fm.lizhi.common.datastore.mysql.mybatis.sqlprovider.BaseUpdateProvider;
import fm.lizhi.ocean.wavecenter.infrastructure.permissions.datastore.entity.WcRoleMenuExample;
import fm.lizhi.ocean.wavecenter.infrastructure.permissions.datastore.entity.WcRoleMenu;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 */
@DataStore(namespace = "mysql_ocean_wavecenter")
public interface WcRoleMenuMapper {

    /**
     * 根据实体对象的字段值查询多条记录，查询条件会跳过NULL值的字段。
     *
     * @param entity  实体对象
     * @return  实体对象列表
     */
    @SelectProvider(type = BaseSelectProvider.class, method="select")
    List<WcRoleMenu> selectMany(WcRoleMenu entity);

    /**
     * 根据实体对象的字段值查询单条记录，查询条件会跳过NULL值的字段。
     *
     * @param entity  实体对象
     * @return  单个实体对象
     */
    @SelectProvider(type = BaseSelectProvider.class, method="select")
    WcRoleMenu selectOne(WcRoleMenu entity);

    /**
     * 根据实体对象中主键（{@link javax.persistence.Id}标注）字段查询单条数据。
     *
     * @param entity  实体对象
     * @return  单个实体对象
     */
    @SelectProvider(type = BaseSelectProvider.class, method="selectByPrimaryKey")
    WcRoleMenu selectByPrimaryKey(WcRoleMenu entity);

    /**
     * 根据实体对象的字段值查询分页记录，查询条件会跳过NULL值的字段。
     *
     * @param entity  实体对象
     * @return  分页数据
     */
    @SelectProvider(type = BaseSelectProvider.class, method="selectPage")
    PageList<WcRoleMenu> selectPage(@Param(ParamContants.ENTITIE) WcRoleMenu entity, @Param(ParamContants.PAGE_NUMBER)int pageNumber, @Param(ParamContants.PAGE_SIZE)int pageSize);

    /**
     * 根据实体对象中主键字段（{@link javax.persistence.Id}标注）删除数据。
     *
     * @param entity  实体对象
     * @return  影响行数
     */
    @DeleteProvider(type = BaseDeleteProvider.class, method="deleteByPrimaryKey")
    int deleteByPrimaryKey(WcRoleMenu entity);

    /**
     * 将实体对象写入数据库，会跳过NULL值的字段。<br/>
     * 如果主键字段（{@link javax.persistence.Id}标注）有设置{@link javax.persistence.GeneratedValue}时，会自动生成主键并设置到实体对象中。
     *
     * @param entity  实体对象
     * @return  影响行数
     */
    @InsertProvider(type = BaseInsertProvider.class, method="insert")
    int insert(WcRoleMenu entity);

    /**
     * 批量将实体对象写入数据库（不会跳过NULL值)。<br/>
     * 如果字段有设置{@link javax.persistence.Id}和{@link javax.persistence.GeneratedValue}时，会自动生成值，并设置到实体类实例中。
     *
     * @param entities  实体对象列表
     * @return  影响行数
     */
    @InsertProvider(type = BaseInsertProvider.class, method="batchInsert")
    int batchInsert(@Param(ParamContants.ENTITIES) List<WcRoleMenu> entities);

    /**
     * 根据实体类中主键（{@link javax.persistence.Id}标注的字段）更新数据，会跳过NULL值的字段。
     *
     * @param entity  实体对象
     * @return  影响行数
     */
    @UpdateProvider(type = BaseUpdateProvider.class, method="updateByPrimaryKey")
    int updateByPrimaryKey(WcRoleMenu entity);
       
    /**
     * 根据example类生成WHERE条件查询总记录条数
     * 
     * @param example
     * @return
     */
    @SelectProvider(type = BaseSelectProvider.class, method="countByExample")
    long countByExample(WcRoleMenuExample example);

    /**
     * 根据example类生成WHERE条件删除记录
     *
     * @param example
     * @return
     */
    @DeleteProvider(type = BaseDeleteProvider.class, method="deleteByExample")
    long deleteByExample(WcRoleMenuExample example);

    /**
     * 根据example类生成WHERE条件查询记录数
     * 
     * @param example
     * @return
     */
    @SelectProvider(type = BaseSelectProvider.class, method="selectByExample")
    List<WcRoleMenu> selectByExample(WcRoleMenuExample example);
    
    /**
     * 根据example类生成WHERE条件查询分页记录数
     *
     * @param example
     * @param pageNumber  页码
     * @param pageSize  每页数据大小
     * @return
     */
    @SelectProvider(type = BaseSelectProvider.class, method="pageByExample")
    PageList<WcRoleMenu> pageByExample(@Param(ParamContants.EXAMPLE) WcRoleMenuExample example, @Param(ParamContants.PAGE_NUMBER)int pageNumber, @Param(ParamContants.PAGE_SIZE)int pageSize);    

    /**
     * 根据example类生成WHERE条件更新记录数，会跳过实体类对象中的NULL值的字段。<br/>
     *
     * @param entity 实体类对象
     * @param example
     * @return
     */
    @UpdateProvider(type = BaseUpdateProvider.class, method="updateByExample")
    int updateByExample(@Param(ParamContants.ENTITIE) WcRoleMenu entity, @Param(ParamContants.EXAMPLE) WcRoleMenuExample example);


}
