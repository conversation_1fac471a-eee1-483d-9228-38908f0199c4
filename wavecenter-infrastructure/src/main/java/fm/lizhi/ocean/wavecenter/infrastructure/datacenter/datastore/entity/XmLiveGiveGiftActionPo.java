package fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.entity;

import lombok.Data;

import java.util.Date;

@Data
public class XmLiveGiveGiftActionPo {

    private Long id;

    private Long userId;

    private Long njId;

    private Long giftId;

    private Long totalLitchiAmount;

    private Long giftAmount;

    private Date createTime;

    private Date modifyTime;

    private String source;

    private Long bizId;

    private String bizInfo;

    private Long value;

    private Long liveId;

    private Long recUserId;

    private Long targetGiftId;

    private Integer isGiveCoin;

    private Long giveCoinAmount;

    private Long giftType;


    private Integer liveType;

    private Integer reward;

    private Long propId;

    private String channel;

    private Integer extendType;

    private Long appId;

    private Long incomeUserId;

    private Long unitPrice;

    private Long subAppId;

    private Integer doubleHitGift;

}
