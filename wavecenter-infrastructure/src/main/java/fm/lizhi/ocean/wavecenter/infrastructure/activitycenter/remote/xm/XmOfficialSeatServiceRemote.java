package fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.remote.xm;

import com.alibaba.fastjson.JSONObject;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.live.pp.dto.homeSeatManual.*;
import fm.lizhi.live.pp.services.HomeSeatManualService;
import xm.fm.lizhi.live.pp.dto.homeSeatManual.*;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.convert.ActivityOfficialSeatConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.remote.IOfficialSeatServiceRemote;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.ResultDTO;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.XmOfficialSeatDetailDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;


@Slf4j
@Component
public class XmOfficialSeatServiceRemote implements IOfficialSeatServiceRemote<XmOfficialSeatDetailDTO> {


    @Autowired
    private HomeSeatManualService homeSeatManualService;

    @Override
    public Result<ResultDTO> saveOfficialSeatWithActivity(XmOfficialSeatDetailDTO xmOfficialSeatDetailDTO) {
        SaveConfigReq saveConfigReq = ActivityOfficialSeatConvert.I.buildXmConfigReq(xmOfficialSeatDetailDTO);
        Result<SaveConfigResp> result = homeSeatManualService.saveConfig(saveConfigReq);
        if (RpcResult.isFail(result)) {
            log.warn("xm.saveOfficialSeatWithActivity fail,req={},rCode={}", JSONObject.toJSONString(saveConfigReq), result.rCode());
            return RpcResult.fail(SAVE_OFFICIAL_SEAT_WITH_ACTIVITY_FAIL);
        }
        return RpcResult.success(ResultDTO.success(result.target().getId()));
    }

    @Override
    public Result<XmOfficialSeatDetailDTO> findSameTimeOfficialSeatConfig(Long startTime, Long endTime, Integer seat, Long tabId) {
        //西米不用检查，时间冲突也能插入成功
        return RpcResult.fail(FIND_SAME_TIME_OFFICIAL_SEAT_CONFIG_NOT_SAME);
    }

    @Override
    public Result<Void> deleteOfficialSeat(long bizRecordId) {
        DeleteConfigReq deleteConfigReq = new DeleteConfigReq();
        deleteConfigReq.setId(bizRecordId);
        Result<DeleteConfigResp> result = homeSeatManualService.deleteConfig(deleteConfigReq);
        if (RpcResult.isFail(result)) {
            log.warn("xm.deleteOfficialSeat fail,bizRecordId={},rCode={}", bizRecordId, result.rCode());
            return RpcResult.fail(DELETE_OFFICIAL_SEAT_FAIL);
        }
        return RpcResult.success();
    }

    @Override
    public Result<XmOfficialSeatDetailDTO> findOfficialSeatByRecordId(long bizRecordId) {
        GetConfigsReq configsReq = new GetConfigsReq();
        configsReq.setId(bizRecordId);
        Result<GetConfigsResp> result = homeSeatManualService.getConfigs(configsReq);
        if (RpcResult.isFail(result)) {
            log.warn("xm.findOfficialSeatByRecordId fail,bizRecordId={},rCode={}", bizRecordId, result.rCode());
            return RpcResult.fail(FIND_OFFICIAL_SEAT_BY_RECORD_ID_FAIL);
        }

        List<ConfigDto> list = result.target().getList();
        if (CollectionUtils.isEmpty(list)) {
            return RpcResult.fail(FIND_OFFICIAL_SEAT_BY_RECORD_ID_NOT_EXIST);
        }
        ConfigDto configDto = list.get(0);
        XmOfficialSeatDetailDTO xmOfficialSeatDetailDTO = new XmOfficialSeatDetailDTO()
                .setId(configDto.getId())
                .setRemark(configDto.getNote())
                .setStartTime(configDto.getStartTime())
                .setEndTime(configDto.getEndTime())
                .setTabId(configDto.getTabId())
                .setBackgroundUrl(configDto.getBackgroundUrl())
                .setPosition(configDto.getSeat())
                .setUserGroupId(configDto.getUserGroups())
                .setUserIds(configDto.getUserIds());
            return RpcResult.success(xmOfficialSeatDetailDTO);
    }


    @Override
    public BusinessEvnEnum getBusinessEvnEnum() {
        return BusinessEvnEnum.XIMI;
    }
}
