package fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.datastore.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 活动资源转存记录
 *
 * @date 2024-10-30 03:20:06
 */
@Table(name = "`activity_resource_transfer`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ActivityResourceTransfer {
    /**
     * ID
     */
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 应用ID
     */
    @Column(name= "`app_id`")
    private Integer appId;

    /**
     * 来源 URI
     */
    @Column(name= "`source_uri`")
    private String sourceUri;

    /**
     * 转存 URI
     */
    @Column(name= "`target_uri`")
    private String targetUri;

    /**
     * 转存状态 0: 失败 1: 成功
     */
    @Column(name= "`status`")
    private Integer status;

    /**
     * 重试次数
     */
    @Column(name= "`try_count`")
    private Integer tryCount;

    /**
     * 创建时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name= "`update_time`")
    private Date updateTime;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", appId=").append(appId);
        sb.append(", sourceUri=").append(sourceUri);
        sb.append(", targetUri=").append(targetUri);
        sb.append(", status=").append(status);
        sb.append(", tryCount=").append(tryCount);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append("]");
        return sb.toString();
    }
}