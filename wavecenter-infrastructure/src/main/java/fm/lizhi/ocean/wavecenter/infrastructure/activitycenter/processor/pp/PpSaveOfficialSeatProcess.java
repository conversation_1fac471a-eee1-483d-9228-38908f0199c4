package fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.processor.pp;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.processor.ISaveOfficialSeatProcess;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import java.util.Iterator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Component
public class PpSaveOfficialSeatProcess implements ISaveOfficialSeatProcess<PpOfficialSeatDetailDTO> {

    @Override
    public PpOfficialSeatDetailDTO buildSaveDTO(SaveOfficialSeatParamDTO param, PpOfficialSeatDetailDTO sameData) {
        PpOfficialActActionConfigDTO actionConfigDto = PpOfficialActActionConfigDTO.builder()
                .action(param.getExtra().getAction())
                .template(param.getExtra().getTemplate())
                .coverImg(param.getBackgroundUrl())
                .liveTitleColor(param.getExtra().getLiveTitleColor())
                .build();

        //配置很灵活，这里只配置单个，给自动下发用
        PpOfficialActCardDTO cardDto = new PpOfficialActCardDTO()
                .setPosition(param.getPosition())
                .setActionConfigList(Lists.newArrayList(actionConfigDto));

        PpOfficialSeatDetailDTO detailDto = null;
        if (sameData != null) {
            //如果不存在相同位置的记录，直接新增，存在的话，跳转记录增加一条
            //查询是否有相同位置的卡片
            Optional<PpOfficialActCardDTO> positionDto = sameData.getCardList().stream().filter(card -> Objects.equals(card.getPosition(), param.getPosition())).findFirst();
            if (positionDto.isPresent()) {
                //相同位置存在，则直接action
                PpOfficialActCardDTO ppOfficialActCardDTO = positionDto.get();
                ppOfficialActCardDTO.getActionConfigList().add(actionConfigDto);
            } else {
                List<PpOfficialActCardDTO> cardList = sameData.getCardList();
                cardList.add(cardDto);
            }
            detailDto = sameData;
        } else {
            //没有配置，则新增一个配置
            PpOfficialActListItemDTO actListItemDto = new PpOfficialActListItemDTO()
                    .setName(param.getName())
                    .setStartTime(param.getStartTime().getTime())
                    .setEndTime(param.getEndTime().getTime());

            detailDto = new PpOfficialSeatDetailDTO()
                    .setActInfo(actListItemDto)
                    .setCardList(Lists.newArrayList(cardDto));
        }

        return detailDto;
    }

    @Override
    public Pair<Boolean, String> checkDataValid(SaveOfficialSeatParamDTO param, PpOfficialSeatDetailDTO sameData) {
        return Pair.of(Boolean.TRUE, "");
    }

    @Override
    public boolean isDirectDeleteSeatRecord(DeleteOfficialSeatParamDTO param, PpOfficialSeatDetailDTO seatData) {
        if (CollectionUtils.isEmpty(seatData.getCardList())) {
            return true;
        }

        //只有一个卡片且卡片中仅有一个action时，可以直接删除
        if (seatData.getCardList().size() == 1) {
            for (PpOfficialActCardDTO card : seatData.getCardList()) {
                List<PpOfficialActActionConfigDTO> actionConfigList = card.getActionConfigList();
                if (CollectionUtils.isEmpty(actionConfigList)) {
                    return true;
                }

                if (actionConfigList.size() == 1) {
                    PpOfficialActActionConfigDTO actionConfig = actionConfigList.get(0);
                    if (StringUtils.isNotEmpty(actionConfig.getAction())) {
                        JSONObject officialSeatAction = JSONObject.parseObject(actionConfig.getAction());
                        //存在该用户
                        if (Objects.equals(officialSeatAction.getString("userId"), String.valueOf(param.getNjId()))) {
                            return true;
                        }
                    }
                }
            }
        }
        return false;
    }

    @Override
    public BusinessEvnEnum getBusinessEnv() {
        return BusinessEvnEnum.PP;
    }

    @Override
    public PpOfficialSeatDetailDTO buildDeleteDTO(DeleteOfficialSeatParamDTO param, PpOfficialSeatDetailDTO detailDto) {
        List<PpOfficialActCardDTO> cardList = detailDto.getCardList();
        Iterator<PpOfficialActCardDTO> cardDTOIterator = cardList.iterator();
        while (cardDTOIterator.hasNext()) {
            PpOfficialActCardDTO card = cardDTOIterator.next();
            List<PpOfficialActActionConfigDTO> actionConfigList = card.getActionConfigList();
            if (actionConfigList.isEmpty()) {
                continue;
            }
            Iterator<PpOfficialActActionConfigDTO> iterator = actionConfigList.iterator();
            while (iterator.hasNext()) {
                PpOfficialActActionConfigDTO actionConfig = iterator.next();
                String action = actionConfig.getAction();
                if (StringUtils.isNotEmpty(action)) {
                    JSONObject officialSeatAction = JSONObject.parseObject(action);
                    //存在该用户，则删除该记录
                    if (Objects.equals(officialSeatAction.getString("userId"), String.valueOf(param.getNjId()))) {
                        iterator.remove();
                    }
                }
            }
            if (actionConfigList.isEmpty()) {
                cardDTOIterator.remove();
            }
        }
        return detailDto;
    }
}
