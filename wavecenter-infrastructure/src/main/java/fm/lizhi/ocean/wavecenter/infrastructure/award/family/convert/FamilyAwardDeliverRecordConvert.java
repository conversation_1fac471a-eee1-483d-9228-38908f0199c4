package fm.lizhi.ocean.wavecenter.infrastructure.award.family.convert;

import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.ocean.wavecenter.api.award.family.constants.FamilyAwardDeliverRecordStatusEnum;
import fm.lizhi.ocean.wavecenter.common.convert.CommonConvert;
import fm.lizhi.ocean.wavecenter.common.utils.DateTimeUtils;
import fm.lizhi.ocean.wavecenter.infrastructure.award.family.datastore.entity.WcFamilyAwardDeliverRecord;
import fm.lizhi.ocean.wavecenter.infrastructure.award.family.datastore.param.CreateFamilyAwardDeliverExecutionParam;
import fm.lizhi.ocean.wavecenter.infrastructure.award.family.datastore.param.CreateFamilyAwardDeliverRecordParam;
import fm.lizhi.ocean.wavecenter.service.award.family.dto.FamilyAwardDeliverRecordDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.Date;
import java.util.List;

@Mapper(unmappedTargetPolicy = ReportingPolicy.ERROR,
        uses = {
                CommonConvert.class,
        },
        imports = {
                ConfigUtils.class,
                Date.class,
                FamilyAwardDeliverRecordStatusEnum.class,
        }
)
public interface FamilyAwardDeliverRecordConvert {

    FamilyAwardDeliverRecordConvert I = Mappers.getMapper(FamilyAwardDeliverRecordConvert.class);

    @Mapping(target = "familyName", source = "familyName", defaultValue = "")
    @Mapping(target = "familyUserName", source = "familyUserName", defaultValue = "")
    CreateFamilyAwardDeliverRecordParam toCreateParam(
            int appId, long familyId, String familyName, long familyUserId, String familyUserName,
            Date awardStartTime, Date deliverTime, List<CreateFamilyAwardDeliverExecutionParam> deliverExecutions);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "deployEnv", expression = "java(ConfigUtils.getEnvRequired().name())")
    @Mapping(target = "awardEndTime", source = "param", qualifiedByName = "awardEndTime")
    @Mapping(target = "deliverDate", source = "param", qualifiedByName = "deliverDate")
    @Mapping(target = "status", expression = "java(FamilyAwardDeliverRecordStatusEnum.DELIVERING.getValue())")
    @Mapping(target = "createTime", expression = "java(new Date())")
    @Mapping(target = "modifyTime", expression = "java(new Date())")
    WcFamilyAwardDeliverRecord toCreateEntity(CreateFamilyAwardDeliverRecordParam param);

    @Named("awardEndTime")
    default Date awardEndTime(CreateFamilyAwardDeliverRecordParam param) {
        return DateTimeUtils.getSameWeekSundayEndTime(param.getAwardStartTime());
    }

    @Named("deliverDate")
    default Date deliverDate(CreateFamilyAwardDeliverRecordParam param) {
        return DateTimeUtils.getStartTimeOfDay(param.getDeliverTime());
    }

    FamilyAwardDeliverRecordDTO entityToDTO(WcFamilyAwardDeliverRecord record);

    List<FamilyAwardDeliverRecordDTO> entitysToDTOs(List<WcFamilyAwardDeliverRecord> records);
}
