package fm.lizhi.ocean.wavecenter.infrastructure.resource.decorate.remote.xm;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.common.service.CommonService;
import fm.lizhi.ocean.wavecenter.api.resource.constants.PlatformDecorateTypeEnum;
import fm.lizhi.ocean.wavecenter.api.resource.decorate.bean.DecorateInfoBean;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.base.util.JsonUtils;
import fm.lizhi.ocean.wavecenter.common.config.CommonConfig;
import fm.lizhi.ocean.wavecenter.common.utils.UrlUtils;
import fm.lizhi.ocean.wavecenter.infrastructure.resource.decorate.constants.XmDecorateTypeEnum;
import fm.lizhi.ocean.wavecenter.infrastructure.resource.decorate.convert.xm.XmDecorateInfoConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.resource.decorate.remote.DecorateRemote;
import fm.lizhi.ocean.wavecenter.infrastructure.resource.decorate.remote.request.RequestRecoverDecorate;
import fm.lizhi.ocean.wavecenter.infrastructure.resource.decorate.remote.request.RequestSendDecorate;
import fm.lizhi.xm.vip.bean.decorate.DecorateDto;
import fm.lizhi.xm.vip.bean.decorate.DecorateSendTypeEnum;
import fm.lizhi.xm.vip.bean.decorate.req.BatchGetDecorateValidStockReq;
import fm.lizhi.xm.vip.bean.decorate.req.DelDecorateStockByIdsReq;
import fm.lizhi.xm.vip.bean.decorate.req.GetDecorateListReq;
import fm.lizhi.xm.vip.bean.decorate.req.SendDecorateManagerReq;
import fm.lizhi.xm.vip.bean.decorate.resp.*;
import fm.lizhi.xm.vip.services.DecorateService;
import fm.lizhi.xm.vip.services.DecorateStockService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component
public class XmDecorateRemote implements DecorateRemote {

    @Autowired
    private DecorateService decorateService;

    @Autowired
    private DecorateStockService decorateStockService;

    @Autowired
    private CommonConfig commonConfig;

    @Override
    public DecorateInfoBean getDecorateInfo(PlatformDecorateTypeEnum decorateType, Long decorateId) {
        Result<GetDecorateInfoResp> result = decorateService.getDecorateInfoById(decorateId);
        if (RpcResult.isFail(result)) {
            log.warn("xm.getDecorateInfo fail. getDecorateInfoById fail, decorateId={},rCode={}", decorateId, result.rCode());
            return null;
        }
        
        DecorateDto decorateDto = result.target().getDecorateDto();
        if (decorateDto == null) {
            return null;
        }

        return XmDecorateInfoConvert.convertDtoToDecorateInfoBean(decorateDto, commonConfig.getBizConfig().getCdnHost(), decorateType);
    }

    @Override
    public BusinessEvnEnum getBusinessEvnEnum() {
        return BusinessEvnEnum.XIMI;
    }

    @Override
    public PageBean<DecorateInfoBean> getDecorateInfoList(PlatformDecorateTypeEnum decorateType, Long decorateId, String decorateName, int pageNum, int pageSize) {
        GetDecorateListReq req = new GetDecorateListReq();
        req.setType(XmDecorateTypeEnum.getByType(decorateType).getType());
        req.setPageNum(pageNum);
        req.setPageSize(pageSize);

        if (decorateId != null && decorateId > 0) {
            req.setId(decorateId);
        }
        if (StringUtils.isNotBlank(decorateName)) {
            req.setName(decorateName);
        }

        Result<GetDecorateListResp> result = decorateService.getDecorateList(req);
        if (RpcResult.isFail(result)) {
            log.warn("xm.getDecorateInfoList fail. getDecorateList fail, decorateType={},decorateName={},rCode={}", decorateType, decorateName, result.rCode());
            return new PageBean<DecorateInfoBean>(0, Collections.emptyList());
        }
        List<PageDecorateDto> decorateList = result.target().getDecorateList();
        if (CollectionUtils.isEmpty(decorateList)) {
            return PageBean.empty();
        }

        List<DecorateInfoBean> decorateInfoBeanList = decorateList.stream().map(x ->
                XmDecorateInfoConvert.convertToDecorateInfoBean(x, commonConfig.getBizConfig().getCdnHost(), decorateType)).collect(Collectors.toList());
        return new PageBean<DecorateInfoBean>(result.target().getTotalPage(), decorateInfoBeanList);
    }

    @Override
    public List<DecorateInfoBean> batchGetDecorateInfo(PlatformDecorateTypeEnum decorateType, List<Long> decorateIds) {
        Result<List<PageDecorateDto>> result = decorateService.getDecorateInfosByIds(decorateIds);
        if (RpcResult.isFail(result)) {
            log.warn("xm.batchGetDecorateInfo fail. getDecorateInfoByIds fail, decorateIds={},rCode={}", decorateIds, result.rCode());
            return Collections.emptyList();
        }
        return result.target().stream().map(x -> XmDecorateInfoConvert.convertToDecorateInfoBean(x, commonConfig.getBizConfig().getCdnHost(), decorateType)).collect(Collectors.toList());
    }

    @Override
    public Result<Void> sendDecorate(RequestSendDecorate request) {
        PlatformDecorateTypeEnum decorateType = request.getDecorateType();
        switch (decorateType) {
            case VEHICLE:
            case MEDAL:
            case AVATAR:
                return sendDecorateManager(request);
            default:
                return RpcResult.fail(CommonService.PARAM_ERROR, "xm暂未实现该装扮类型发放: " + decorateType);
        }
    }

    @Override
    public Result<Void> recoverDecorate(RequestRecoverDecorate request) {
        PlatformDecorateTypeEnum decorateType = request.getDecorateType();
        switch (decorateType) {
            case VEHICLE:
            case MEDAL:
            case AVATAR:
                return recoverDecorateManager(request);
            default:
                return RpcResult.fail(CommonService.PARAM_ERROR, "xm暂未实现该装扮类型恢复: " + decorateType);
        }
    }

    private Result<Void> recoverDecorateManager(RequestRecoverDecorate request) {
        BatchGetDecorateValidStockReq req = new BatchGetDecorateValidStockReq();
        req.setUserIds(Collections.singletonList(request.getUserId()));
        req.setDecorateIds(Collections.singletonList(request.getDecorateId()));
        Result<BatchGetDecorateValidStockResp> stockRespResult = decorateStockService.batchGetDecorateValidStock(req);
        if(RpcResult.isFail(stockRespResult)) {
            log.error("xm.recoverDecorateManager batchGetDecorateValidStock fail; rCode={}; request={}", stockRespResult.rCode(), request);
            return RpcResult.fail(stockRespResult.rCode());
        }
        if(CollectionUtils.isEmpty(stockRespResult.target().getStockDtoList())) {
            log.info("xm.recoverDecorateManager batchGetDecorateValidStock empty;request={}}", JsonUtils.toJsonString(request));
            return RpcResult.success();
        }
        List<Long> stockIds = stockRespResult.target().getStockDtoList().stream().map(PageDecorateStockDto::getId).collect(Collectors.toList());
        DelDecorateStockByIdsReq delDecorateStockByIdsReq = new DelDecorateStockByIdsReq();
        delDecorateStockByIdsReq.setIds(stockIds);
        Result<DelDecorateStockByIdsResp> result = decorateStockService.batchDelDecorateStockByIds(delDecorateStockByIdsReq);

        if (RpcResult.isFail(result)) {
            log.warn("xm.recoverDecorateManager fail. recoverDecorateManager fail, request={},recoverDecorateManagerReq={},rCode={}",
                    request, delDecorateStockByIdsReq, result.rCode());
            return RpcResult.fail(result.rCode(), result.getMessage());
        }
        return RpcResult.success();
    }

    private Result<Void> sendDecorateManager(RequestSendDecorate request) {
        int useImmediately = NumberUtils.INTEGER_ONE;
        SendDecorateManagerReq sendDecorateManagerReq = new SendDecorateManagerReq();
        sendDecorateManagerReq.setDecorateId(request.getDecorateId());
        sendDecorateManagerReq.setOwnerIds(String.valueOf(request.getUserId()));
        sendDecorateManagerReq.setCount(request.getDecorateNumber());
        sendDecorateManagerReq.setUseImmediately(useImmediately);
        sendDecorateManagerReq.setValidMin(request.getDecorateExpireTime());
        sendDecorateManagerReq.setSendType(DecorateSendTypeEnum.SEND_NOW.getType());
        Result<SendDecorateManagerResp> result = decorateStockService.sendDecorateManager(sendDecorateManagerReq);
        if (RpcResult.isFail(result)) {
            log.warn("xm.sendDecorateManager fail. sendDecorateManager fail, request={},sendDecorateManagerReq={},rCode={},msg={}",
                    request, sendDecorateManagerReq, result.rCode(), result.getMessage());
            return RpcResult.fail(result.rCode(), result.getMessage());
        }
        if (result.target() != null && CollectionUtils.isNotEmpty(result.target().getFailIdList())) {
            log.info("xm.sendDecorateManager fail. sendDecorateManager fail, request={},sendDecorateManagerReq={},failIdList={}",
                    request, sendDecorateManagerReq, result.target().getFailIdList());
            return RpcResult.fail(CommonService.BUSINESS_ERROR, "发放失败");
        }
        return RpcResult.success();
    }
}
