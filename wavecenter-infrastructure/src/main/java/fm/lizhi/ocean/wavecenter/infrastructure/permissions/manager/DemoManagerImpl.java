package fm.lizhi.ocean.wavecenter.infrastructure.permissions.manager;

import fm.lizhi.ocean.wavecenter.infrastructure.permissions.datastore.entity.WcTest;
import fm.lizhi.ocean.wavecenter.infrastructure.permissions.datastore.mapper.WcTestMapper;
import fm.lizhi.ocean.wavecenter.service.permissions.dto.DemoDto;
import fm.lizhi.ocean.wavecenter.service.permissions.manager.DemoManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/25 18:33
 */
@Component
public class DemoManagerImpl implements DemoManager {

    @Autowired
    private WcTestMapper wcTestMapper;

    @Override
    public DemoDto getTest() {
        List<WcTest> wcTests = wcTestMapper.selectMany(new WcTest());
        WcTest wcTest = wcTests.get(0);
        DemoDto demoDto = new DemoDto();
        demoDto.setId(wcTest.getId());
        return demoDto;
    }
}
