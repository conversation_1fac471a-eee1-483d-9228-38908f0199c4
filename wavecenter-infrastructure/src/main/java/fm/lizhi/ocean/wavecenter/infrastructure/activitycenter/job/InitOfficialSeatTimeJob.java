package fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.job;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import fm.lizhi.common.job.dispatcher.executor.handler.JobExecuteContext;
import fm.lizhi.common.job.dispatcher.executor.handler.JobHandler;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.commons.util.DateUtil;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.common.dto.DateDTO;
import fm.lizhi.ocean.wavecenter.common.utils.DateTimeUtils;
import fm.lizhi.ocean.wavecenter.common.utils.ResultHandler;
import fm.lizhi.ocean.wavecenter.service.activitycenter.config.ActivityConfig;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.ActivityOfficialSeatTimeDTO;
import fm.lizhi.ocean.wavecenter.service.activitycenter.manager.ActivityOfficialSeatManager;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class InitOfficialSeatTimeJob implements JobHandler {

    @Autowired
    private ActivityOfficialSeatManager activityOfficialSeatManager;

    @Autowired
    private ActivityConfig config;

    @Override
    public void execute(JobExecuteContext context) throws Exception {
        for (BusinessEvnEnum env : BusinessEvnEnum.values()) {
            if (env.getOnline() != 1) {
                continue;
            }
            Result<Void> result = initOfficialSeatTime(env.getAppId());
            log.info("初始化官方活动座位时间表，appId:{}, result={}", env.getAppId(), result.rCode());
        }
    }

    public Result<Void> initOfficialSeatTime(Integer appId) {
        return ResultHandler.handle(appId, () -> {
            try {
                Integer applyDay = config.getBizConfig().getMaxPreactApplyDay();
                Date dayStart = DateUtil.getDayStart(new Date());
                Date dayEnd = DateUtil.getDayAfter(dayStart, applyDay + 1);
                return initCore(appId, dayStart, dayEnd);
            } catch (Exception e) {
                log.error("initOfficialSeatTime happen error: appId={}", appId, e);
                return RpcResult.fail(GeneralRCode.GENERAL_RCODE_UNKNOWN_ERROR);
            }
        });
    }

    private Result<Void> initCore(int appId, Date startTime, Date endTime) {
        List<Integer> officialSeatAvailableNumbers = config.getBizConfig().getOfficialSeatAvailableNumbers();
        List<DateDTO> dateDTOS = DateTimeUtils.divideTimeSlots(startTime, endTime);
        for (Integer seatNumber : CollectionUtils.emptyIfNull(officialSeatAvailableNumbers)) {
            List<ActivityOfficialSeatTimeDTO> list = new ArrayList<>();
            for (DateDTO dateDTO : dateDTOS) {
                List<ActivityOfficialSeatTimeDTO> seatList = activityOfficialSeatManager.getOfficialSeatList(appId, dateDTO.getStartTime(), dateDTO.getEndTime(), seatNumber);
                if (seatList != null && seatList.size() >= 48) {
                    continue;
                }

                Map<String, ActivityOfficialSeatTimeDTO> map = new HashMap<>(64);
                if (CollectionUtils.isNotEmpty(seatList)) {
                    seatList.forEach(timeDTO -> map.put(DateUtil.formatDateToString(timeDTO.getStartTime(), DateUtil.datetime_2) + "-" + DateUtil.formatDateToString(timeDTO.getEndTime(), DateUtil.datetime_2), timeDTO));
                }

                String key = DateUtil.formatDateToString(dateDTO.getStartTime(), DateUtil.datetime_2) + "-" + DateUtil.formatDateToString(dateDTO.getEndTime(), DateUtil.datetime_2);
                if (map.containsKey(key)) {
                    continue;
                }
                ActivityOfficialSeatTimeDTO seatTime = new ActivityOfficialSeatTimeDTO()
                        .setSeat(seatNumber)
                        .setAppId(appId)
                        .setStartTime(dateDTO.getStartTime())
                        .setEndTime(dateDTO.getEndTime())
                        .setCount(0)
                        .setShowDate(DateUtil.getDayStart(dateDTO.getStartTime()));
                list.add(seatTime);
            }

            boolean officialSeatRes = activityOfficialSeatManager.initOfficialSeatTime(list);
            log.info("初始化官方活动座位时间表，appId:{}, seat:{}, startTime:{}, endTime:{}, officialSeatRes={}", appId, seatNumber, startTime, endTime, officialSeatRes);
        }
        return RpcResult.success();
    }
}
