package fm.lizhi.ocean.wavecenter.infrastructure.permissions.manager;

import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wavecenter.infrastructure.permissions.datastore.entity.WcRoleComponent;
import fm.lizhi.ocean.wavecenter.infrastructure.permissions.datastore.mapper.WcRoleComponentMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.permissions.convert.ComponentConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.permissions.datastore.entity.WcRoleComponentExample;
import fm.lizhi.ocean.wavecenter.service.permissions.dto.ComponentDto;
import fm.lizhi.ocean.wavecenter.service.permissions.manager.ComponentManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Nonnull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/27 16:25
 */
@Component
public class ComponentManagerImpl implements ComponentManager {

    @Autowired
    private WcRoleComponentMapper wcRoleComponentMapper;

    @Nonnull
    @Override
    public List<ComponentDto> getRoleComponent(String roleCode) {
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        WcRoleComponentExample example = new WcRoleComponentExample();
        example.createCriteria()
                .andAppIdEqualTo(appId)
                .andRoleCodeEqualTo(roleCode);

        List<WcRoleComponent> componentList = wcRoleComponentMapper.selectByExample(example);
        return ComponentConvert.I.roleRefPos2Dtos(componentList);
    }

}
