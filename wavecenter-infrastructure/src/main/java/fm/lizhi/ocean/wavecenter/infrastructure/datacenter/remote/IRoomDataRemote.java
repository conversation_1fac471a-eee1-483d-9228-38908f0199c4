package fm.lizhi.ocean.wavecenter.infrastructure.datacenter.remote;

import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.RoomAssessmentInfoBean;
import fm.lizhi.ocean.wavecenter.infrastructure.common.remote.IRemote;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.po.PlayerSignPerformancePo;
import fm.lizhi.ocean.wavecenter.service.datacenter.dto.GetRoomPlayerPerformanceParamDto;

/**
 * 签约厅数据
 * 数据查询差异化+业务服务调用
 * <AUTHOR>
 * @date 2024/5/23 16:07
 */
public interface IRoomDataRemote extends IRemote {

    /**
     * 查询考核数据
     * @param familyId
     * @param roomId
     * @return
     */
    RoomAssessmentInfoBean queryAssessment(Long familyId, Long roomId);


    PageList<PlayerSignPerformancePo> selectPlayerSignPerformance(GetRoomPlayerPerformanceParamDto paramDto);
}
