package fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.convert;

import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.SendRecommendCardParamDTO;
import hy.fm.lizhi.live.pp.live.protocol.TopRecommendProto;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface RecommendCardConvert {

    RecommendCardConvert I = Mappers.getMapper(RecommendCardConvert.class);


}
