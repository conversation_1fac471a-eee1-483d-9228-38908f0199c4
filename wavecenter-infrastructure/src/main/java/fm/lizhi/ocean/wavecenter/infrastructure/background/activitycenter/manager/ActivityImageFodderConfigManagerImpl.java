package fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.manager;

import cn.hutool.core.util.StrUtil;
import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityImageFodderBean;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestPageActivityImageFodder;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestSaveActivityImageFodder;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestUpdateActivityImageFodder;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.response.ResponseSaveActivityImageFodder;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.service.ActivityImageFodderConfigService;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.common.config.CommonConfig;
import fm.lizhi.ocean.wavecenter.common.utils.UrlUtils;
import fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.convert.ActivityImageFodderConfigConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.datastore.entity.ActivityImageFodder;
import fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.datastore.entity.ActivityImageFodderExample;
import fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.datastore.mapper.ActivityImageFodderMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.common.constants.LogicDeleteConstants;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.manager.ActivityImageFodderConfigManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class ActivityImageFodderConfigManagerImpl implements ActivityImageFodderConfigManager {


    @Autowired
    private ActivityImageFodderMapper activityImageFodderMapper;

    @Autowired
    private CommonConfig commonConfig;

    @Override
    public Result<ResponseSaveActivityImageFodder> saveImageFodder(RequestSaveActivityImageFodder param) {
        ActivityImageFodder imageFodder = ActivityImageFodderConfigConvert.I.buildActivityImageFodderForSave(param);
        imageFodder.setImageUrl(UrlUtils.removeHostOrEmpty(imageFodder.getImageUrl()));
        
        return activityImageFodderMapper.insert(imageFodder) > 0
                ? RpcResult.success(ActivityImageFodderConfigConvert.I.buildResponseSaveActivityImageFodder(imageFodder, commonConfig.getRomeFsDownloadCdn()))
                : RpcResult.fail(ActivityImageFodderConfigService.SAVE_ACTIVITY_IMAGE_FODDER_FAIL, "保存图片素材失败");
    }

    @Override
    public Result<Void> updateImageFodder(RequestUpdateActivityImageFodder param) {
        ActivityImageFodder imageFodder = ActivityImageFodderConfigConvert.I.buildActivityImageFodderForUpdate(param);
        imageFodder.setImageUrl(UrlUtils.removeHostOrEmpty(imageFodder.getImageUrl()));
        return activityImageFodderMapper.updateByPrimaryKey(imageFodder) > 0
                ? RpcResult.success()
                : RpcResult.fail(ActivityImageFodderConfigService.UPDATE_ACTIVITY_IMAGE_FODDER_FAIL, "更新图片素材失败");
    }

    @Override
    public Result<Void> deleteImageFodder(Long id, String operator) {
        ActivityImageFodder imageFodder = ActivityImageFodder.builder().id(id).deleted(LogicDeleteConstants.DELETED).operator(operator).modifyTime(new Date()).build();
        return activityImageFodderMapper.updateByPrimaryKey(imageFodder) > 0
                ? RpcResult.success()
                : RpcResult.fail(ActivityImageFodderConfigService.DELETE_ACTIVITY_IMAGE_FODDER_FAIL, "删除图片素材失败");
    }

    @Override
    public Result<PageBean<ActivityImageFodderBean>> pageImageFodder(RequestPageActivityImageFodder param) {

        ActivityImageFodderExample example = new ActivityImageFodderExample();
        ActivityImageFodderExample.Criteria criteria = example.createCriteria().andDeletedEqualTo(LogicDeleteConstants.NOT_DELETED)
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name());

        if (param.getAppId() != null){
            criteria.andAppIdEqualTo(param.getAppId());
        }

        if (StrUtil.isNotBlank(param.getName())){
            criteria.andNameLike("%" + param.getName().trim() + "%");
        }

        if (param.getType() != null){
            criteria.andTypeEqualTo(param.getType());
        }
        example.setOrderByClause("create_time desc, id desc");
        PageList<ActivityImageFodder> pageList = activityImageFodderMapper.pageByExample(example, param.getPageNo(), param.getPageSize());
        pageList.forEach(fodder -> {
            fodder.setImageUrl(UrlUtils.addHostOrEmpty(fodder.getImageUrl(), commonConfig.getRomeFsDownloadCdn()));
        });

        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS,
                PageBean.of(pageList.getTotal(), ActivityImageFodderConfigConvert.I.convertPageImageFodder(pageList))
        );
    }
}
