package fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.remote.pp;

import java.util.Objects;
import java.util.Optional;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONObject;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.base.util.JsonUtils;
import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.convert.ActivityOfficialSeatConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.remote.IOfficialSeatServiceRemote;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.PpOfficialSeatDetailDTO;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.ResultDTO;
import fm.lizhi.pp.content.artrcmd.api.OfficialActivityService;
import fm.lizhi.pp.content.artrcmd.bean.offact.OfficialActDetailDto;
import fm.lizhi.pp.content.artrcmd.bean.offact.OfficialActListItemDto;
import fm.lizhi.pp.content.artrcmd.bean.req.ReqGetOfficialActList;
import fm.lizhi.pp.content.artrcmd.bean.resp.RespGetOfficialActList;
import fm.lizhi.pp.content.artrcmd.constants.OfficialActConstants;
import fm.lizhi.pp.content.artrcmd.protocol.OfficialActivityProto;
import fm.lizhi.pp.content.artrcmd.protocol.OfficialActivityProto.ResponseUpdateActivityStatus;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class PpOfficialSeatServiceRemote implements IOfficialSeatServiceRemote<PpOfficialSeatDetailDTO> {

    @Autowired
    private OfficialActivityService officialActivityService;

    @Override
    public Result<ResultDTO> saveOfficialSeatWithActivity(PpOfficialSeatDetailDTO detailDto) {
        OfficialActDetailDto detail = ActivityOfficialSeatConvert.I.buildPpConfigReq(detailDto);
        Result<OfficialActivityProto.ResponseSaveActivity> result = officialActivityService.saveActivity(JSONObject.toJSONString(detail));
        if (RpcResult.isFail(result)) {
            log.warn("PpOfficialSeatServiceRemote.saveOfficialSeatWithActivity code:{},activityName:{}",
                    result.rCode(), detailDto.getActInfo().getName());
            return RpcResult.fail(result.rCode());
        }

        ResultDTO resDTO = new ResultDTO()
                .setCode(result.target().getCode())
                .setMsg(result.target().getOperateMsg())
                .setBizRecordId(result.target().getId());
        return RpcResult.success(resDTO);
    }

    @Override
    public Result<PpOfficialSeatDetailDTO> findSameTimeOfficialSeatConfig(Long startTime, Long endTime, Integer seat, Long tableId) {
        //需要查询是否有活动了
        ReqGetOfficialActList queryParam = new ReqGetOfficialActList();
        queryParam.setFromTime(startTime);
        queryParam.setStatus(OfficialActConstants.ActDaoMapDtoStatus.WAIT_EFFECT);
        //不会很多，所以先查一页
        queryParam.setPageNo(1);
        queryParam.setPageSize(100);
        Result<OfficialActivityProto.ResponseGetActivityList> queryResult = officialActivityService.getActivityList(JSONObject.toJSONString(queryParam));
        //查询失败直接设置，设置失败提示运营手动添加
        OfficialActivityProto.ResponseGetActivityList res = queryResult.target();
        if (res == null) {
            return RpcResult.fail(FIND_SAME_TIME_OFFICIAL_SEAT_CONFIG_NOT_SAME);
        }

        RespGetOfficialActList officialActList = JsonUtils.fromJsonStringLegacy(res.getRespGetOfficialActListStr(), RespGetOfficialActList.class);
        //确认是否有开始结束时间一样的，如果有，则在里面增加配置
        if (officialActList == null || CollectionUtils.isEmpty(officialActList.getRows())) {
            return RpcResult.fail(FIND_SAME_TIME_OFFICIAL_SEAT_CONFIG_NOT_SAME);
        }

        //过滤出开始时间结束时间一致的配置
        Optional<OfficialActListItemDto> dtoOptional = officialActList.getRows().stream()
                .filter(info -> Objects.equals(info.getStartTime(), startTime) && Objects.equals(info.getEndTime(), endTime))
                .findFirst();
        if (!dtoOptional.isPresent()) {
            return RpcResult.fail(FIND_SAME_TIME_OFFICIAL_SEAT_CONFIG_NOT_SAME);
        }

        Result<OfficialActivityProto.ResponseGetActivityDetail> detailRes = officialActivityService.getActivityDetail(dtoOptional.get().getId());
        if (RpcResult.isFail(detailRes)) {
            return RpcResult.fail(FIND_SAME_TIME_OFFICIAL_SEAT_CONFIG_FAIL);
        }
        OfficialActDetailDto detailDto = JsonUtils.fromJsonStringLegacy(detailRes.target().getActivityDetailStr(), OfficialActDetailDto.class);
        PpOfficialSeatDetailDTO waveDTO = ActivityOfficialSeatConvert.I.convertPpSeatDto2WaveDTO(detailDto);
        return RpcResult.success(waveDTO);
    }

    @Override
    public BusinessEvnEnum getBusinessEvnEnum() {
        return BusinessEvnEnum.PP;
    }

    @Override
    public Result<Void> deleteOfficialSeat(long bizRecordId) {
        Result<ResponseUpdateActivityStatus> delRes = officialActivityService.updateActivityStatus(bizRecordId, OfficialActConstants.OperateType.DOWN_SHELF);
        if (RpcResult.isFail(delRes)) {
            return RpcResult.fail(DELETE_OFFICIAL_SEAT_FAIL);
        }
        return RpcResult.success();

    }

    @Override
    public Result<PpOfficialSeatDetailDTO> findOfficialSeatByRecordId(long bizRecordId) {
        Result<OfficialActivityProto.ResponseGetActivityDetail> detailRes = officialActivityService.getActivityDetail(bizRecordId);
        if (RpcResult.isFail(detailRes)) {
            return RpcResult.fail(FIND_OFFICIAL_SEAT_BY_RECORD_ID_FAIL);
        }

        if (detailRes.target() == null || StringUtils.isEmpty(detailRes.target().getActivityDetailStr())) {
            return RpcResult.fail(FIND_OFFICIAL_SEAT_BY_RECORD_ID_NOT_EXIST);
        }

        OfficialActDetailDto detailDto = JsonUtils.fromJsonStringLegacy(detailRes.target().getActivityDetailStr(), OfficialActDetailDto.class);
        PpOfficialSeatDetailDTO waveDTO = ActivityOfficialSeatConvert.I.convertPpSeatDto2WaveDTO(detailDto);
        return RpcResult.success(waveDTO);
    }
}
