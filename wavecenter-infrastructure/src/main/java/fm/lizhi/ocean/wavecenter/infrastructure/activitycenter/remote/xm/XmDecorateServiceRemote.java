package fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.remote.xm;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.convert.DecorateConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.remote.IDecorateServiceRemote;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.DecorateInfoDTO;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.SendDecorateParamDTO;
import fm.lizhi.xm.vip.bean.decorate.req.SendDecorateImmediatelyResp;
import fm.lizhi.xm.vip.bean.decorate.req.SendDecorateReq;
import fm.lizhi.xm.vip.bean.decorate.resp.DecorateTipEnum;
import fm.lizhi.xm.vip.services.DecorateStockService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class XmDecorateServiceRemote implements IDecorateServiceRemote {

    @Autowired
    private DecorateStockService decorateStockService;

    @Override
    public Result<Void> sendDecorate(SendDecorateParamDTO param) {
        SendDecorateReq sendDecorateReq = DecorateConvert.I.convertXmSendDecorateReq(param);
        sendDecorateReq.setOwnerIds(String.valueOf(param.getOwnerId()));
        Result<SendDecorateImmediatelyResp> result = decorateStockService.sendDecorateImmediately(sendDecorateReq);
        if (RpcResult.isFail(result)) {
            log.warn("xm.sendDecorate fail decorateId={},rCode={}", param.getDecorateId(), result.rCode());
            if (result.rCode() == DecorateTipEnum.ILLEGAL_PARAMS.getCode()) {
                return RpcResult.fail(SEND_DECORATED_PARAM_ERROR, "参数异常");
            } else if (result.rCode() == DecorateTipEnum.NOT_DATA.getCode()) {
                return RpcResult.fail(SEND_DECORATED_DECORATE_NO_EXIST, "装扮不存在");
            }
            return RpcResult.fail(SEND_DECORATED_FAIL);
        }
        return RpcResult.success();
    }

    @Override
    public Result<DecorateInfoDTO> getDecorateInfo(long decorateId) {
        return null;
    }

    @Override
    public BusinessEvnEnum getBusinessEvnEnum() {
        return BusinessEvnEnum.XIMI;
    }
}
