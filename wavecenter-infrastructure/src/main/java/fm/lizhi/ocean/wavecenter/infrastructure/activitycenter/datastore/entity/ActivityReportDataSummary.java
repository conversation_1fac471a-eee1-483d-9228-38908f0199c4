package fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.datastore.entity;

import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 活动报表-汇总表
 *
 * @date 2025-04-29 05:49:36
 */
@Table(name = "`activity_report_data_summary`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ActivityReportDataSummary {
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private String id;

    /**
     * 活动ID
     */
    @Column(name= "`activity_id`")
    private Long activityId;

    /**
     * 提报厅厅主ID
     */
    @Column(name= "`nj_id`")
    private Long njId;

    /**
     * 家族ID
     */
    @Column(name= "`family_id`")
    private Long familyId;

    /**
     * 活动开始时间
     */
    @Column(name= "`start_time`")
    private Date startTime;

    /**
     * 活动结束时间
     */
    @Column(name= "`end_time`")
    private Date endTime;

    /**
     * 进房人数
     */
    @Column(name= "`enter_room_user_cnt`")
    private Long enterRoomUserCnt;

    /**
     * 新人进房人数
     */
    @Column(name= "`enter_room_new_user_cnt`")
    private Long enterRoomNewUserCnt;

    /**
     * 房间逗留人数
     */
    @Column(name= "`user_full_one_min`")
    private Long userFullOneMin;

    /**
     * 新人都留人数
     */
    @Column(name= "`new_user_full_one_min`")
    private Long newUserFullOneMin;

    /**
     * 送礼人数
     */
    @Column(name= "`gift_user_cnt`")
    private Long giftUserCnt;

    /**
     * 新人送礼人数
     */
    @Column(name= "`gift_new_user_cnt`")
    private Long giftNewUserCnt;

    /**
     * 房间新增粉丝数
     */
    @Column(name= "`new_fans_user_cnt`")
    private Long newFansUserCnt;

    /**
     * 送礼钻石数
     */
    @Column(name= "`all_income`")
    private Long allIncome;

    /**
     * 送礼魅力值
     */
    @Column(name= "`all_charm`")
    private Long allCharm;

    /**
     * 应用ID
     */
    @Column(name= "`app_id`")
    private Integer appId;

    /**
     * 进房停留率
     */
    @Column(name= "`enter_room_stay_rate`")
    private BigDecimal enterRoomStayRate;

    /**
     * 评论人数
     */
    @Column(name= "`comment_user_cnt`")
    private Long commentUserCnt;

    /**
     * 停留发言率
     */
    @Column(name= "`stay_speak_rate`")
    private BigDecimal staySpeakRate;

    /**
     * 停留付费率
     */
    @Column(name= "`stay_pay_rate`")
    private BigDecimal stayPayRate;

    /**
     * 新用户进房停留率
     */
    @Column(name= "`new_user_enter_room_stay_rate`")
    private BigDecimal newUserEnterRoomStayRate;

    /**
     * 新用户评论人数
     */
    @Column(name= "`new_user_comment_user_cnt`")
    private Long newUserCommentUserCnt;

    /**
     * 新用户停留发言率
     */
    @Column(name= "`new_user_stay_speak_rate`")
    private BigDecimal newUserStaySpeakRate;

    /**
     * 新用户停留付费率
     */
    @Column(name= "`new_user_stay_pay_rate`")
    private BigDecimal newUserStayPayRate;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", activityId=").append(activityId);
        sb.append(", njId=").append(njId);
        sb.append(", familyId=").append(familyId);
        sb.append(", startTime=").append(startTime);
        sb.append(", endTime=").append(endTime);
        sb.append(", enterRoomUserCnt=").append(enterRoomUserCnt);
        sb.append(", enterRoomNewUserCnt=").append(enterRoomNewUserCnt);
        sb.append(", userFullOneMin=").append(userFullOneMin);
        sb.append(", newUserFullOneMin=").append(newUserFullOneMin);
        sb.append(", giftUserCnt=").append(giftUserCnt);
        sb.append(", giftNewUserCnt=").append(giftNewUserCnt);
        sb.append(", newFansUserCnt=").append(newFansUserCnt);
        sb.append(", allIncome=").append(allIncome);
        sb.append(", allCharm=").append(allCharm);
        sb.append(", appId=").append(appId);
        sb.append(", enterRoomStayRate=").append(enterRoomStayRate);
        sb.append(", commentUserCnt=").append(commentUserCnt);
        sb.append(", staySpeakRate=").append(staySpeakRate);
        sb.append(", stayPayRate=").append(stayPayRate);
        sb.append(", newUserEnterRoomStayRate=").append(newUserEnterRoomStayRate);
        sb.append(", newUserCommentUserCnt=").append(newUserCommentUserCnt);
        sb.append(", newUserStaySpeakRate=").append(newUserStaySpeakRate);
        sb.append(", newUserStayPayRate=").append(newUserStayPayRate);
        sb.append("]");
        return sb.toString();
    }
}