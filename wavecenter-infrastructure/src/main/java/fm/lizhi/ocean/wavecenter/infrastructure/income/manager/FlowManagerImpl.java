package fm.lizhi.ocean.wavecenter.infrastructure.income.manager;

import cn.hutool.core.collection.CollUtil;
import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.commons.util.DateUtil;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.income.bean.*;
import fm.lizhi.ocean.wavecenter.api.income.constants.IncomeType;
import fm.lizhi.ocean.wavecenter.common.dto.PageDto;
import fm.lizhi.ocean.wavecenter.infrastructure.income.convert.IncomeInfraConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.income.datastore.entity.WcFlowFamilyNj;
import fm.lizhi.ocean.wavecenter.infrastructure.income.datastore.entity.WcFlowFamilyNjExample;
import fm.lizhi.ocean.wavecenter.infrastructure.income.datastore.entity.WcFlowUser;
import fm.lizhi.ocean.wavecenter.infrastructure.income.datastore.entity.WcFlowUserExample;
import fm.lizhi.ocean.wavecenter.infrastructure.income.datastore.mapper.WcFlowFamilyNjMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.income.datastore.mapper.WcFlowUserMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.income.remote.IFlowRemote;
import fm.lizhi.ocean.wavecenter.service.income.dto.GiveGiftFlowDto;
import fm.lizhi.ocean.wavecenter.service.income.dto.RoomRecFlowSumDto;
import fm.lizhi.ocean.wavecenter.service.income.manager.FlowManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/4/23 19:09
 */
@Component
public class FlowManagerImpl implements FlowManager {

    @Autowired
    private WcFlowFamilyNjMapper flowFamilyNjMapper;
    @Autowired
    private IFlowRemote iFlowRemote;

    @Autowired
    private WcFlowUserMapper flowUserMapper;

    @Override
    public PageBean<GuildIncomeDetailBean> getGuildIncomeDetailOut(GetGuildIncomeDetailParamBean paramBean) {
        WcFlowFamilyNjExample example = buildFlowFamilyNjExample(paramBean.getAppId(), paramBean.getRoomId(), paramBean.getFamilyId(),
                                        paramBean.getIncomeType(),
                                        paramBean.getStartDate(), paramBean.getEndDate());

        PageList<WcFlowFamilyNj> pageList = flowFamilyNjMapper.pageByExample(example, paramBean.getPageNo(), paramBean.getPageSize());
        List<GuildIncomeDetailBean> list = IncomeInfraConvert.I.flowFamilyNjs2guildIncomeDetailBeans(pageList);
        return PageBean.of(pageList.getTotal(), list);
    }

    @Override
    public PageBean<PersonalIncomeDetailBean> getPersonalIncomeDetailOut(GetPersonalIncomeDetailParamBean paramBean) {

        WcFlowUserExample example = buildFlowUserExample(paramBean.getAppId(), paramBean.getUserId(), null,
                paramBean.getStartDate(), paramBean.getEndDate());

        PageList<WcFlowUser> pageList = flowUserMapper.pageByExample(example, paramBean.getPageNo(), paramBean.getPageSize());
        List<PersonalIncomeDetailBean> list = IncomeInfraConvert.I.flowUsers2personalIncomeDetailBeans(pageList);
        return PageBean.of(pageList.getTotal(), list);

    }

    @Override
    public PageBean<PlayerIncomeDetailBean> getPlayerIncomeDetailOut(GetPlayerIncomeDetailParamBean paramBean) {

        WcFlowUserExample example = buildFlowUserExample(paramBean.getAppId(), paramBean.getUserId(), paramBean.getIncomeType(),
                paramBean.getStartDate(), paramBean.getEndDate());

        PageList<WcFlowUser> pageList = flowUserMapper.pageByExample(example, paramBean.getPageNo(), paramBean.getPageSize());
        List<PlayerIncomeDetailBean> list = IncomeInfraConvert.I.flowUsers2playerIncomeDetailBeans(pageList);
        return PageBean.of(pageList.getTotal(), list);
    }



    private static WcFlowUserExample buildFlowUserExample(Integer appId, Long userId, List<IncomeType> incomeType,
                                                                   Date startDate, Date endDate) {
        String startDateStr = DateUtil.formatDateToString(startDate, DateUtil.date_2) + " 00:00:00";
        String endDateStr = DateUtil.formatDateToString(endDate, DateUtil.date_2) + " 23:59:59";

        WcFlowUserExample example = new WcFlowUserExample();
        WcFlowUserExample.Criteria criteria = example.createCriteria();
        criteria.andAppIdEqualTo(appId)
                .andFlowDateBetween(DateUtil.formatStrToNormalDate(startDateStr), DateUtil.formatStrToNormalDate(endDateStr))
                .andUserIdEqualTo(userId)
        ;

        if (incomeType != null) {
            criteria.andBizIdIn(incomeType.stream().map(IncomeType::getValue).collect(Collectors.toList()));
        }

        example.setOrderByClause("flow_date desc");
        return example;
    }


    private static WcFlowFamilyNjExample buildFlowFamilyNjExample(Integer appId, Long roomId, Long familyId, List<IncomeType> incomeType,
                                                                  Date startDate, Date endDate) {
        String startDateStr = DateUtil.formatDateToString(startDate, DateUtil.date_2) + " 00:00:00";
        String endDateStr = DateUtil.formatDateToString(endDate, DateUtil.date_2) + " 23:59:59";

        WcFlowFamilyNjExample example = new WcFlowFamilyNjExample();
        WcFlowFamilyNjExample.Criteria criteria = example.createCriteria();
        criteria.andAppIdEqualTo(appId)
                .andFamilyIdEqualTo(familyId)
                .andNjIdEqualTo(roomId)
                .andFlowDateBetween(DateUtil.formatStrToNormalDate(startDateStr), DateUtil.formatStrToNormalDate(endDateStr))
        ;
        if (CollUtil.isNotEmpty(incomeType)) {
            criteria.andBizIdIn(incomeType.stream().map(IncomeType::getValue).collect(Collectors.toList()));
        }
        example.setOrderByClause("flow_date desc");

        return example;

    }

    @Override
    public PageDto<GiveGiftFlowDto> getRoomRecFlow(GetRoomSignRoomParamBean paramBean) {
        return iFlowRemote.getRoomRecFlow(paramBean);
    }

    @Override
    public RoomRecFlowSumDto getRoomRecFlowSum(GetRoomSignRoomParamBean paramBean) {
        return iFlowRemote.getRoomRecFlowSum(paramBean);
    }

    @Override
    public PageBean<RoomIncomeDetailBean> getRoomIncomeDetailOut(GetRoomIncomeDetailParamBean paramBean) {
        WcFlowFamilyNjExample example = buildFlowFamilyNjExample(paramBean.getAppId(), paramBean.getRoomId(), paramBean.getFamilyId(),
                paramBean.getIncomeType(),
                paramBean.getStartDate(), paramBean.getEndDate());

        PageList<WcFlowFamilyNj> pageList = flowFamilyNjMapper.pageByExample(example, paramBean.getPageNo(), paramBean.getPageSize());
        List<RoomIncomeDetailBean> list = IncomeInfraConvert.I.flowFamilyNjs2roomIncomeDetailBeans(pageList);
        return PageBean.of(pageList.getTotal(), list);
    }
}
