package fm.lizhi.ocean.wavecenter.infrastructure.datacenter.job;

import fm.lizhi.common.job.dispatcher.executor.handler.JobExecuteContext;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wavecenter.infrastructure.common.constants.PaySettleConfigCodeEnum;
import fm.lizhi.ocean.wavecenter.infrastructure.common.constants.PayTenantCodeEnum;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.dao.PayConfigCodeDao;
import fm.lizhi.ocean.wavecenter.infrastructure.income.dto.ConfigCodeBizInfoDTO;
import fm.lizhi.ocean.wavecenter.infrastructure.income.manager.PaymentManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/4/22 11:39
 */
@Slf4j
public abstract class AbstractPaySettleConfigCodeSyncJob {

    @Autowired
    private PaymentManager paymentManager;
    @Autowired
    private PayConfigCodeDao payConfigCodeDao;

    protected void doSync(JobExecuteContext jobExecuteContext){
        String param = jobExecuteContext.getParam();
        log.info("AbstractPaySettleConfigCodeSyncJob start. param={}", param);

        // 如果参数有指定，则只同步指定的配置，否则会同步所有配置
        List<String> configCodes = getConfigCodes(param);
        if (CollectionUtils.isEmpty(configCodes)) {
            log.info("configCodes is empty");
            return;
        }

        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        Optional<String> payTenantCodeOp = PayTenantCodeEnum.getPayTenantCodeNullable(appId);
        if (!payTenantCodeOp.isPresent()) {
            log.info("payTenantCode not present. appId={}", appId);
            return;
        }

        for (String configCode : configCodes) {
            Optional<ConfigCodeBizInfoDTO> configCodeBizInfoOp = paymentManager.getConfigCodeBizInfo(payTenantCodeOp.get(), configCode);
            if (!configCodeBizInfoOp.isPresent()) {
                continue;
            }

            ConfigCodeBizInfoDTO configCodeBizInfoDTO = configCodeBizInfoOp.get();
            // 查询账户主体类型
            Optional<String> accountTypeOp = paymentManager.getAccountType(configCodeBizInfoDTO.getAccountEngineCode());
            if (!accountTypeOp.isPresent()) {
                log.info("accountType not exist. configCode={},accountCode={}", configCode, configCodeBizInfoDTO.getAccountEngineCode());
                continue;
            }

            // 保存配置
            payConfigCodeDao.saveConfigCode(appId, configCode
                    , configCodeBizInfoDTO.getAccountCode(), accountTypeOp.get(), configCodeBizInfoDTO.getAccountEngineCode()
                    , configCodeBizInfoDTO.getBizIdList());
        }

    }

    /**
     * 获取需要同步的配置
     * @param param
     * @return
     */
    protected List<String> getConfigCodes(String param){
        if (StringUtils.isBlank(param)) {
            return Arrays.stream(PaySettleConfigCodeEnum.values())
                    .map(PaySettleConfigCodeEnum::getConfigCode)
                    .collect(Collectors.toList());
        }

        PaySettleConfigCodeSyncParam paramEntity = JsonUtil.loads(param, PaySettleConfigCodeSyncParam.class);
        return paramEntity.getConfigCodes();
    }

}
