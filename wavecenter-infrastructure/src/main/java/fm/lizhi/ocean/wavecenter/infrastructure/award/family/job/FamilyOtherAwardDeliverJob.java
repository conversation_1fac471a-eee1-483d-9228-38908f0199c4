package fm.lizhi.ocean.wavecenter.infrastructure.award.family.job;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.Week;
import fm.lizhi.common.job.dispatcher.executor.handler.JobExecuteContext;
import fm.lizhi.common.job.dispatcher.executor.handler.JobHandler;
import fm.lizhi.ocean.wavecenter.base.util.JsonUtils;
import fm.lizhi.ocean.wavecenter.common.utils.DateTimeUtils;
import fm.lizhi.ocean.wavecenter.infrastructure.award.family.manager.deliver.FamilyOtherAwardDeliverManager;
import fm.lizhi.ocean.wavecenter.service.award.family.config.FamilyAwardConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedHashSet;
import java.util.List;

/**
 * 公会其他奖励发放任务
 */
@Component
@Slf4j
public class FamilyOtherAwardDeliverJob implements JobHandler {

    @Autowired
    private FamilyAwardConfig familyAwardConfig;

    @Autowired
    private FamilyOtherAwardDeliverManager familyOtherAwardDeliverManager;

    @Override
    public void execute(JobExecuteContext context) throws Exception {
        FamilyOtherAwardDeliverJobParam param = parseParam(context);
        List<Integer> appIds = getAppIds(param);
        Date awardStartTime = getAwardStartTime(param);

        if (param == null || param.getIgnoreDayCheck() == null) {
            // 线上空参数执行，必须保证当前是周二，否则收入数据结算会缺失
            Week week = DateUtil.thisDayOfWeekEnum();
            if (week != Week.TUESDAY) {
                log.info("not TUESDAY. week={}", week);
                return;
            }
        }

        log.info("FamilyOtherAwardDeliverJob start, appIds: {}, awardStartTime: {}", appIds, awardStartTime);
        familyOtherAwardDeliverManager.deliverAward(appIds, awardStartTime);
        log.info("FamilyOtherAwardDeliverJob end");
    }

    private FamilyOtherAwardDeliverJobParam parseParam(JobExecuteContext context) {
        if (StringUtils.isBlank(context.getParam())) {
            return null;
        }
        try {
            return JsonUtils.fromJsonString(context.getParam(), FamilyOtherAwardDeliverJobParam.class);
        } catch (RuntimeException e) {
            log.info("Failed to parse param: {}", context.getParam(), e);
            return null;
        }
    }

    /**
     * 获取应用id列表, 如果参数中没有指定则返回默认的值: PP
     *
     * @param param 参数
     * @return 应用id列表
     */
    private List<Integer> getAppIds(FamilyOtherAwardDeliverJobParam param) {
        if (param == null || param.getAppIds() == null || param.getAppIds().isEmpty()) {
            return new ArrayList<>(new LinkedHashSet<>(familyAwardConfig.getOtherAwardJobAppIds()));
        }
        return param.getAppIds();
    }

    /**
     * 获取奖励周期开始, 如果参数中没有指定则返回上一周周一的开始时间
     *
     * @param param 参数
     * @return 奖励开始时间
     */
    private Date getAwardStartTime(FamilyOtherAwardDeliverJobParam param) {
        if (param != null && param.getAwardStartTime() != null) {
            return param.getAwardStartTime();
        }
        return DateTimeUtils.getLastWeekMondayStartTime();
    }
}
