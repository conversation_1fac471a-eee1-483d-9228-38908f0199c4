package fm.lizhi.ocean.wavecenter.infrastructure.common.constants;

import java.util.EnumSet;
import java.util.Set;

public class StateMachine {

    // 定义状态枚举
    public enum State {
        WAIT_REVIEW(StatusType.INITIAL),
        PENDING(StatusType.MIDDLE),
        SELECTED(StatusType.MIDDLE),
        APPROVED(StatusType.FINAL),
        REJECTED(StatusType.FINAL);

        private enum StatusType { INITIAL, MIDDLE, FINAL }
        private final StatusType statusType;

        // 每个状态允许的转换目标集合
        private final Set<State> allowedTransitions;

        State(StatusType statusType, State... allowed) {
            this.statusType = statusType;
            this.allowedTransitions = EnumSet.noneOf(State.class);
            for (State state : allowed) {
                allowedTransitions.add(state);
            }
        }

        static {
            // 初始化状态转移规则
            WAIT_REVIEW.allowedTransitions.addAll(EnumSet.of(PENDING, REJECTED));
            PENDING.allowedTransitions.addAll(EnumSet.of(SELECTED, WAIT_REVIEW));
            SELECTED.allowedTransitions.addAll(EnumSet.of(APPROVED, REJECTED));
            APPROVED.allowedTransitions.add(REJECTED);
            REJECTED.allowedTransitions.add(APPROVED);
        }

        public void validateTransition(State newState) {
            if (!allowedTransitions.contains(newState)) {
                throw new IllegalStateException("状态流转操作非法");
            }
            if (statusType == StatusType.FINAL && newState.statusType != StatusType.FINAL) {
                throw new IllegalStateException("终态不允许修改");
            }
        }
    }

    private State currentState = State.WAIT_REVIEW;

    /**
     * 核心状态转换方法
     * @param newState 目标状态
     */
    public void transitionTo(State newState) {
        currentState.validateTransition(newState);
        currentState = newState;
    }

    /**
     * 处理特殊超时逻辑（7天自动退回）
     */
    public void handlePendingTimeout() {
        if (currentState == State.PENDING) {
            currentState = State.WAIT_REVIEW;
        } else {
            throw new IllegalStateException("非待定状态不可执行超时退回");
        }
    }

    /**
     * 业务操作方法（根据实际业务需求补充）
     */
    // 提交审核
    public void submitReview() { transitionTo(State.PENDING); }
    // 审核驳回
    public void reject() { transitionTo(State.REJECTED); }
    // 确认通过
    public void confirmPass() { transitionTo(State.SELECTED); }
    // 考核通过
    public void passAssessment() { transitionTo(State.APPROVED); }
    // 考核不通过
    public void failAssessment() { transitionTo(State.REJECTED); }

    // 获取当前状态（测试用）
    public State getCurrentState() {
        return currentState;
    }

    /**
     * 测试用例
     */
    public static void main(String[] args) {
        StateMachine sm = new StateMachine();

        // 正常流程测试
        sm.submitReview();   // PENDING_REVIEW → PENDING
        sm.confirmPass();    // PENDING → SELECTED
        sm.passAssessment(); // SELECTED → APPROVED
        System.out.println("当前状态: " + sm.getCurrentState());

        // 非法流转测试
        try {
            sm.transitionTo(State.PENDING); // APPROVED → PENDING 应该失败
        } catch (IllegalStateException e) {
            System.out.println(e.getMessage()); // 预期输出：状态流转操作非法
        }
    }

}
