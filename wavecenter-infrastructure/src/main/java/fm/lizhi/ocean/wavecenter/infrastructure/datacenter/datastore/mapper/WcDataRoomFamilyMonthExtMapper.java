package fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.mapper;

import fm.lizhi.common.datastore.core.annotation.DataStore;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.entity.WcDataRoomFamilyMonth;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 */
@DataStore(namespace = "mysql_ocean_wavecenter")
public interface WcDataRoomFamilyMonthExtMapper {

    @Select({
            "<script>"
            , "select "
            , "<foreach collection='metrics' item='m' separator=','>"
            , "sum(${m}) as ${m}"
            , "</foreach>"
            , "from wavecenter_data_room_family_month where app_id=#{appId}"
            , "and family_id=#{familyId}"
            , "and room_id in "
            , "<foreach collection='roomIds' item='roomId' open='(' close=')' separator=','>"
            , "#{roomId}"
            , "</foreach>"
            , "and stat_month=#{statMonth}"
            , "group by stat_month"
            , "</script>"
    })
    List<WcDataRoomFamilyMonth> sum(@Param("metrics")List<String> metrics
            , @Param("appId") Integer appId
            , @Param("familyId") Long familyId
            , @Param("roomIds") List<Long> roomIds
            , @Param("statMonth") Integer statMonth
    );


}
