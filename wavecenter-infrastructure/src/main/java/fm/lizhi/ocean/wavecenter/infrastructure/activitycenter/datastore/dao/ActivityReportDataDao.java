package fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.datastore.dao;

import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.datastore.entity.*;
import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.datastore.mapper.ActivityReportDataDetailMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.datastore.mapper.ActivityReportDataGiftMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.datastore.mapper.ActivityReportDataPlayerMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.datastore.mapper.ActivityReportDataSummaryMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class ActivityReportDataDao {

    @Autowired
    private ActivityReportDataSummaryMapper activityReportDataSummaryMapper;

    @Autowired
    private ActivityReportDataDetailMapper activityReportDataDetailMapper;

    @Autowired
    private ActivityReportDataGiftMapper activityReportDataGiftMapper;

    @Autowired
    private ActivityReportDataPlayerMapper activityReportDataPlayerMapper;


    public ActivityReportDataSummary reportSummary(Long activityId, int appId) {

        ActivityReportDataSummary summary = new ActivityReportDataSummary();
        summary.setAppId(appId);
        summary.setActivityId(activityId);

        return activityReportDataSummaryMapper.selectOne(summary);
    }

    public List<ActivityReportDataDetail> reportDetail(Long activityId, int appId) {
        ActivityReportDataDetailExample example = new ActivityReportDataDetailExample();
        example.createCriteria()
                .andAppIdEqualTo(appId)
                .andActivityIdEqualTo(activityId)
                .andTypeEqualTo(true)
        ;
        example.setOrderByClause("depart_start_time asc");

        return activityReportDataDetailMapper.selectByExample(example);
    }

    public PageList<ActivityReportDataPlayer> reportPlayer(Long activityId, int appId, int pageNo, int pageSize) {

        ActivityReportDataPlayerExample example = new ActivityReportDataPlayerExample();
        example.createCriteria().andAppIdEqualTo(appId).andActivityIdEqualTo(activityId);
        example.setOrderByClause("all_income desc");
        return activityReportDataPlayerMapper.pageByExample(example, pageNo, pageSize);
    }

    public PageList<ActivityReportDataGift> reportGift(Long activityId, int appId, int pageNo, int pageSize) {
        ActivityReportDataGiftExample example = new ActivityReportDataGiftExample();
        example.createCriteria().andAppIdEqualTo(appId).andActivityIdEqualTo(activityId);
        example.setOrderByClause("all_income desc");
        return activityReportDataGiftMapper.pageByExample(example, pageNo, pageSize);
    }
}
