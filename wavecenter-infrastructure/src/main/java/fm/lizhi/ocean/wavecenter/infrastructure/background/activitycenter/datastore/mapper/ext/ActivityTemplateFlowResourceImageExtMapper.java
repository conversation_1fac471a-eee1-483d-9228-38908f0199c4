package fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.datastore.mapper.ext;

import fm.lizhi.common.datastore.core.annotation.DataStore;
import fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.datastore.entity.ActivityTemplateFlowResourceImage;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

@DataStore(namespace = "mysql_ocean_wavecenter")
public interface ActivityTemplateFlowResourceImageExtMapper {

    @Select("<script>\n" +
            "  SELECT * FROM `activity_template_flow_resource_image` \n" +
            "  WHERE `flow_resource_id` IN \n" +
            "    <foreach collection=\"flowResourceIds\" item=\"flowResourceId\" open=\"(\" separator=\",\" close=\")\"> \n" +
            "      #{flowResourceId} \n" +
            "    </foreach> \n" +
            "   ORDER BY `flow_resource_id` ASC, `index` ASC \n" +
            "</script>")
    List<ActivityTemplateFlowResourceImage> getFlowResourceImagesByFlowResourceIds(@Param("flowResourceIds") List<Long> flowResourceIds);

    @Update("<script>\n" +
            "  DELETE FROM `activity_template_flow_resource_image` \n" +
            "  WHERE `flow_resource_id` IN \n" +
            "    <foreach collection=\"flowResourceIds\" item=\"flowResourceId\" open=\"(\" separator=\",\" close=\")\"> \n" +
            "      #{flowResourceId} \n" +
            "    </foreach> \n" +
            "</script>")
    int deleteFlowResourceImagesByFlowResourceIds(@Param("flowResourceIds") List<Long> flowResourceIds);
}
