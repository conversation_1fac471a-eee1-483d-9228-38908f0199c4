package fm.lizhi.ocean.wavecenter.infrastructure.permissions.singleton;

import fm.lizhi.common.dubbo.DubboClientBuilder;
import fm.lizhi.live.room.recommendation.api.PpRecommendationCardManageService;
import fm.lizhi.live.room.xm.services.LiveRoomRoleSpringService;
import fm.lizhi.ocean.wavecenter.infrastructure.common.constants.ServiceProviderConstants;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2024/3/28 10:17
 */
@Configuration
public class PermissionServiceProvider {

    @Bean
    @ConditionalOnMissingBean(value = LiveRoomRoleSpringService.class)
    public LiveRoomRoleSpringService liveRoomRoleSpringService(){
        return new DubboClientBuilder<>(LiveRoomRoleSpringService.class)
                .connections(ServiceProviderConstants.connections)
                .timeoutInMillis(ServiceProviderConstants.timeout)
                .retries(ServiceProviderConstants.readRetries)
                .build();
    }


}
