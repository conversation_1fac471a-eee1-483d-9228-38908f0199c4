package fm.lizhi.ocean.wavecenter.infrastructure.kafka.consumer;

import fm.lizhi.common.kafka.ioc.api.annotation.KafkaHandler;
import fm.lizhi.common.kafka.ioc.api.annotation.KafkaListener;
import fm.lizhi.common.kafka.ioc.api.strategy.annotation.Strategy;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wavecenter.common.utils.KafkaMsgUtils;
import fm.lizhi.ocean.wavecenter.infrastructure.income.manager.GiftIncomeStatManager;
import fm.lizhi.ocean.wavecenter.infrastructure.kafka.message.GiftMsg;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@KafkaListener(clusterNamespace = "xm-kafka250-bootstrap-server")
public class XmGiftMsgConsumer {

    @Autowired
    private GiftIncomeStatManager giftIncomeStatManager;

    @KafkaHandler(topic = "xm_topic_gift_gift_msg",
            group = "lz_ocean_wave_center_topic_xm_gift_msg_group")
    @Strategy(beanName = "syncConsumeStrategy")
    public void handlerGiftMsg(String body) {
        String msg = null;

        GiftMsg giftMsg = null;
        try {
            msg = KafkaMsgUtils.removeKafkaMsgCICDHead(body);
            log.info("xm.handlerGiftMsg - receive gift msg,msg={}", msg);

            ContextUtils.setBusinessEvnEnum(BusinessEvnEnum.XIMI);
            giftMsg = JsonUtil.loads(msg, GiftMsg.class);
        } catch (Exception e) {
            log.error("xm.handlerGiftMsg - json parse error,msg:{}", msg, e);
        }
        if (giftMsg == null) {
            return;
        }

        boolean res = giftIncomeStatManager.statGiftCharm(giftMsg);
        if (!res) {
            //抛异常，就不用再消费了
            throw new RuntimeException("xm.handlerGiftMsg.statGiftCharm fail");
        }
    }

}
