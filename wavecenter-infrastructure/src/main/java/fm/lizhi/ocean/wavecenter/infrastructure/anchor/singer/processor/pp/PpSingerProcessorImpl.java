package fm.lizhi.ocean.wavecenter.infrastructure.anchor.singer.processor.pp;

import cn.hutool.core.collection.CollUtil;
import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerStatusEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerTypeEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.response.ResponseGetAllSingerStatics;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserInFamilyBean;
import fm.lizhi.ocean.wavecenter.infrastructure.anchor.singer.datastore.mapper.extr.SingerInfoExtraMapper;
import fm.lizhi.ocean.wavecenter.common.utils.MyDateUtil;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.processor.ISingerProcessor;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.constant.SingerOperatorConstant;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.SaveSingerInfoParamDTO;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.dto.SingerInfoDTO;
import fm.lizhi.ocean.wavecenter.service.anchor.singer.manager.SingerInfoManager;
import fm.lizhi.ocean.wavecenter.service.sign.manager.FamilyManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class PpSingerProcessorImpl implements ISingerProcessor {

    @Autowired
    private SingerInfoManager singerInfoManager;

    @Autowired
    private FamilyManager familyManager;

    @Autowired
    private SingerInfoExtraMapper singerInfoExtraMapper;

    @Override
    public BusinessEvnEnum getBusinessEnv() {
        return BusinessEvnEnum.PP;
    }

    @Override
    public int importSinger(Map<Integer, List<Long>> ppImportSingerMap) {
        BusinessEvnEnum evnEnum = BusinessEvnEnum.PP;
        ContextUtils.setBusinessEvnEnum(evnEnum);
        int importedCount = 0;

        for (Map.Entry<Integer, List<Long>> entry : ppImportSingerMap.entrySet()) {
            Integer singerType = entry.getKey();
            List<Long> userIds = entry.getValue();

            if (CollUtil.isEmpty(userIds)) {
                continue;
            }

            // 校验歌手等级是否存在
            if (isInvalidSingerType(singerType, evnEnum)) {
                log.warn("PP 无效的歌手等级: {}", singerType);
                continue;
            }

            // 分组处理，每个分组最多处理20个用户
            List<List<Long>> userGroups = CollUtil.split(userIds, 20);

            for (List<Long> group : userGroups) {
                // 批量查询已存在的歌手
                Map<Long, SingerInfoDTO> existsMap = singerInfoManager.getSingerInfoByUserIds(group, evnEnum.getAppId(), null)
                        .stream().collect(Collectors.toMap(SingerInfoDTO::getUserId, Function.identity(), (e1, e2) -> e1));

                for (Long userId : group) {
                    if (existsMap.containsKey(userId)) {
                        log.info("PP 歌手已存在，跳过导入 userId: {}, singerType: {}", userId, singerType);
                        continue;
                    }

                    try {
                        boolean success = importSinger(userId, singerType, evnEnum);
                        importedCount += success ? 1 : 0;
                        if (success){
                            // 加入到缓存中，避免重复写入
                            existsMap.put(userId, new SingerInfoDTO());
                        }
                        log.info("PP 导入歌手 userId: {}, singerType: {}, success:{}", userId, singerType, success);
                    } catch (Exception e) {
                        log.error("PP 导入歌手失败 userId: {}, singerType: {}", userId, singerType, e);
                    }
                }
            }

        }
        return importedCount;
    }

    @Override
    public List<Long> getRelatedEliminateSingerIds(List<Long> userIds, int appId, List<Long> ids) {

        if (CollUtil.isEmpty(userIds)){
            return CollUtil.newArrayList();
        }

        // 1. 获取这些用户的所有有效歌手记录
        List<SingerInfoDTO> allSingerInfoList = singerInfoManager.getSingerInfoByUserIds(userIds, appId, CollUtil.newArrayList(SingerStatusEnum.EFFECTIVE));
        if (CollUtil.isEmpty(allSingerInfoList)){
            return CollUtil.newArrayList();
        }

        // 2. 按用户分组
        Map<Long, List<SingerInfoDTO>> userSingerMap = allSingerInfoList.stream()
                .collect(Collectors.groupingBy(SingerInfoDTO::getUserId));

        // 3. 找出当前要淘汰的歌手记录（通过ids参数）
        Set<Long> currentEliminateIds = new HashSet<>(ids);
        Map<Long, Set<Integer>> userEliminateTypesMap = new HashMap<>();

        for (SingerInfoDTO singer : allSingerInfoList) {
            if (currentEliminateIds.contains(singer.getId())) {
                userEliminateTypesMap.computeIfAbsent(singer.getUserId(), k -> new HashSet<>())
                        .add(singer.getSingerType());
            }
        }

        // 4. 根据淘汰规则找出需要关联淘汰的歌手
        // 规则：淘汰低等级歌手同时淘汰高级歌手；淘汰高级歌手时不淘汰初级歌手
        List<Long> relatedEliminateIds = new ArrayList<>();

        for (Map.Entry<Long, Set<Integer>> entry : userEliminateTypesMap.entrySet()) {
            Long userId = entry.getKey();
            Set<Integer> eliminateTypes = entry.getValue();
            List<SingerInfoDTO> userSingers = userSingerMap.get(userId);

            if (CollUtil.isEmpty(userSingers)) {
                continue;
            }

            // 找出最低的被淘汰等级
            int minEliminateType = eliminateTypes.stream().min(Integer::compareTo).orElse(Integer.MAX_VALUE);

            // 如果淘汰的是低等级歌手，需要关联淘汰所有高等级歌手
            for (SingerInfoDTO singer : userSingers) {
                // 跳过已经在淘汰列表中的歌手
                if (currentEliminateIds.contains(singer.getId())) {
                    continue;
                }

                // 如果当前歌手等级高于最低被淘汰等级，则需要关联淘汰
                if (singer.getSingerType() > minEliminateType) {
                    relatedEliminateIds.add(singer.getId());
                    log.info("PP 关联淘汰歌手: userId={}, singerType={}, 原因=淘汰了更低等级歌手({})",
                            singer.getUserId(), singer.getSingerType(), minEliminateType);
                }
            }
        }

        log.info("PP getRelatedEliminateSingerIds: userIds={}, 关联淘汰歌手数量={}", userIds, relatedEliminateIds.size());
        return relatedEliminateIds;
    }

    @Override
    public ResponseGetAllSingerStatics fillSingerStatics(Integer appId, ResponseGetAllSingerStatics result) {

        Integer currentDateValue = MyDateUtil.getDateDayValue(new Date());
        String deployEnv = ConfigUtils.getEnvRequired().name();
        int effectiveStatus = SingerStatusEnum.EFFECTIVE.getStatus();
        int singerType = SingerTypeEnum.QUALITY.getType();

        // 高级歌手数量
        long seniorSingerCnt = singerInfoExtraMapper.countSingerByStatus(appId, effectiveStatus, deployEnv, singerType);

        // 原创高级歌手数量
        long originalSeniorSingerCnt = singerInfoExtraMapper.countSingerByOriginal(appId, effectiveStatus, deployEnv, singerType, true);

        // 有收入的高级歌手数量
        // T+1，从singer_data_day统计
        long allIncomeSeniorSingerCnt = singerInfoExtraMapper.countIncomeSingerByStatDate(appId, effectiveStatus, deployEnv, currentDateValue, singerType);

        // 有收入的原创高级歌手数量
        // T+1，从singer_data_day统计
        long allIncomeOriginalSeniorSingerCnt = singerInfoExtraMapper.countIncomeSingerByOriginalAndSingerType(appId, effectiveStatus, deployEnv, currentDateValue, singerType, true);

        return result
                .setSeniorSingerCnt((int) seniorSingerCnt)
                .setOriginalSeniorSinger((int) originalSeniorSingerCnt)
                .setAllIncomeSeniorSingerCnt((int) allIncomeSeniorSingerCnt)
                .setAllIncomeOriginalSeniorSingerCnt((int) allIncomeOriginalSeniorSingerCnt);
    }


    private boolean importSinger(Long userId, Integer singerType, BusinessEvnEnum evnEnum) {
        Optional<UserInFamilyBean> familyBeanOptional = Optional.ofNullable(familyManager.getUserInFamily(userId));
        SaveSingerInfoParamDTO param = new SaveSingerInfoParamDTO();
        param.setAppId(evnEnum.getAppId());
        param.setUserId(userId);
        param.setNjId(familyBeanOptional.map(UserInFamilyBean::getNjId).orElse(0L));
        param.setFamilyId(familyBeanOptional.map(UserInFamilyBean::getFamilyId).orElse(0L) );
        param.setSingerVerifyId(0L);
        param.setSingerStatus(SingerStatusEnum.EFFECTIVE.getStatus());
        param.setSongStyle("");
        param.setOriginalSinger(false);
        param.setSingerType(singerType);
        param.setOperator(SingerOperatorConstant.SYSTEM);
        return singerInfoManager.saveSingerInfo(param);
    }
}