package fm.lizhi.ocean.wavecenter.infrastructure.common.constants;

import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Table;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import lombok.Getter;

@Getter
public enum AuditTypeEnum {

    /**
     * PP审核类型
     */
    PP_TYPE(BusinessEvnEnum.PP.getAppId(), 1110028, "AI_QUESTION"),


    ;

    private int appId;

    private int type;

    private String scene;

    /**
     * appId,scene,type
     */
    private static final Table<Integer, String, Integer> typeTable = HashBasedTable.create();

    AuditTypeEnum(int appId, int type, String scene) {
        this.appId = appId;
        this.type = type;
        this.scene = scene;
    }

    static {
        for (AuditTypeEnum value : values()) {
            typeTable.put(value.getAppId(), value.getScene(), value.getType());
        }
    }

    /**
     * 获取审核类型
     *
     * @param appId 应用ID
     * @param scene 场景
     * @return 类型
     */
    public static int getType(int appId, String scene) {
        for (AuditTypeEnum auditTypeEnum : values()) {
            if (auditTypeEnum.getAppId() == appId && auditTypeEnum.getScene().equals(scene)) {
                return auditTypeEnum.getType();
            }
        }
        throw new RuntimeException("未找到合适的审核类型，请和审核产品核对");
    }


}
