package fm.lizhi.ocean.wavecenter.infrastructure.anchor.singer.constants;

import fm.lizhi.ocean.wavecenter.infrastructure.common.constants.IRedisKey;

/**
 * 歌手相关Redis Key
 */
public enum SingerRedisKey implements IRedisKey {

    /**
     * 用户是否是歌手
     * WC_SINGER_IS_SINGER_[appId]_[userId]
     * value=1/0
     */
    IS_SINGER,

    /**
     * 厅内歌手总数
     * WC_SINGER_TOTAL_COUNT_IN_HALL_[njId]
     * value=歌手数量
     */
    SINGER_TOTAL_COUNT_IN_HALL,
    /**
     * 淘汰歌手ZSET
     * WAVECENTER_SINGER_ELIMINATE_SINGER_ZSET_[appId]
     * value=歌手ID
     * score=解约时间戳
     */
    ELIMINATE_SINGER_ZSET,

    /**
     * 歌手信息修改锁
     * WAVECENTER_SINGER_INFO_UPDATE_LOCK_[appId]_[njId]
     * value: 时间戳
     */
    SINGER_INFO_UPDATE_LOCK,

    /**
     * 歌手审核申请锁
     * WAVECENTER_SINGER_VERIFY_APPLY_LOCK_[appId]_[idCardNumber]
     * value: 时间戳
     */
    SINGER_VERIFY_APPLY_LOCK,

    /**
     * 厅内歌手总数
     * WC_SINGER_TOTAL_COUNT_METRIC_IN_HALL_KEY_[njId]
     * value=歌手数量
     */
    SINGER_TOTAL_COUNT_METRIC_IN_HALL_KEY,

    /**
     * 审核入口配置
     * WAVECENTER_SINGER_APPLY_MENU_CONFIG_[appId]_[singerType]_[deploy_env]
     * value=配置信息
     */
    APPLY_MENU_CONFIG;

    @Override
    public String getPrefix() {
        return "WAVECENTER_SINGER";
    }

    @Override
    public String getName() {
        return this.name();
    }
} 