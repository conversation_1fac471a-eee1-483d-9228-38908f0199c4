package fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.convert;

import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.datastore.entity.ActivityAiResultRate;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.ActivityAiResultRateDTO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

/**
 * AI结果评分对象转换器
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.ERROR)
public interface ActivityAiResultRateConvert {

    ActivityAiResultRateConvert INSTANCE = Mappers.getMapper(ActivityAiResultRateConvert.class);

    /**
     * DTO 转 Entity
     *
     * @param dto ActivityAiResultRateDTO 数据传输对象
     * @return ActivityAiResultRate 实体
     */
    ActivityAiResultRate toEntity(ActivityAiResultRateDTO dto);

    /**
     * Entity 转 DTO
     *
     * @param entity ActivityAiResultRate 实体
     * @return ActivityAiResultRateDTO 数据传输对象
     */
    ActivityAiResultRateDTO toDTO(ActivityAiResultRate entity);
} 