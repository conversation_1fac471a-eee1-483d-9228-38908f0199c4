package fm.lizhi.ocean.wavecenter.infrastructure.permissions.datastore.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 角色组件关联表
 *
 * @date 2024-03-27 05:09:52
 */
@Table(name = "`wavecenter_role_component`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WcRoleComponent {
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 组件编码
     */
    @Column(name= "`component_code`")
    private String componentCode;

    /**
     * 角色编码
     */
    @Column(name= "`role_code`")
    private String roleCode;

    /**
     * 业务线
     */
    @Column(name= "`app_id`")
    private Integer appId;

    /**
     * 权限类型：1=写，2=读
     */
    @Column(name= "`permission_type`")
    private Integer permissionType;

    /**
     * 角色创建时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    /**
     * 角色修改时间
     */
    @Column(name= "`modify_time`")
    private Date modifyTime;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", componentCode=").append(componentCode);
        sb.append(", roleCode=").append(roleCode);
        sb.append(", appId=").append(appId);
        sb.append(", permissionType=").append(permissionType);
        sb.append(", createTime=").append(createTime);
        sb.append(", modifyTime=").append(modifyTime);
        sb.append("]");
        return sb.toString();
    }
}