package fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.manager;

import com.alibaba.fastjson.JSONObject;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.processor.ISaveOfficialSeatProcess;
import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.remote.IBannerServiceRemote;
import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.remote.IDecorateServiceRemote;
import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.remote.IOfficialSeatServiceRemote;
import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.remote.IRecommendCardServiceRemote;
import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.remote.xm.XmActivityServiceRemote;
import fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.constants.DecorateMapping;
import fm.lizhi.ocean.wavecenter.service.activitycenter.constatns.ResourceGiveErrorTipConstant;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.*;
import fm.lizhi.ocean.wavecenter.service.activitycenter.manager.ActivityMaterielManager;
import fm.lizhi.ocean.wavecenter.service.common.processor.ProcessorFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class ActivityMaterielManagerImpl implements ActivityMaterielManager {

    @Autowired
    private IDecorateServiceRemote decorateServiceRemote;

    @Autowired
    private IBannerServiceRemote bannerServiceRemote;

    @Autowired
    private IRecommendCardServiceRemote recommendCardServiceRemote;

    @Autowired
    private IOfficialSeatServiceRemote<OfficialSeatDetailDTO> officialServiceRemote;

    @Autowired
    private ProcessorFactory factory;

    @Autowired
    private XmActivityServiceRemote xmActivityServiceRemote;

    @Override
    public Result<Void> sendDecorate(SendDecorateParamDTO paramDTO) {
        return decorateServiceRemote.sendDecorate(paramDTO);
    }

    @Override
    public Result<DecorateInfoDTO> getDecorateInfo(long decorateId) {
        Result<DecorateInfoDTO> decorateInfo = decorateServiceRemote.getDecorateInfo(decorateId);
        if (RpcResult.isFail(decorateInfo)) {
            return decorateInfo;
        }

        Integer waveType = DecorateMapping.bizValue2WaveType(decorateInfo.target().getType(), ContextUtils.getBusinessEvnEnum());
        decorateInfo.target().setType(waveType);
        return decorateInfo;
    }

    @Override
    public Result<Long> editBannerConfig(EditBannerParamDTO param) {
        return bannerServiceRemote.editBanner(param);
    }

    @Override
    public Result<Void> deleteBannerConfig(DeleteBannerParamDTO param) {
        return bannerServiceRemote.deleteBanner(param);
    }

    @Override
    public Result<Void> sendRecommendCard(SendRecommendCardParamDTO param) {
        return recommendCardServiceRemote.sendRecommendCard(param);
    }

    @Override
    public Result<SaveOfficialSeatResDTO> saveOfficialSeat(SaveOfficialSeatParamDTO param) {
        Result<OfficialSeatDetailDTO> findRes = officialServiceRemote.findSameTimeOfficialSeatConfig(param.getStartTime().getTime(),
                param.getEndTime().getTime(), param.getPosition(), param.getTabId());
        if (findRes.rCode() == IOfficialSeatServiceRemote.FIND_SAME_TIME_OFFICIAL_SEAT_CONFIG_FAIL) {
            SaveOfficialSeatResDTO resDTO = SaveOfficialSeatResDTO.fail(ResourceGiveErrorTipConstant.OFFICIAL_SEAT_GIVE_FAIL_EXCEPTION);
            return RpcResult.success(resDTO);
        }

        if (findRes.rCode() == IOfficialSeatServiceRemote.FIND_SAME_TIME_OFFICIAL_SEAT_CONFIG_SAME_TIME_SEAT) {
            SaveOfficialSeatResDTO resDTO = SaveOfficialSeatResDTO.fail(ResourceGiveErrorTipConstant.OFFICIAL_SEAT_TIME_SAME);
            return RpcResult.success(resDTO);
        }

        ISaveOfficialSeatProcess<OfficialSeatDetailDTO> processor = factory.getProcessor(ISaveOfficialSeatProcess.class);
        OfficialSeatDetailDTO detailDTO = findRes.rCode() == IOfficialSeatServiceRemote.FIND_SAME_TIME_OFFICIAL_SEAT_CONFIG_NOT_SAME ? null : findRes.target();
        Pair<Boolean, String> checkRes = processor.checkDataValid(param, detailDTO);
        if (!checkRes.getLeft()) {
            return RpcResult.success(new SaveOfficialSeatResDTO().setMsg(checkRes.getRight()));
        }

        OfficialSeatDetailDTO buildRes = processor.buildSaveDTO(param, detailDTO);
        Result<ResultDTO> saveRes = officialServiceRemote.saveOfficialSeatWithActivity(buildRes);
        log.info("saveOfficialSeat param. activityName={}, requestParam={}，res={}", param.getName(), JSONObject.toJSONString(buildRes), JSONObject.toJSONString(saveRes));
        if (RpcResult.isFail(saveRes)) {
            String msg = ResourceGiveErrorTipConstant.OFFICIAL_SEAT_GIVE_FAIL_EXCEPTION;
            if (saveRes.rCode() == IOfficialSeatServiceRemote.SAVE_OFFICIAL_SEAT_WITH_ACTIVITY_DUPLICATE_POSITION) {
                msg = ResourceGiveErrorTipConstant.OFFICIAL_SEAT_TIME_SAME;
            }
            SaveOfficialSeatResDTO resDTO = SaveOfficialSeatResDTO.fail(msg);
            return RpcResult.success(resDTO);
        }
        if (saveRes.target().getCode() == 0) {
            return RpcResult.success(SaveOfficialSeatResDTO.success(saveRes.target().getBizRecordId()));
        }
        return RpcResult.success(SaveOfficialSeatResDTO.fail(saveRes.target().getMsg()));
    }

    @Override
    public Result<Long> syncWaveActivityToApplyRecord(SyncWaveActivityToApplyRecordDTO dto) {
        return xmActivityServiceRemote.syncWaveActivityToApplyRecord(dto);
    }

    @Override
    public Result<Void> deleteWaveActivityToApplyRecord(DeleteWaveActivityToApplyRecordDTO dto) {
        return xmActivityServiceRemote.deleteWaveActivityToApplyRecord(dto);
    }

    @Override
    public Result<Void> deleteOfficialSeat(DeleteOfficialSeatParamDTO param) {
        //先查询是否存在
        Result<OfficialSeatDetailDTO> findRes = officialServiceRemote.findOfficialSeatByRecordId(param.getBizRecordId());
        if (RpcResult.isFail(findRes)) {
            log.warn("deleteOfficialSeat.findOfficialSeatByRecordId fail,rCode={}, njId={}, bizRecordId={}", findRes.rCode(), param.getNjId(), param.getBizRecordId());
            if (findRes.rCode() == IOfficialSeatServiceRemote.FIND_OFFICIAL_SEAT_BY_RECORD_ID_NOT_EXIST) {
                //不存在就直接成功
                return RpcResult.success();
            }
            return RpcResult.fail(findRes.rCode(), ResourceGiveErrorTipConstant.DELETE_OFFICIAL_SEAT_FAIL);
        }

        ISaveOfficialSeatProcess<OfficialSeatDetailDTO> processor = factory.getProcessor(ISaveOfficialSeatProcess.class);
        if (processor.isDirectDeleteSeatRecord(param, findRes.target())) {
            //直接调用删除接口
            Result<Void> delRes = officialServiceRemote.deleteOfficialSeat(param.getBizRecordId());
            if (RpcResult.isFail(delRes)) {
                log.warn("deleteOfficialSeat fail,rCode={}, njId={}, bizRecordId={}", delRes.rCode(), param.getNjId(), param.getBizRecordId());
                return RpcResult.fail(delRes.rCode(), ResourceGiveErrorTipConstant.DELETE_OFFICIAL_SEAT_FAIL);
            }
            return RpcResult.success();
        }

        //否则直接调用保存接口，把修改后的数据保存起来
        Result<ResultDTO> result = officialServiceRemote.saveOfficialSeatWithActivity(processor.buildDeleteDTO(param, findRes.target()));
        if (RpcResult.isFail(result)) {
            log.warn("deleteOfficialSeat.saveOfficialSeatWithActivity fail,rCode={}, njId={}, bizRecordId={}", result.rCode(), param.getNjId(), param.getBizRecordId());
            return RpcResult.fail(result.rCode(), ResourceGiveErrorTipConstant.DELETE_OFFICIAL_SEAT_FAIL);
        }
        return RpcResult.success();
    }
}
