package fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.mapper;

import fm.lizhi.common.datastore.core.annotation.DataStore;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.entity.WcDataRoomFamilyWeek;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 */
@DataStore(namespace = "mysql_ocean_wavecenter")
public interface WcDataRoomFamilyWeekExtMapper {

    @Select({
            "<script>"
            , "select "
            , "<foreach collection='metrics' item='m' separator=','>"
            , "sum(${m}) as ${m}"
            , "</foreach>"
            , "from wavecenter_data_room_family_week where app_id=#{appId}"
            , "and family_id=#{familyId}"
            , "and room_id in "
            , "<foreach collection='roomIds' item='roomId' open='(' close=')' separator=','>"
            , "#{roomId}"
            , "</foreach>"
            , "and start_week_date=#{startDay}"
            , "and end_week_date=#{endDay}"
            , "group by start_week_date"
            , "</script>"
    })
    List<WcDataRoomFamilyWeek> sum(@Param("metrics")List<String> metrics
            , @Param("appId") Integer appId
            , @Param("familyId") Long familyId
            , @Param("roomIds") List<Long> roomIds
            , @Param("startDay") String startDay
            , @Param("endDay") String endDay
    );


}
