package fm.lizhi.ocean.wavecenter.infrastructure.user.remote.convert;

import java.util.ArrayList;
import java.util.List;

import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import fm.lizhi.common.verify.protocol.UserVerifyProto.ResponseSearchUserVerifyResult;
import fm.lizhi.common.verify.protocol.UserVerifyProto.SearchUserVerifyResultParams;
import fm.lizhi.common.verify.protocol.UserVerifyProto.UserVerifyResult;
import fm.lizhi.ocean.wavecenter.infrastructure.user.remote.bean.UserVerifyResultBean;
import fm.lizhi.ocean.wavecenter.infrastructure.user.remote.req.SearchUserVerifyResultReq;
import fm.lizhi.ocean.wavecenter.infrastructure.user.remote.res.SearchUserVerifyResultRes;

@Mapper(unmappedTargetPolicy = ReportingPolicy.ERROR)
public interface UserVerifyConvert {

    UserVerifyConvert I = Mappers.getMapper(UserVerifyConvert.class);

    default UserVerifyResultBean verifyResultToRes(UserVerifyResult result) {
        UserVerifyResultBean bean = new UserVerifyResultBean();
        bean.setId(result.getId());
        bean.setRecordId(result.getRecordId());
        bean.setUserId(result.getUserId());
        bean.setAppId((int) result.getAppId());
        bean.setSubAppId((int) result.getSubAppId());
        bean.setBizId((int) result.getBizId());
        bean.setIdCardType(result.getIdCardType());
        bean.setName(result.getName());
        bean.setIdCardNumber(result.getIdCardNumber());
        bean.setVerifyType(result.getVerifyType());
        bean.setVerifyStatus(result.getVerifyStatus());
        bean.setCreateTime(result.getCreateTime());
        bean.setModifyTime(result.getModifyTime());
        bean.setIdCardFront(result.getIdCardFront());
        bean.setIdCardBack(result.getIdCardBack());
        bean.setIdCardPerson(result.getIdCardPerson());
        bean.setBizName(result.getBizName());
        bean.setPhoneNum(result.getPhoneNum());
        bean.setReviewer(result.getReviewer());
        bean.setReviewNote(result.getReviewNote());
        bean.setZfbBizNo(result.getZfbBizNo());
        bean.setTransactionId(result.getTransactionId());
        return bean;

    }

    default SearchUserVerifyResultParams searchVerifyParamToProto(SearchUserVerifyResultReq req) {
        SearchUserVerifyResultParams.Builder builder = SearchUserVerifyResultParams.newBuilder();
        if (req.getUserId() != null) {
            builder.setUserId(req.getUserId());
        }
        if (req.getVerifyStatus() != null) {
            builder.setVerifyStatus(req.getVerifyStatus());
        }
        if (req.getVerifyType() != null) {
            builder.setVerifyType(req.getVerifyType());
        }

        if (req.getBeginDate() != null) {
            builder.setBeginDate(req.getBeginDate());
        }
        if (req.getEndDate() != null) {
            builder.setEndDate(req.getEndDate());
        }
        if (req.getIdCardNumber() != null) {
            builder.setIdCardNumber(req.getIdCardNumber());
        }

        if (req.getAppId() != null) {
            builder.setAppId(req.getAppId());
        }

        if (req.getSearchType() != null) {
            builder.setSearchType(req.getSearchType());
        }
        return builder.build();
    }

    default SearchUserVerifyResultRes searchVerifyResultToRes(ResponseSearchUserVerifyResult verifyResult) {
        List<UserVerifyResult> resultList = verifyResult.getResultList();
        SearchUserVerifyResultRes res = new SearchUserVerifyResultRes();
        List<UserVerifyResultBean> list = new ArrayList<>();

        for (UserVerifyResult result : resultList) {
            list.add(verifyResultToRes(result));
        }
        res.setUserVerifyResultList(list);
        res.setTotalCount(verifyResult.getTotalCount());
        return res;
    }

}
