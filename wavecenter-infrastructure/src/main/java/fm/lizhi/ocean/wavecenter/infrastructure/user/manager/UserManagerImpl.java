package fm.lizhi.ocean.wavecenter.infrastructure.user.manager;

import fm.lizhi.common.verify.api.UserVerifyService;
import fm.lizhi.common.verify.protocol.UserVerifyProto;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.wave.user.export.api.param.QueryUserParam;
import fm.lizhi.ocean.wave.user.export.api.result.QueryUserByParamResult;
import fm.lizhi.ocean.wave.user.export.api.service.UserService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wavecenter.service.user.constants.VerifyStatusConstant;
import fm.lizhi.ocean.wavecenter.infrastructure.user.convert.UserConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.user.remote.IPlayerAuthServiceRemote;
import fm.lizhi.ocean.wavecenter.infrastructure.user.remote.IUserRemote;
import fm.lizhi.ocean.wavecenter.infrastructure.user.remote.req.GetUserVerifyDataReq;
import fm.lizhi.ocean.wavecenter.infrastructure.user.remote.res.GetUserPassTypeRes;
import fm.lizhi.ocean.wavecenter.infrastructure.user.remote.res.GetUserVerifyDataRes;
import fm.lizhi.ocean.wavecenter.service.user.dto.SimpleUserDto;
import fm.lizhi.ocean.wavecenter.service.user.dto.UserInfoDto;
import fm.lizhi.ocean.wavecenter.service.user.dto.UserMediaDto;
import fm.lizhi.ocean.wavecenter.service.user.dto.UserVerifyDataDTO;
import fm.lizhi.ocean.wavecenter.service.user.manager.UserManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/4/11 10:05
 */
@Slf4j
@Component
public class UserManagerImpl implements UserManager {

    @Autowired
    private IUserRemote iUserRemote;
    @Autowired
    private IPlayerAuthServiceRemote iPlayerAuthServiceRemote;
    @Autowired
    private UserService userService;
    @Autowired
    private UserVerifyService userVerifyService;


    @Override
    public List<SimpleUserDto> getSimpleUserByIds(List<Long> ids) {
        return iUserRemote.getSimpleUserByIds(ids);
    }

    @Override
    public Map<Long, SimpleUserDto> getSimpleUserMapByIds(List<Long> ids) {
        List<SimpleUserDto> dtoList = getSimpleUserByIds(ids);
        if (CollectionUtils.isEmpty(dtoList)) {
            return Collections.emptyMap();
        }
        return dtoList.stream().collect(Collectors.toMap(SimpleUserDto::getId, v->v, (k1, k2) -> k2));
    }

    @Override
    public boolean checkPlayerAuth(long userId) {
        Optional<GetUserPassTypeRes> userPassType = iPlayerAuthServiceRemote.getUserPassType(userId);
        return userPassType.map(GetUserPassTypeRes::isPassRoomPlayer).orElse(false);
    }

    @Override
    public boolean checkUserRealNameAuthStatus(long userId, int appId) {
        Optional<GetUserVerifyDataRes> userVerifyDataResult = iUserRemote.getUserVerifyDataResult(GetUserVerifyDataReq.builder()
                .userId(userId)
                .appId(appId).build());
        if (!userVerifyDataResult.isPresent()) {
            return false;
        }

        Integer verifyStatus = userVerifyDataResult.get().getVerifyStatus();
        LogContext.addResLog("verifyStatus={}", verifyStatus);
        return verifyStatus == VerifyStatusConstant.VERIFY_PASS;
    }

    @Override
    public boolean checkUserBanStatus(long userId) {
        return iUserRemote.getUserBanStatus(userId);
    }

    @Override
    public Optional<Long> getUserIdByBusinessToken(String businessToken) {
//        return Optional.of(1386455656927647746L);
        return iUserRemote.getUserIdByBusinessToken(businessToken);
    }

    @Override
    public Optional<UserInfoDto> getUserInfoById(long userId) {
        // 查询用户信息
        QueryUserParam param = new QueryUserParam();
        param.setUserId(userId);
        Result<QueryUserByParamResult> result = userService.queryUserByParam(param);
        if (RpcResult.isFail(result)) {
            return Optional.empty();
        }
        return Optional.ofNullable(UserConvert.I.waveUserPojo2UserInfoDto(result.target().getUserInfo()));
    }

    @Override
    public Optional<UserInfoDto> getUserInfoById2(long userId) {
        return iUserRemote.getUserInfoById(userId);
    }

    @Override
    public List<UserInfoDto> getUserInfoByIds(List<Long> userIds) {
        return iUserRemote.getUserInfoByIds(userIds);
    }

    @Override
    public Optional<SimpleUserDto> getUserInfoByBand(String band) {
        return iUserRemote.getUserInfoByBand(band);
    }

    @Override
    public Long getUserIdByBand(String band) {
        if (StringUtils.isBlank(band)) {
            return null;
        }
        Optional<SimpleUserDto> userOp = getUserInfoByBand(band);
        return userOp.map(SimpleUserDto::getId).orElse(null);
    }

    @Override
    public Optional<UserMediaDto> getUserMediaById(long userId) {
        return iUserRemote.getUserMediaById(userId);
    }

    @Override
    public Optional<UserVerifyDataDTO> getVerifyData(long userId) {
        Optional<GetUserVerifyDataRes> userVerifyDataResult = iUserRemote.getUserVerifyDataResult(GetUserVerifyDataReq.builder()
                .userId(userId)
                .appId(ContextUtils.getBusinessEvnEnum().appId())
                .build());
        return userVerifyDataResult.map(UserConvert.I::verifyRes2DTO);
    }


    @Override
    public int getUserBandStatus(Long userId) {
        return iUserRemote.getUserBandStatus(userId);
    }

    @Override
    public List<Long> getUserIdFromVerifyResult(String idCardNumber) {
        UserVerifyProto.GetUserIdFromVerifyResultParams.Builder builder = UserVerifyProto.GetUserIdFromVerifyResultParams.newBuilder();
        builder.setIdCardNumber(idCardNumber);
        Result<UserVerifyProto.ResponseGetUserIdFromVerifyResult> result = userVerifyService.getUserIdFromVerifyResult(builder.build());
        if (RpcResult.isFail(result)) {
            log.warn("getUserIdFromVerifyResult fail. rCode={}, idCardNumber={}", result.rCode(), idCardNumber);
            return Collections.emptyList();
        }
        return result.target().getUserIdList();
    }

    @Override
    public boolean finishPlayerCenterAuth(Long userId) {
        return iUserRemote.finishPlayerCenterAuth(userId);
    }

    @Override
    public List<Long> getUserAuthUnionIdList(String idCardNumber) {
        UserVerifyProto.GetUnionPassUserIdParams unionParam = UserVerifyProto.GetUnionPassUserIdParams.newBuilder()
                .setAppId(ContextUtils.getBusinessEvnEnum().getAppId())
                .setIdCardNumber(idCardNumber)
                .build();
        Result<UserVerifyProto.ResponseGetUnionPassUserId> unionPassUserId = userVerifyService.getUnionPassUserId(unionParam);
        log.info("getUserAuthUnionIdList idCardNumber={},getUnionPassUserIdResult:{}", idCardNumber, unionPassUserId.rCode());
        if(unionPassUserId.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            return Collections.emptyList();
        }
        return new ArrayList<>(unionPassUserId.target().getUserIdList());
    }

    @Override
    public Optional<SimpleUserDto> getUserByKeyWord(String keyword) {
        // 判断keyword是否是数字，如果是，将keywrod分别赋予userId和userName两个参数，如果keyword是字符串，将keyword赋予userName参数
        if (StringUtils.isNumeric(keyword)) {
            return iUserRemote.getUserByKeyWord(Long.parseLong(keyword), keyword);
        }
        return iUserRemote.getUserByKeyWord(0L, keyword);
    }
    
    

}
