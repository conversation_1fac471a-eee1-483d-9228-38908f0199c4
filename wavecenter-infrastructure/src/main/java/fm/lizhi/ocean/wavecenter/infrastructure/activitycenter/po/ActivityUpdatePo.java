package fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.po;

import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.datastore.entity.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

import org.apache.commons.lang3.tuple.Pair;

@Data
@Accessors(chain = true)
public class ActivityUpdatePo {
    /**
     * 申请信息
     */
    private ActivityApplyInfo applyInfo;

    /**
     * 旧的申请信息
     */
    private ActivityApplyInfo oldApplyInfo;
    
    /**
     * 流程资源列表
     */
    private List<ActivityApplyFlowResource> flowResources;
    
    /**
     * 流程列表
     */
    private List<ActivityApplyProcess> processList;
    
    /**
     * 座位时间列表
     */
    private List<ActivityOfficialSeatTime> seatTimeList;
    
    /**
     * 旧的座位时间列表
     */
    private List<ActivityOfficialSeatTime> oldSeatTimeList;
    
    /**
     * 最大座位数
     */
    private Integer maxSeatCount;
    
    /**
     * 模板使用关系
     */
    private ActivityTemplateUsedRelation relation;


    /**
     * 装扮发放记录
     */
    private Pair<List<ActivityResourceGiveRecord>, List<ActivityDressUpGiveRecord>> dressRecordPair;

    /**
     * 旧的装扮发放主播ID列表
     */
    private List<Long> oldNjIdList;

    /**
     * 装扮发放ID
     */
    private List<Long> giveIds;

    /**
     * 流量资源发放ID
     */
    private List<Long> flowResourceGiveIds;

    /**
     * 是否有旧的流量资源
     */
    private boolean hasOldFlowResource;

    /**
     * 装扮列表
     */
    private List<ActivityApplyDecorate> decorates;
}
