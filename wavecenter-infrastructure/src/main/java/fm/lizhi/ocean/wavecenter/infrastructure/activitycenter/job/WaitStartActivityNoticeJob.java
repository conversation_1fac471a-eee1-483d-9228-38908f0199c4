package fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.job;

import fm.lizhi.common.job.dispatcher.executor.handler.JobExecuteContext;
import fm.lizhi.common.job.dispatcher.executor.handler.JobHandler;
import fm.lizhi.ocean.wavecenter.service.activitycenter.manager.ActivityChatManager;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


@Component
public class WaitStartActivityNoticeJob implements JobHandler{

    @Autowired
    private ActivityChatManager activityChatManager;


    @Override
    public void execute(JobExecuteContext arg0) throws Exception {
        activityChatManager.startActivityNotice();
    }

}
