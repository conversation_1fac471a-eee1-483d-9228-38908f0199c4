package fm.lizhi.ocean.wavecenter.infrastructure.user.manager;

import fm.lizhi.accountcenter.api.AccountAuthService;
import fm.lizhi.accountcenter.protocol.AccountAuthProto;
import fm.lizhi.accountcenter.protocol.CommonProto;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.user.export.api.model.UserInfo;
import fm.lizhi.ocean.wave.user.export.api.result.GetByAuthAccountIdResult;
import fm.lizhi.ocean.wave.user.export.api.service.UserService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.infrastructure.user.convert.UserConvert;
import fm.lizhi.ocean.wavecenter.service.user.dto.AccountCodeAuthResultDto;
import fm.lizhi.ocean.wavecenter.service.user.dto.UserInfoDto;
import fm.lizhi.ocean.wavecenter.service.user.manager.AccountAuthManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/4/12 19:55
 */
@Slf4j
@Component
public class AccountAuthManagerImpl implements AccountAuthManager {

    @Autowired
    private AccountAuthService accountAuthService;
    @Autowired
    private UserService userService;

    @Override
    public Optional<AccountCodeAuthResultDto> codeConvertAccount(String authCode, int appId) {
        AccountAuthProto.RequestCodeConvertAccount request = AccountAuthProto.RequestCodeConvertAccount.newBuilder()
                .setHeader(CommonProto.Header.newBuilder()
                        .setAppCode(String.valueOf(appId)))
                .setCode(authCode)
                .build();
        Result<AccountAuthProto.ResponseCodeConvertAccount> result = accountAuthService.codeConvertAccount(request);
        if (RpcResult.isFail(result)) {
            log.warn("exchange fail, rCode={}, authCode={}, ", result.rCode(), authCode);
            return Optional.empty();
        }
        long accountId = result.target().getAccountId();
        LogContext.addReqLog("accountId={}", accountId);
        return Optional.of(new AccountCodeAuthResultDto().setAccountId(accountId));
    }

    @Override
    public Optional<UserInfoDto> getUserByAuthAccountId(Long authAccountId) {
        Result<GetByAuthAccountIdResult> result = userService.getByAuthAccountId(authAccountId);
        if (RpcResult.isFail(result)) {
            log.error("exchange fail, rCode={}, authAccountId={}", result.rCode(), authAccountId);
            return Optional.empty();
        }
        UserInfo userInfo = result.target().getUserInfo();
        return Optional.ofNullable(UserConvert.I.waveUserPojo2UserInfoDto(userInfo));
    }
}
