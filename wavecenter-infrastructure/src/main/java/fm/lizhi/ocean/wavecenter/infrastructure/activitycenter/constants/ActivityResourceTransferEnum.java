package fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.constants;

import lombok.Getter;

/**
 * 资源转存类型枚举
 * <AUTHOR>
 */
@Getter
public enum ActivityResourceTransferEnum {

    /**
     * 房间公告图片资源
     */
    ROOM_ANNOUNCEMENT(1),

    /**
     * 流量资源
     */
    APPLY_FLOW_RESOURCE(2),


    ;

    ActivityResourceTransferEnum(Integer type) {
        this.type = type;
    }

    private final Integer type;


    public static Boolean support(Integer type) {
        for (ActivityResourceTransferEnum item : ActivityResourceTransferEnum.values()) {
            if (item.getType().equals(type)) {
                return true;
            }
        }
        return false;
    }
}
