package fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.convert;

import fm.lizhi.ocean.wavecenter.infrastructure.activitycenter.datastore.entity.ActivityResourceTransfer;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.ActivityResourceTransferResultDTO;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ActivityResourceTransferConvert {

    ActivityResourceTransferConvert I = Mappers.getMapper(ActivityResourceTransferConvert.class);


    ActivityResourceTransferResultDTO convert(ActivityResourceTransfer activityResourceTransfer);
}
