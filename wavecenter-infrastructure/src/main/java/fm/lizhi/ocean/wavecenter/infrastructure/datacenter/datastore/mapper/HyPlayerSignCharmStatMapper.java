package fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.mapper;

import fm.lizhi.common.datastore.core.annotation.DataStore;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.entity.PlayerSignCharmSumPo;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.po.CharmStatPo;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.po.PlayerSignPerformancePo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@DataStore(namespace = "mysql_heiye_lzppfamily_r")
public interface HyPlayerSignCharmStatMapper {

    @Select({
            "<script>"
                    + "select s.user_id , s.nj_id, sum(s.value) value "
                    + "from player_sign_charm_stat s "
                    + "where s.stat_day=#{date} "
                    + "<if test='userIds != null and userIds.size() > 0'>"
                    + "and user_id in "
                    +   "<foreach item='userId' collection='userIds' open='(' separator=',' close=')'>"
                    +       "#{userId}"
                    +   "</foreach>"
                    + "</if>"
                    + "<if test='njIds != null and njIds.size() > 0'>"
                    + "and nj_id in "
                    +   "<foreach item='njId' collection='njIds' open='(' separator=',' close=')'>"
                    +       "#{njId}"
                    +   "</foreach>"
                    + "</if>"
                    + "group by s.user_id, s.nj_id"
                    + "</script>"
    })
    List<CharmStatPo> getGuildPlayerCharmInfo(@Param("date") String date, @Param("userIds") List<Long> userIds, @Param("njIds") List<Long> njIds);

    @Select({
            "<script>"
                    + "select distinct user_id , value "
                    + "from player_sign_charm_stat s "
                    + "where s.stat_day=#{date} and nj_id = #{njId}"
                    + "<if test='userIds != null and userIds.size() > 0'>"
                    + "and user_id in "
                    +   "<foreach item='userId' collection='userIds' open='(' separator=',' close=')'>"
                    +       "#{userId}"
                    +   "</foreach>"
                    + "</if>"
                    + "group by s.user_id, s.nj_id"
                    + "</script>"
    })
    List<CharmStatPo> roomPlayerCharmInfo(@Param("njId") long njId, @Param("date") String date, @Param("userIds") List<Long> userIds, @Param("orderType") String orderType);

    @Select({
            "<script>"
            , "select s.user_id, sum(s.value) totalValue"
            , "from player_sign_charm_stat s"
            , "where s.nj_id = #{njId}"
            , "and s.stat_day &gt;= #{startDate}"
            , "and s.stat_day &lt;= #{endDate}"
            , "group by s.user_id "
            , "having totalValue != 0"
            ,"</script>"
    })
    List<PlayerSignPerformancePo> selectPlayerSignCharmSum(@Param("njId") long njId
            , @Param("startDate") String startDate
            , @Param("endDate") String endDate);

    @Select({
            "<script>"
            , "select s.user_id , s.nj_id, sum(s.value) value "
            , "from player_sign_charm_stat s "
            , "where s.stat_day&gt;=#{startDate} and s.stat_day&lt;=#{endDate} "
            , "<if test='roomIds != null and roomIds.size() > 0'>"
            , "and s.nj_id in "
            ,   "<foreach item='roomId' collection='roomIds' open='(' separator=',' close=')'>"
            ,       "#{roomId}"
            ,   "</foreach>"
            , "</if>"
            , "group by s.user_id, s.nj_id"
            , "</script>"
    })
    List<CharmStatPo> getRoomPlayerCharmByRooms(@Param("roomIds") List<Long> roomIds, @Param("startDate") String startDate, @Param("endDate") String endDate);

    @Select({
            "<script>"
            , "select s.user_id, sum(s.value) totalValue"
            , "from player_sign_charm_stat s"
            , "where s.nj_id = #{njId} "
            , "<if test='null != userIds and userIds.size > 0'>"
            , "and s.user_id in "
            , "<foreach collection='userIds' item='uId' open='(' separator=',' close=')'>"
            , "#{uId}"
            , "</foreach>"
            , "</if>"
            , "and s.stat_day &gt;= #{startDate}"
            , "and s.stat_day &lt;= #{endDate}"
            , "group by s.user_id"
            ,"</script>"
    })
    List<PlayerSignCharmSumPo> selectPlayerSignCharmSumByUsers(@Param("njId") long njId
            , @Param("userIds") List<Long> userIds
            , @Param("startDate") String startDate
            , @Param("endDate") String endDate);

}
