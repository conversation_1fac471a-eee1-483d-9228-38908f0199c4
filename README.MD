# 1. 配置&环境
1.1 配置中心地址(测试环境)

http://configportalinoffice.lizhi.fm/config.html?#/appid=lz_ocean_wavecenter

1.2 本地开发启动类需要增加的JVM参数（灯塔和发布平台不需要）:

`-Dmetadata.region=cn -Dmetadata.business.env=default -Dmetadata.deploy.env=test -Dmetadata.service.name=lz_ocean_wavecenter`

1.3 需要获取环境参数请使用以下方法获取:

- 获取运行时的区域变量： `com.lizhi.commons.config.core.util.ConfigUtils.ConfigUtils.getRegion()`
- 获取运行时的业务环境变量： `com.lizhi.commons.config.core.util.ConfigUtils.ConfigUtils.getBusinessEnv()`
- 获取运行时的部署环境变量： `com.lizhi.commons.config.core.util.ConfigUtils.ConfigUtils.getEnv()`
- 获取运行时的服务名称变量：  `com.lizhi.commons.config.core.util.ConfigUtils.ConfigUtils.getServiceName()`

# 2. 项目说明：
> 飞书文档：https://lizhi2021.feishu.cn/wiki/GhcswwHahiC13rkeRJOcV32Snwd
- 类命名约定
  - 接口实现类命名：接口名+Impl
  - 其他类名：业务含义+包名后缀
- 模块说明

# 3. Domain-op
Domaim=30001
> 原则：如果DC接口属于高频场景，使用Domain-op定义

# 4. 统一改造工程 版本
  mvn versions:set -DnewVersion=1.0.9  -DgenerateBackupPoms=false
